# Troubleshooting - High Agents Service Ingress

## 🚨 Erro: "secrets 'high-agents-service-tls-secret' not found"

Este erro indica que há uma referência a um secret TLS que não existe. Isso pode acontecer devido a configurações anteriores.

### 🔧 Solução Rápida

Execute os comandos abaixo para limpar e reaplicar a configuração:

```bash
# 1. Navegar para o diretório do serviço
cd high-agents-service

# 2. Executar script de limpeza
./k8s/cleanup-and-redeploy.sh
```

### 🔧 Solução Manual

Se o script não funcionar, execute os comandos manualmente:

```bash
# 1. Remover recursos antigos
kubectl delete ingress high-agents-service-ingress --ignore-not-found=true
kubectl delete managedcertificate high-agents-service-ssl-cert --ignore-not-found=true
kubectl delete secret high-agents-service-tls-secret --ignore-not-found=true

# 2. Aguardar limpeza
sleep 10

# 3. Reaplicar apenas o Ingress e ManagedCertificate
kubectl apply -f k8s/ingress.yml

# 4. Verificar status
kubectl get ingress high-agents-service-ingress
kubectl get managedcertificate high-agents-service-ssl-cert
```

### 🔍 Verificação de Status

```bash
# Verificar se o Ingress foi criado corretamente
kubectl describe ingress high-agents-service-ingress

# Verificar status do ManagedCertificate
kubectl describe managedcertificate high-agents-service-ssl-cert

# Verificar se há secrets órfãos
kubectl get secrets | grep high-agents

# Verificar logs do ingress controller
kubectl logs -n kube-system -l k8s-app=glbc
```

### 📋 Status Esperado

**ManagedCertificate**:
- Status deve mostrar "Provisioning" inicialmente
- Depois de alguns minutos deve ficar "Active"

**Ingress**:
- Deve mostrar o IP estático: `*************`
- Não deve ter erros relacionados a secrets

### ⚠️ Notas Importantes

1. **Sem TLS Section**: O Ingress não deve ter seção `tls:` quando usando ManagedCertificate
2. **Sem Secrets**: Não criamos secrets TLS manualmente com ManagedCertificate
3. **DNS**: Certifique-se de que `api.highagents-dev.highcapital.io` aponta para `*************`

### 🚀 Teste Final

Após aplicar as correções:

```bash
# Testar conectividade
curl -I https://api.highagents-dev.highcapital.io

# Verificar certificado
openssl s_client -connect api.highagents-dev.highcapital.io:443 -servername api.highagents-dev.highcapital.io
```

### 🆘 Se o Problema Persistir

1. Verificar se o cluster tem permissões para criar ManagedCertificates
2. Verificar se o IP estático foi criado corretamente
3. Verificar se o DNS está propagado
4. Considerar recriar o IP estático se necessário

```bash
# Recriar IP estático se necessário
gcloud compute addresses delete high-agents-service-ip --global
gcloud compute addresses create high-agents-service-ip --global
```

# 🚨 Correção Rápida - Ingress e Certificado

## Problema Identificado
- Certificado em "Provisioning" 
- IP não está sendo associado
- Ingress não encontra o certificado

## 🔧 Solução Passo a Passo

### 1. Execute o script de correção automática:
```bash
cd high-agents-service
./k8s/fix-ingress.sh
```

### 2. OU execute manualmente:

```bash
# Verificar se IP estático existe
gcloud compute addresses describe high-agents-service-ip --global

# Se não existir, criar:
gcloud compute addresses create high-agents-service-ip --global

# Obter o IP
IP=$(gcloud compute addresses describe high-agents-service-ip --global --format="value(address)")
echo "IP: $IP"

# Verificar DNS
nslookup api.highagents-dev.highcapital.io

# Limpar recursos antigos
kubectl delete ingress high-agents-service-ingress --ignore-not-found=true
kubectl delete managedcertificate high-agents-service-ssl-cert --ignore-not-found=true

# Aguardar
sleep 15

# Aplicar configuração
kubectl apply -f k8s/ingress.yml
```

### 3. Verificar se DNS está correto:
O domínio `api.highagents-dev.highcapital.io` DEVE apontar para o IP do Load Balancer.

```bash
# Verificar IP atual do DNS
nslookup api.highagents-dev.highcapital.io

# Verificar IP do Ingress
kubectl get ingress high-agents-service-ingress -o jsonpath='{.status.loadBalancer.ingress[0].ip}'
```

### 4. Se ainda não funcionar, usar versão alternativa:
```bash
kubectl apply -f k8s/ingress-alternative.yml
```

## 📋 Checklist de Verificação

- [ ] IP estático criado no GCP
- [ ] DNS aponta para o IP correto
- [ ] Service está funcionando (ClusterIP)
- [ ] Pod está rodando e saudável
- [ ] Ingress foi aplicado sem erros
- [ ] ManagedCertificate foi criado

## 🔍 Comandos de Debug

```bash
# Status do certificado
kubectl describe managedcertificate high-agents-service-ssl-cert

# Status do Ingress
kubectl describe ingress high-agents-service-ingress

# Logs do pod
kubectl logs -l app=high-agents-service

# Testar service interno
kubectl port-forward svc/high-agents-service 8080:80
# Em outro terminal: curl http://localhost:8080
```

## ⏰ Tempo Esperado

- **Ingress**: 2-5 minutos para ficar ativo
- **Certificado**: 5-15 minutos para provisionar
- **DNS**: Até 48h para propagação completa

## 🆘 Se Nada Funcionar

1. Verificar se o cluster tem as permissões necessárias
2. Verificar se o domínio é válido e acessível
3. Considerar usar um subdomínio diferente para teste
4. Verificar logs do ingress controller:
   ```bash
   kubectl logs -n kube-system -l k8s-app=glbc
   ```

## ✅ Teste Final

Quando tudo estiver funcionando:
```bash
curl -I https://api.highagents-dev.highcapital.io
```

Deve retornar status 200, 404 ou 302 (dependendo da aplicação).

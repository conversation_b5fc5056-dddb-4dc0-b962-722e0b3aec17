name: Test Report

on:
  pull_request:
    branches:
      - main
      - release
  push:
    branches:
      - main
      - release
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  test-detailed:
    name: Detailed Test Report
    runs-on: ubuntu-latest
    
    permissions:
      contents: read

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore
      env:
        NUGET_USERNAME: ${{ secrets.NUGET_USERNAME }}
        NUGET_TOKEN: ${{ secrets.NUGET_TOKEN }}

    - name: Build solution
      run: dotnet build --configuration Release --no-restore

    - name: Run all tests with detailed output
      continue-on-error: true
      run: |
        echo "## 🧪 Test Execution Report" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Run Domain tests
        echo "### Domain Tests" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if dotnet test tests/Domain.UnitTests/Domain.UnitTests.csproj \
          --configuration Release \
          --no-build \
          --verbosity normal \
          --logger "console;verbosity=detailed" \
          --logger "trx;LogFileName=domain-tests.trx" \
          --results-directory TestResults/ 2>&1 | tee domain-test-output.txt; then
          echo "✅ **Domain Tests**: PASSED" >> $GITHUB_STEP_SUMMARY
          DOMAIN_STATUS="PASSED"
        else
          echo "❌ **Domain Tests**: FAILED" >> $GITHUB_STEP_SUMMARY
          DOMAIN_STATUS="FAILED"
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Application Tests" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Run Application tests
        if dotnet test tests/Application.UnitTests/Application.UnitTests.csproj \
          --configuration Release \
          --no-build \
          --verbosity normal \
          --logger "console;verbosity=detailed" \
          --logger "trx;LogFileName=app-tests.trx" \
          --results-directory TestResults/ 2>&1 | tee app-test-output.txt; then
          echo "✅ **Application Tests**: PASSED" >> $GITHUB_STEP_SUMMARY
          APP_STATUS="PASSED"
        else
          echo "❌ **Application Tests**: FAILED" >> $GITHUB_STEP_SUMMARY
          APP_STATUS="FAILED"
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Extract test counts from output
        DOMAIN_TOTAL=$(grep -o "Total tests: [0-9]*" domain-test-output.txt | grep -o "[0-9]*" || echo "0")
        DOMAIN_PASSED=$(grep -o "Passed: [0-9]*" domain-test-output.txt | grep -o "[0-9]*" || echo "0")
        DOMAIN_FAILED=$(grep -o "Failed: [0-9]*" domain-test-output.txt | grep -o "[0-9]*" || echo "0")

        APP_TOTAL=$(grep -o "Total tests: [0-9]*" app-test-output.txt | grep -o "[0-9]*" || echo "0")
        APP_PASSED=$(grep -o "Passed: [0-9]*" app-test-output.txt | grep -o "[0-9]*" || echo "0")
        APP_FAILED=$(grep -o "Failed: [0-9]*" app-test-output.txt | grep -o "[0-9]*" || echo "0")

        # Calculate totals
        TOTAL_TESTS=$((DOMAIN_TOTAL + APP_TOTAL))
        TOTAL_PASSED=$((DOMAIN_PASSED + APP_PASSED))
        TOTAL_FAILED=$((DOMAIN_FAILED + APP_FAILED))

        echo "| Test Suite | Total | Passed | Failed | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|------------|-------|--------|--------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Domain Tests | $DOMAIN_TOTAL | $DOMAIN_PASSED | $DOMAIN_FAILED | $DOMAIN_STATUS |" >> $GITHUB_STEP_SUMMARY
        echo "| Application Tests | $APP_TOTAL | $APP_PASSED | $APP_FAILED | $APP_STATUS |" >> $GITHUB_STEP_SUMMARY
        echo "| **TOTAL** | **$TOTAL_TESTS** | **$TOTAL_PASSED** | **$TOTAL_FAILED** | - |" >> $GITHUB_STEP_SUMMARY
        
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ $TOTAL_FAILED -eq 0 ]; then
          echo "🎉 **All tests passed!** ($TOTAL_PASSED/$TOTAL_TESTS)" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **$TOTAL_FAILED test(s) failed** out of $TOTAL_TESTS total tests" >> $GITHUB_STEP_SUMMARY
          
          # Show failed test details if available
          if [ $DOMAIN_FAILED -gt 0 ]; then
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Failed Domain Tests:**" >> $GITHUB_STEP_SUMMARY
            grep -A 5 -B 5 "FAIL\|Failed" domain-test-output.txt | head -20 >> $GITHUB_STEP_SUMMARY || true
          fi

          if [ $APP_FAILED -gt 0 ]; then
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Failed Application Tests:**" >> $GITHUB_STEP_SUMMARY
            grep -A 5 -B 5 "FAIL\|Failed" app-test-output.txt | head -20 >> $GITHUB_STEP_SUMMARY || true
          fi
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Test Results Location**: \`TestResults/\`" >> $GITHUB_STEP_SUMMARY
        echo "**Commit**: \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY

    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-results
        path: TestResults/
        retention-days: 30

    - name: Generate test badge data
      if: always()
      run: |
        # Create a simple badge data file for external use
        mkdir -p badges

        DOMAIN_TOTAL=$(grep -o "Total tests: [0-9]*" domain-test-output.txt | grep -o "[0-9]*" || echo "0")
        DOMAIN_PASSED=$(grep -o "Passed: [0-9]*" domain-test-output.txt | grep -o "[0-9]*" || echo "0")
        APP_TOTAL=$(grep -o "Total tests: [0-9]*" app-test-output.txt | grep -o "[0-9]*" || echo "0")
        APP_PASSED=$(grep -o "Passed: [0-9]*" app-test-output.txt | grep -o "[0-9]*" || echo "0")

        TOTAL_TESTS=$((DOMAIN_TOTAL + APP_TOTAL))
        TOTAL_PASSED=$((DOMAIN_PASSED + APP_PASSED))
        
        if [ $TOTAL_TESTS -eq 0 ]; then
          echo "no tests" > badges/tests.txt
        elif [ $TOTAL_PASSED -eq $TOTAL_TESTS ]; then
          echo "$TOTAL_PASSED/$TOTAL_TESTS passing" > badges/tests.txt
        else
          TOTAL_FAILED=$((TOTAL_TESTS - TOTAL_PASSED))
          echo "$TOTAL_FAILED/$TOTAL_TESTS failing" > badges/tests.txt
        fi

    - name: Upload badge data
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-badge-data
        path: badges/
        retention-days: 1

﻿# HighCapitalTemplate API

Uma API REST construída com Clean Architecture utilizando .NET 9.0, Entity Framework e SQLite.



## 🚀 Como executar

### Método principal: Docker Compose

O modo recomendado de executar a aplicação é utilizando o Docker Compose. Isso irá construir a solução, aplicar as migrations e iniciar todos os serviços necessários automaticamente:

```bash
docker-compose up --build
```

Acesse a aplicação em:
- **Swagger UI**: http://localhost:5000/swagger
- **Health Check**: http://localhost:5000/health
- **API Base**: http://localhost:5000

### Execução direta (apenas para desenvolvimento)

Se preferir rodar o projeto diretamente (sem Docker), o banco de dados utilizado será o SQLite. O arquivo `highcapitaltemplate.db` será criado automaticamente na pasta `src/Web`.

#### Passos
1. **Clone e navegue até o diretório:**
   ```bash
   cd D:\Projetos\TesteCleanArch
   ```
2. **Compile o projeto:**
   ```bash
   dotnet build HighCapitalTemplate.sln
   ```
3. **Execute a aplicação:**
   ```bash
   dotnet run --project src/Web/Web.csproj --urls="http://localhost:5001"
   ```
4. **Acesse a aplicação:**
   - **Swagger UI**: http://localhost:5001/swagger
   - **Health Check**: http://localhost:5001/health
   - **API Base**: http://localhost:5001

## 📋 Funcionalidades

- **Authentication API**: Endpoints para registro e login (`/api/Auth/register`, `/api/Auth/login`)
- **JWT Token**: Autenticação baseada em tokens JWT
- **SQLite Database**: Banco de dados local para desenvolvimento
- **Swagger Documentation**: Interface interativa para testar a API
- **Health Checks**: Monitoramento da aplicação e banco de dados

## 🏗️ Arquitetura

O projeto segue os princípios de Clean Architecture:

- **Domain**: Entidades, enums, exceções e objetos de valor
- **Application**: Casos de uso, interfaces e lógica de negócio
- **Infrastructure**: Acesso a dados, serviços externos e implementações
- **Web**: Controllers, endpoints e configuração da API


## 🗄️ Banco de Dados

- **Docker Compose:** Utiliza PostgreSQL como banco de dados principal (veja o arquivo `docker-compose.yml`).
- **Execução direta:** Utiliza SQLite apenas para desenvolvimento local. O arquivo `highcapitaltemplate.db` é criado automaticamente em `src/Web`.

---

**Desenvolvido com Clean Architecture e .NET 9.0**
# Ingress e HTTPS Setup - High Agents Service

Este documento descreve como configurar o Ingress com HTTPS para o serviço high-agents-service.

## 📋 Pré-requisitos

### 1. Criar IP Estático Global

```bash
# Criar IP estático global no Google Cloud
gcloud compute addresses create high-agents-service-ip --global

# Verificar o IP criado
gcloud compute addresses describe high-agents-service-ip --global
```

### 2. Configurar DNS

Após criar o IP estático, configure o DNS para apontar o domínio para o IP:

- **Domínio**: `api.highagents-dev.highcapital.io`
- **Tipo**: A Record
- **Valor**: *************

## 🚀 Deploy

O deploy já foi configurado no workflow. Os recursos incluem:

- **Service**: ClusterIP (interno)
- **Ingress**: Com ManagedCertificate do GKE
- **ManagedCertificate**: Certificado SSL automático do Google

## 🔍 Verificação

### Verificar recursos criados:

```bash
# Verificar Ingress
kubectl get ingress high-agents-service-ingress

# Verificar ManagedCertificate
kubectl get managedcertificate high-agents-service-ssl-cert

# Verificar IP do Ingress
kubectl get ingress high-agents-service-ingress -o jsonpath='{.status.loadBalancer.ingress[0].ip}'

# Verificar status do certificado
kubectl describe managedcertificate high-agents-service-ssl-cert
```

### Verificar conectividade:

```bash
# Testar HTTP (deve redirecionar para HTTPS)
curl -I http://api.highagents-dev.highcapital.io

# Testar HTTPS
curl -I https://api.highagents-dev.highcapital.io
```

## ⚠️ Notas Importantes

1. **Certificado SSL**: O ManagedCertificate pode levar alguns minutos para ser provisionado
2. **DNS**: Certifique-se de que o DNS está apontando para o IP correto
3. **Propagação**: Pode levar até 48h para propagação completa do DNS
4. **Status**: Monitore o status do ManagedCertificate até ficar "Active"

## 🔧 Troubleshooting

### Certificado não provisiona:
- Verificar se o DNS está correto
- Verificar se o domínio é acessível publicamente
- Aguardar propagação do DNS

### Ingress não recebe tráfego:
- Verificar se o Service está funcionando
- Verificar se o IP estático foi criado
- Verificar configuração do DNS

### Logs úteis:
```bash
# Logs do pod
kubectl logs -l app=high-agents-service

# Eventos do Ingress
kubectl describe ingress high-agents-service-ingress

# Status do ManagedCertificate
kubectl describe managedcertificate high-agents-service-ssl-cert
```

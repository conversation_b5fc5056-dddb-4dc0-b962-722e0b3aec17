using System.Reflection;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Infrastructure.Repository;
using HighAgentsApi.Service.Services;
using HighAgentsApi.Application.Services;
using Microsoft.Extensions.Hosting;


namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddApplicationServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddAutoMapper(Assembly.GetExecutingAssembly());
        builder.Services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        builder.Services.AddScoped<IAgentService, AgentService>();
        builder.Services.AddScoped<IAgentRepository, AgentRepository>();
        builder.Services.AddScoped<IAgentParamsService, AgentParamsService>();
        builder.Services.AddScoped<IAgentParamsRepository, AgentParamsRepository>();
        builder.Services.AddScoped<IMessageService, MessageService>();
        builder.Services.AddScoped<IMessageRepository, MessageRepository>();
        builder.Services.AddScoped<PromptContextService>();
    }
}

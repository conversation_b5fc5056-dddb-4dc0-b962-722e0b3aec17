using System.Reflection;
using Api.Infrastructure.OpenIa;
using HighAgentsApi.Application.Services;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Infrastructure.Repository;
using HighAgentsApi.Service.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddApplicationServices(this IHostApplicationBuilder builder)
    {
        var openAiSection = builder.Configuration.GetSection("OpenAi");
        var apiKey = openAiSection.GetValue<string>("ApiKey");
        var model = openAiSection.GetValue<string>("Model") ?? "gpt-3.5-turbo";

        if (string.IsNullOrEmpty(apiKey))
        {
            throw new InvalidOperationException("(OpenAI:ApiKey) not found in configuration.");
        }

        builder.Services.AddSingleton<OpenAIService>(sp => new OpenAIService(apiKey, model));

        builder.Services.AddAutoMapper(Assembly.GetExecutingAssembly());
        builder.Services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        builder.Services.AddScoped<IAgentService, AgentService>();
        builder.Services.AddScoped<IAgentRepository, AgentRepository>();
        builder.Services.AddScoped<IAgentParamsService, AgentParamsService>();
        builder.Services.AddScoped<IAgentParamsRepository, AgentParamsRepository>();
        builder.Services.AddScoped<ITypeService, TypeService>();
        builder.Services.AddScoped<IAgentTypeRepository, TypeRepository>();
        builder.Services.AddScoped<IBrainService, BrainService>();
        builder.Services.AddScoped<IBrainRepository, BrainRepository>();
        builder.Services.AddScoped<IMessageService, MessageService>();
        builder.Services.AddScoped<IMessageRepository, MessageRepository>();
        builder.Services.AddScoped<IWhatsAppService, WhatsAppService>();
        builder.Services.AddScoped<IEvolutionApiService, EvolutionApiService>();
        builder.Services.AddScoped<MessageProcessorService>();
        builder.Services.AddHostedService<WhatsAppBackgroundService>();
    }
}

using System.Text;
using System.Text.Json;
using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;


namespace HighAgentsApi.Service.Services
{
    public class AgentService : IAgentService
    {
        private readonly IAgentRepository _agentRepository;

        private readonly IAgentParamsService _agentParamService;
        private readonly IBrainService _brainService;
        private readonly ITypeService _typeService;
        private readonly ILogger<AgentService> _logger;

        private readonly IConfiguration _configuration;

        private readonly HttpClient _httpClient;

        public AgentService(
            IAgentRepository agentRepository,
            ILogger<AgentService> logger,
            IAgentParamsService agentParamService,
            IBrainService brainService,
            ITypeService typeService,
            IConfiguration configuration,

            HttpClient httpClient
            )
        {
            _agentRepository = agentRepository;
            _logger = logger;
            _agentParamService = agentParamService;
            _brainService = brainService;
            _typeService = typeService;
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public async Task<Result<AgentResponseDto>> Create(AgentDto data)
        {
            try
            {
                // Verificar se já existe um agente com o mesmo nome para o usuário
                var existingAgent = await _agentRepository.GetAgentByNameAsync(data.Name, data.userId);
                if (existingAgent != null)
                {
                    _logger.LogWarning("Tentativa de criar agente com nome duplicado: {Name} para o usuário: {UserId}",
                        data.Name, data.userId);
                    return Result<AgentResponseDto>.Failure(new[] { "Já existe um agente com este nome para o usuário." });
                }

                var agent = new Agent
                {
                    Context = data.Context,
                    UserId = data.userId,
                    Name = data.Name,
                    AgentParamsId = data.agentParamsId,
                    AgentTypeId = data.AgentTypeId,
                    AgentBrainId = data.AgentBrainId
                };

                await _agentRepository.AddAsync(agent);

                _logger.LogInformation("Agent criado com sucesso. Agent ID: {AgentId}", agent.Id);



                var dataCreateInstance = new CreateInstanceRequestDto
                {
                    InstanceName = agent.Name,
                    AgentId = agent.Id,
                    Qrcode = true,
                    Integration = "WHATSAPP-BAILEYS"
                };


                _logger.LogInformation("Criando instância do WhatsApp: {InstanceName}", dataCreateInstance.InstanceName);

                var baseUrl = _configuration["WhatsApp:HighAgentsMessageService"];
                var json = JsonSerializer.Serialize(dataCreateInstance);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{baseUrl}/api/WhatsApp/create-instance", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Resposta do serviço externo - Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, responseContent);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Instância do WhatsApp criada com sucesso: {InstanceName}", dataCreateInstance.InstanceName);

                    // Retornar o JSON direto da resposta
                    var result = new WhatsAppResponseDto
                    {
                        Success = true,
                        Data = string.IsNullOrWhiteSpace(responseContent) ? null : JsonSerializer.Deserialize<object>(responseContent),
                        Message = "Instância criada com sucesso"
                    };

                    var agentDto = new AgentUpdateDto
                    {
                        InstanceWhatsappName = dataCreateInstance.InstanceName
                    };

                    await Update(agentDto, dataCreateInstance.AgentId);

                    //  return Result<WhatsAppResponseDto>.Success(result);

                }

                // Criar AgentResponseDto com os relacionamentos carregados
                var agentResponseDto = new AgentResponseDto
                {
                    Id = agent.Id,
                    Name = agent.Name,
                    Context = agent.Context,
                    UserId = agent.UserId,
                    AgentParamsId = agent.AgentParamsId,
                    AgentTypeId = agent.AgentTypeId,
                    AgentBrainId = agent.AgentBrainId,
                    InstanceWhatsappName = agent.InstanceWhatsappName
                };

                // Buscar o paramName se AgentParamsId existir
                if (agent.AgentParamsId.HasValue)
                {
                    var agentParamsResult = await _agentParamService.GetOneById(agent.AgentParamsId.Value);
                    if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                    {
                        agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                    }
                }

                // Buscar o brainName se AgentBrainId existir
                if (agent.AgentBrainId.HasValue)
                {
                    var brainResult = await _brainService.GetOneById(agent.AgentBrainId.Value);
                    if (brainResult.Succeeded && brainResult.Value != null)
                    {
                        agentResponseDto.BrainName = brainResult.Value.BrainName;
                    }
                }

                // Buscar o typeName se AgentTypeId existir
                if (agent.AgentTypeId.HasValue)
                {
                    var typeResult = await _typeService.GetOneById(agent.AgentTypeId.Value);
                    if (typeResult.Succeeded && typeResult.Value != null)
                    {
                        agentResponseDto.TypeName = typeResult.Value.Name;
                    }
                }

                return Result<AgentResponseDto>.Success(agentResponseDto);

            }


            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar o agent para o usuário: {UserId}", data.userId);
                return Result<AgentResponseDto>.Failure(new[] { "Erro ao criar o agent." });
            }
        }

        public async Task<Result<Agent>> Delete(int agentId, int? deletedBy = null)
        {
            try
            {
                _logger.LogInformation("Tentando deletar o agent com ID: {AgentId}", agentId);

                var agent = await _agentRepository.GetByIdAsync(agentId);
                if (agent == null)
                {
                    return Result<Agent>.Failure(new[] { "Agent não encontrado." });
                }

                var result = await _agentRepository.DeleteAsync(agent, deletedBy);
                _logger.LogInformation("Agent deletado com sucesso. Agent ID: {AgentId}", agentId);

                return Result<Agent>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar o agent com ID: {AgentId}", agentId);
                return Result<Agent>.Failure(new[] { "Erro ao deletar." });
            }
        }

        public async Task<Result<IEnumerable<AgentResponseDto>>> GetAll(int userId)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar todos os agents para o usuário:{userId}", userId);

                var agents = await _agentRepository.GetAgentByUserIdAsync(userId);

                var agentResponseDtos = new List<AgentResponseDto>();

                foreach (var agent in agents)
                {
                    var agentResponseDto = new AgentResponseDto
                    {
                        Id = agent.Id,
                        Name = agent.Name,
                        Context = agent.Context,
                        UserId = agent.UserId,
                        AgentParamsId = agent.AgentParamsId,
                        AgentTypeId = agent.AgentTypeId,
                        AgentBrainId = agent.AgentBrainId,
                        InstanceWhatsappName = agent.InstanceWhatsappName
                    };

                    // Buscar o paramName se AgentParamsId existir
                    if (agent.AgentParamsId.HasValue)
                    {
                        var agentParamsResult = await _agentParamService.GetOneById(agent.AgentParamsId.Value);
                        if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                        {
                            agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                        }
                    }

                    // Buscar o brainName se AgentBrainId existir
                    if (agent.AgentBrainId.HasValue)
                    {
                        var brainResult = await _brainService.GetOneById(agent.AgentBrainId.Value);
                        if (brainResult.Succeeded && brainResult.Value != null)
                        {
                            agentResponseDto.BrainName = brainResult.Value.BrainName;
                        }
                    }

                    // Buscar o typeName se AgentTypeId existir
                    if (agent.AgentTypeId.HasValue)
                    {
                        var typeResult = await _typeService.GetOneById(agent.AgentTypeId.Value);
                        if (typeResult.Succeeded && typeResult.Value != null)
                        {
                            agentResponseDto.TypeName = typeResult.Value.Name;
                        }
                    }

                    agentResponseDtos.Add(agentResponseDto);
                }

                _logger.LogInformation("Agents recuperados com sucesso para o usuário: {id}", userId);

                return Result<IEnumerable<AgentResponseDto>>.Success(agentResponseDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar os agents para o usuário: {id}", userId);
                return Result<IEnumerable<AgentResponseDto>>.Failure(new[] { "Erro ao recuperar os agents." });
            }
        }

        public async Task<Result<AgentResponseDto>> GetOneById(int agentId)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar o agent com ID: {AgentId} ", agentId);

                var agent = await _agentRepository.GetByIdAsync(agentId);
                if (agent == null)
                    return Result<AgentResponseDto>.Failure(new[] { "Agent não encontrado." });

                var agentResponseDto = new AgentResponseDto
                {
                    Id = agent.Id,
                    Name = agent.Name,
                    Context = agent.Context,
                    UserId = agent.UserId,
                    AgentParamsId = agent.AgentParamsId,
                    AgentTypeId = agent.AgentTypeId,
                    AgentBrainId = agent.AgentBrainId,
                    InstanceWhatsappName = agent.InstanceWhatsappName
                };

                // Buscar o paramName se AgentParamsId existir
                if (agent.AgentParamsId.HasValue)
                {
                    var agentParamsResult = await _agentParamService.GetOneById(agent.AgentParamsId.Value);
                    if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                    {
                        agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                    }
                }

                // Buscar o brainName se AgentBrainId existir
                if (agent.AgentBrainId.HasValue)
                {
                    var brainResult = await _brainService.GetOneById(agent.AgentBrainId.Value);
                    if (brainResult.Succeeded && brainResult.Value != null)
                    {
                        agentResponseDto.BrainName = brainResult.Value.BrainName;
                    }
                }

                // Buscar o typeName se AgentTypeId existir
                if (agent.AgentTypeId.HasValue)
                {
                    var typeResult = await _typeService.GetOneById(agent.AgentTypeId.Value);
                    if (typeResult.Succeeded && typeResult.Value != null)
                    {
                        agentResponseDto.TypeName = typeResult.Value.Name;
                    }
                }

                return Result<AgentResponseDto>.Success(agentResponseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar o agent com ID: {AgentId}", agentId);
                return Result<AgentResponseDto>.Failure(new[] { "Erro ao recuperar o agent." });
            }
        }
        public async Task<Result<AgentResponseDto>> Update(AgentUpdateDto data, int agentId)
        {
            try
            {
                _logger.LogInformation("Tentando atualizar o agent com ID: {AgentId}", agentId);

                var existingAgent = await _agentRepository.GetByIdAsync(agentId);
                if (existingAgent == null)
                {
                    return Result<AgentResponseDto>.Failure(new[] { "Agente não encontrado" });
                }

                if (!string.IsNullOrWhiteSpace(data.Context))
                    existingAgent.Context = data.Context;

                if (!string.IsNullOrWhiteSpace(data.Name))
                    existingAgent.Name = data.Name;

                if (data.agentParamsId.HasValue)
                    existingAgent.AgentParamsId = data.agentParamsId.Value;

                if (data.AgentTypeId.HasValue)
                    existingAgent.AgentTypeId = data.AgentTypeId.Value;

                if (data.AgentBrainId.HasValue)
                    existingAgent.AgentBrainId = data.AgentBrainId.Value;

                if (!string.IsNullOrWhiteSpace(data.InstanceWhatsappName))
                    existingAgent.InstanceWhatsappName = data.InstanceWhatsappName;

                var result = await _agentRepository.UpdateAsync(existingAgent);

                _logger.LogInformation("Agent atualizado com sucesso. Agent: {AgentName}", existingAgent.Name);

                // Criar AgentResponseDto com os relacionamentos carregados
                var agentResponseDto = new AgentResponseDto
                {
                    Id = result.Id,
                    Name = result.Name,
                    Context = result.Context,
                    UserId = result.UserId,
                    AgentParamsId = result.AgentParamsId,
                    AgentTypeId = result.AgentTypeId,
                    AgentBrainId = result.AgentBrainId,
                    InstanceWhatsappName = result.InstanceWhatsappName
                };

                // Buscar o paramName se AgentParamsId existir
                if (result.AgentParamsId.HasValue)
                {
                    var agentParamsResult = await _agentParamService.GetOneById(result.AgentParamsId.Value);
                    if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                    {
                        agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                    }
                }

                // Buscar o brainName se AgentBrainId existir
                if (result.AgentBrainId.HasValue)
                {
                    var brainResult = await _brainService.GetOneById(result.AgentBrainId.Value);
                    if (brainResult.Succeeded && brainResult.Value != null)
                    {
                        agentResponseDto.BrainName = brainResult.Value.BrainName;
                    }
                }

                // Buscar o typeName se AgentTypeId existir
                if (result.AgentTypeId.HasValue)
                {
                    var typeResult = await _typeService.GetOneById(result.AgentTypeId.Value);
                    if (typeResult.Succeeded && typeResult.Value != null)
                    {
                        agentResponseDto.TypeName = typeResult.Value.Name;
                    }
                }

                return Result<AgentResponseDto>.Success(agentResponseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar o agent com ID: {AgentId}", agentId);
                return Result<AgentResponseDto>.Failure(new[] { "Erro ao atualizar o agent." });
            }
        }

        public async Task<Result<PaginatedResult<AgentResponseDto>>> GetAgentsPaginated(AgentPaginationRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar agentes paginados para o usuário: {UserId}, página: {Page}, tamanho: {PageSize}",
                    request.UserId, request.Page, request.PageSize);

                var result = await _agentRepository.GetAgentsPaginatedAsync(request);

                var agentResponseDtos = new List<AgentResponseDto>();

                foreach (var agent in result.Data)
                {
                    var agentResponseDto = new AgentResponseDto
                    {
                        Id = agent.Id,
                        Name = agent.Name,
                        Context = agent.Context,
                        UserId = agent.UserId,
                        AgentParamsId = agent.AgentParamsId,
                        AgentTypeId = agent.AgentTypeId,
                        AgentBrainId = agent.AgentBrainId,
                        InstanceWhatsappName = agent.InstanceWhatsappName
                    };

                    // Buscar o paramName se AgentParamsId existir
                    if (agent.AgentParamsId.HasValue)
                    {
                        var agentParamsResult = await _agentParamService.GetOneById(agent.AgentParamsId.Value);
                        if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                        {
                            agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                        }
                    }

                    // Buscar o brainName se AgentBrainId existir
                    if (agent.AgentBrainId.HasValue)
                    {
                        var brainResult = await _brainService.GetOneById(agent.AgentBrainId.Value);
                        if (brainResult.Succeeded && brainResult.Value != null)
                        {
                            agentResponseDto.BrainName = brainResult.Value.BrainName;
                        }
                    }

                    // Buscar o typeName se AgentTypeId existir
                    if (agent.AgentTypeId.HasValue)
                    {
                        var typeResult = await _typeService.GetOneById(agent.AgentTypeId.Value);
                        if (typeResult.Succeeded && typeResult.Value != null)
                        {
                            agentResponseDto.TypeName = typeResult.Value.Name;
                        }
                    }

                    agentResponseDtos.Add(agentResponseDto);
                }

                var paginatedResult = new PaginatedResult<AgentResponseDto>
                {
                    Data = agentResponseDtos,
                    TotalCount = result.TotalCount,
                    Page = result.Page,
                    PageSize = result.PageSize
                };

                _logger.LogInformation("Agentes paginados recuperados com sucesso para o usuário: {UserId}. Total: {TotalCount}",
                    request.UserId, paginatedResult.TotalCount);

                return Result<PaginatedResult<AgentResponseDto>>.Success(paginatedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar agentes paginados para o usuário: {UserId}", request.UserId);
                return Result<PaginatedResult<AgentResponseDto>>.Failure(new[] { "Erro ao recuperar agentes paginados." });
            }
        }

        public async Task<Result<IEnumerable<AgentResponseDto>>> SearchAgentsByName(AgentSearchRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando buscar agentes por nome: {Name} para o usuário: {UserId}",
                    request.Name, request.UserId);

                var agents = await _agentRepository.SearchAgentsByNameAsync(request);

                var agentResponseDtos = new List<AgentResponseDto>();

                foreach (var agent in agents)
                {
                    var agentResponseDto = new AgentResponseDto
                    {
                        Id = agent.Id,
                        Name = agent.Name,
                        Context = agent.Context,
                        UserId = agent.UserId,
                        AgentParamsId = agent.AgentParamsId,
                        AgentTypeId = agent.AgentTypeId,
                        AgentBrainId = agent.AgentBrainId,
                        InstanceWhatsappName = agent.InstanceWhatsappName
                    };

                    // Buscar o paramName se AgentParamsId existir
                    if (agent.AgentParamsId.HasValue)
                    {
                        var agentParamsResult = await _agentParamService.GetOneById(agent.AgentParamsId.Value);
                        if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                        {
                            agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                        }
                    }

                    // Buscar o brainName se AgentBrainId existir
                    if (agent.AgentBrainId.HasValue)
                    {
                        var brainResult = await _brainService.GetOneById(agent.AgentBrainId.Value);
                        if (brainResult.Succeeded && brainResult.Value != null)
                        {
                            agentResponseDto.BrainName = brainResult.Value.BrainName;
                        }
                    }

                    // Buscar o typeName se AgentTypeId existir
                    if (agent.AgentTypeId.HasValue)
                    {
                        var typeResult = await _typeService.GetOneById(agent.AgentTypeId.Value);
                        if (typeResult.Succeeded && typeResult.Value != null)
                        {
                            agentResponseDto.TypeName = typeResult.Value.Name;
                        }
                    }

                    agentResponseDtos.Add(agentResponseDto);
                }

                _logger.LogInformation("Busca de agentes por nome concluída para o usuário: {UserId}. Encontrados: {Count} agentes",
                    request.UserId, agentResponseDtos.Count());

                return Result<IEnumerable<AgentResponseDto>>.Success(agentResponseDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar agentes por nome: {Name} para o usuário: {UserId}",
                    request.Name, request.UserId);
                return Result<IEnumerable<AgentResponseDto>>.Failure(new[] { "Erro ao buscar agentes por nome." });
            }
        }

        public async Task<Result<AgentResponseDto>> GetAgentByName(string name, int userId)
        {
            try
            {
                _logger.LogInformation("Tentando buscar agente específico por nome: {Name} para o usuário: {UserId}",
                    name, userId);

                var agent = await _agentRepository.GetAgentByNameAsync(name, userId);

                if (agent == null)
                {
                    _logger.LogInformation("Agente não encontrado com nome: {Name} para o usuário: {UserId}",
                        name, userId);
                    return Result<AgentResponseDto>.Failure(new[] { "Agente não encontrado com este nome." });
                }

                var agentResponseDto = new AgentResponseDto
                {
                    Id = agent.Id,
                    Name = agent.Name,
                    Context = agent.Context,
                    UserId = agent.UserId,
                    AgentParamsId = agent.AgentParamsId,
                    AgentTypeId = agent.AgentTypeId,
                    AgentBrainId = agent.AgentBrainId,
                    InstanceWhatsappName = agent.InstanceWhatsappName
                };

                // Buscar o paramName se AgentParamsId existir
                if (agent.AgentParamsId.HasValue)
                {
                    var agentParamsResult = await _agentParamService.GetOneById(agent.AgentParamsId.Value);
                    if (agentParamsResult.Succeeded && agentParamsResult.Value != null)
                    {
                        agentResponseDto.ParamName = agentParamsResult.Value.ParamName;
                    }
                }

                // Buscar o brainName se AgentBrainId existir
                if (agent.AgentBrainId.HasValue)
                {
                    var brainResult = await _brainService.GetOneById(agent.AgentBrainId.Value);
                    if (brainResult.Succeeded && brainResult.Value != null)
                    {
                        agentResponseDto.BrainName = brainResult.Value.BrainName;
                    }
                }

                // Buscar o typeName se AgentTypeId existir
                if (agent.AgentTypeId.HasValue)
                {
                    var typeResult = await _typeService.GetOneById(agent.AgentTypeId.Value);
                    if (typeResult.Succeeded && typeResult.Value != null)
                    {
                        agentResponseDto.TypeName = typeResult.Value.Name;
                    }
                }

                _logger.LogInformation("Agente encontrado com nome: {Name} para o usuário: {UserId}",
                    name, userId);

                return Result<AgentResponseDto>.Success(agentResponseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar agente por nome: {Name} para o usuário: {UserId}",
                    name, userId);
                return Result<AgentResponseDto>.Failure(new[] { "Erro ao buscar agente por nome." });
            }
        }
    }
}

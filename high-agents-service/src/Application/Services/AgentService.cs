using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.Extensions.Logging;


namespace HighAgentsApi.Service.Services
{
    public class AgentService : IAgentService
    {
        private readonly IAgentRepository _agentRepository;
        private readonly ILogger<AgentService> _logger;

        public AgentService(
            IAgentRepository agentRepository,
            ILogger<AgentService> logger)
        {
            _agentRepository = agentRepository;
            _logger = logger;
        }

        public async Task<Result<int>> Create(AgentDto data)
        {
            try
            {
                var agent = new Agent
                {
                    Context = data.Context,
                    UserId = data.userId,
                    Name = data.Name,
                    AgentParamsId = data.agentParamsId
                };

                await _agentRepository.AddAsync(agent);

                _logger.LogInformation("Agent criado com sucesso. Agent ID: {AgentId}", agent.Id);
                return Result<int>.Success(agent.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar o agent para o usuário: {UserId}", data.userId);
                return Result<int>.Failure(new[] { "Erro ao criar o agent." });
            }
        }

        public async Task<Result> Delete(int agentId)
        {
            try
            {
                _logger.LogInformation("Tentando deletar o agent com ID: {AgentId}", agentId);


                var agent = await _agentRepository.GetByIdAsync((int)agentId.GetHashCode());
                if (agent == null)
                {
                    return Result.Failure(new[] { "Agent não encontrado." });
                }

                await _agentRepository.DeleteAsync(agent);
                _logger.LogInformation("Agent deletado com sucesso. Agent ID: {AgentId}", agentId);

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar o agent com ID: {AgentId}", agentId);
                return Result.Failure(new[] { "Erro ao deletar o agent." });
            }
        }

        public async Task<Result<IEnumerable<Agent>>> GetAll(int userId)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar todos os agents para o usuário:{userId}", userId);

                var agents = await _agentRepository.GetAgentByUserIdAsync(userId);

                var agentEntities = agents.Select(agent => new Agent
                {
                    Name = agent.Name,
                    Context = agent.Context,
                    UserId = agent.UserId,
                    AgentParamsId = agent.AgentParamsId
                });

                _logger.LogInformation("Agents recuperados com sucesso para o usuário: {id}", userId);

                return Result<IEnumerable<Agent>>.Success(agentEntities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar os agents para o usuário: {id}", userId);
                return Result<IEnumerable<Agent>>.Failure(new[] { "Erro ao recuperar os agents." });
            }
        }

        public async Task<Result<Agent>> GetOneById(int agentId)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar o agent com ID: {AgentId} ", agentId);

                var agent = await _agentRepository.GetByIdAsync((int)agentId.GetHashCode()); // Conversão temporária
                if (agent == null)
                    return Result<Agent>.Failure(new[] { "Agent não encontrado." });

                // Converter Agent para Agent
                var agentEntity = new Agent
                {
                    Name = agent.Name,
                    Context = agent.Context,
                    UserId = agent.UserId,
                    AgentParamsId = agent.AgentParamsId
                };

                return Result<Agent>.Success(agentEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar o agent com ID: {AgentId}", agentId);
                return Result<Agent>.Failure(new[] { "Erro ao recuperar o agent." });
            }
        }
        public async Task<Result> Update(AgentUpdateDto data, int agentId)
        {
            try
            {
                _logger.LogInformation("Tentando atualizar o agent com ID: {AgentName} para o usuário: {UserEmail}", data.Name, data.userEmail);

                var existingAgent = await _agentRepository.GetByIdAsync((int)agentId.GetHashCode()); // Conversão temporária
                if (existingAgent == null)
                {
                    return Result.Failure(new[] { "Agent não encontrado." });
                }


                existingAgent.Context = data.Context;
                existingAgent.Name = data.Name;
                existingAgent.AgentParamsId = data.agentParamsId;

                await _agentRepository.UpdateAsync(existingAgent);

                _logger.LogInformation("Agent atualizado com sucesso. Agent: {AgentName}", data.Name);

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar o agent com name: {AgentName}", data.Name);
                return Result.Failure(new[] { "Erro ao atualizar o agent." });
            }
        }
    }
}

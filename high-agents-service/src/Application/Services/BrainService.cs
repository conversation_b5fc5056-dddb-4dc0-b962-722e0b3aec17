using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Application.Services
{
    public class BrainService : IBrainService
    {
        private readonly IBrainRepository _brainRepository;
        private readonly ILogger<BrainService> _logger;
        private readonly IAgentRepository _agentRepository;

        public BrainService(
            IBrainRepository brainRepository,
            ILogger<BrainService> logger,
            IAgentRepository agentRepository
            )
        {
            _brainRepository = brainRepository;
            _logger = logger;
            _agentRepository = agentRepository;
        }

        public async Task<Result<AgentBrain>> Create(BrainDto data)
        {
            try
            {
                // Verificar se já existe um brain com o mesmo nome para o usuário
                var existingBrain = await _brainRepository.GetBrainByNameAsync(data.BrainName, data.UserId);
                if (existingBrain != null)
                {
                    _logger.LogWarning("Tentativa de criar brain com nome duplicado: {Name} para o usuário: {UserId}",
                        data.BrainName, data.UserId);
                    return Result<AgentBrain>.Failure(new[] { "Já existe um brain com este nome para o usuário." });
                }

                var brain = new AgentBrain
                {
                    BrainName = data.BrainName,
                    Brainroot = data.Brainroot,
                    CompanyName = data.CompanyName,
                    CompanyDescription = data.CompanyDescription,
                    UserId = data.UserId,
                };

                await _brainRepository.AddAsync(brain);

                if (data.AgentId.HasValue)
                {
                    var agent = await _agentRepository.GetByIdAsync(data.AgentId.Value);
                    if (agent != null)
                    {
                        agent.AgentBrainId = brain.Id;
                        await _agentRepository.UpdateAsync(agent);
                    }
                }
                _logger.LogInformation("Brain criado com sucesso. Brain ID: {BrainId}", brain.Id);

                return Result<AgentBrain>.Success(brain);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar o brain para o usuário: {UserId}", data.UserId);
                return Result<AgentBrain>.Failure(new[] { "Erro ao criar o brain." });
            }
        }

        public async Task<Result<AgentBrain>> Delete(int brainId, int? deletedBy = null)
        {
            try
            {
                _logger.LogInformation("Tentando deletar o brain com ID: {BrainId}", brainId);

                var brain = await _brainRepository.GetByIdAsync(brainId);
                if (brain == null)
                {
                    return Result<AgentBrain>.Failure(new[] { "Brain não encontrado." });
                }

                var result = await _brainRepository.DeleteAsync(brain, deletedBy);
                _logger.LogInformation("Brain deletado com sucesso. Brain ID: {BrainId}", brainId);

                return Result<AgentBrain>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar o brain com ID: {BrainId}", brainId);
                return Result<AgentBrain>.Failure(new[] { "Erro ao deletar o brain." });
            }
        }

        public async Task<Result<IEnumerable<BrainResponseDto>>> GetAll(int userId)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar todos os brains para o usuário: {UserId}", userId);

                var brains = await _brainRepository.GetByUserIdAsync(userId);

                var brainResponseDtos = brains.Select(brain => new BrainResponseDto
                {
                    Id = brain.Id,
                    BrainName = brain.BrainName,
                    Brainroot = brain.Brainroot,
                    CompanyName = brain.CompanyName,
                    CompanyDescription = brain.CompanyDescription,
                    UserId = brain.UserId,
                    DeletedAt = brain.DeletedAt,
                    DeletedBy = brain.DeletedBy
                }).ToList();

                _logger.LogInformation("Brains recuperados com sucesso para o usuário: {UserId}. Total: {Count}", userId, brainResponseDtos.Count);

                return Result<IEnumerable<BrainResponseDto>>.Success(brainResponseDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar os brains para o usuário: {UserId}", userId);
                return Result<IEnumerable<BrainResponseDto>>.Failure(new[] { "Erro ao recuperar os brains." });
            }
        }

        public async Task<Result<BrainResponseDto>> GetOneById(int brainId)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar o brain com ID: {BrainId}", brainId);

                var brain = await _brainRepository.GetByIdAsync(brainId);
                if (brain == null)
                    return Result<BrainResponseDto>.Failure(new[] { "Brain não encontrado." });

                var brainResponseDto = new BrainResponseDto
                {
                    Id = brain.Id,
                    BrainName = brain.BrainName,
                    Brainroot = brain.Brainroot,
                    CompanyName = brain.CompanyName,
                    CompanyDescription = brain.CompanyDescription,
                    UserId = brain.UserId,
                    DeletedAt = brain.DeletedAt,
                    DeletedBy = brain.DeletedBy
                };

                return Result<BrainResponseDto>.Success(brainResponseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar o brain com ID: {BrainId}", brainId);
                return Result<BrainResponseDto>.Failure(new[] { "Erro ao recuperar o brain." });
            }
        }

        public async Task<Result<AgentBrain>> Update(BrainUpdateDto data, int brainId)
        {
            try
            {
                _logger.LogInformation("Tentando atualizar o brain com ID: {BrainId}", brainId);

                var existingBrain = await _brainRepository.GetByIdAsync(brainId);
                if (existingBrain == null)
                {
                    return Result<AgentBrain>.Failure(new[] { "Brain não encontrado" });
                }

                if (!string.IsNullOrWhiteSpace(data.BrainName))
                    existingBrain.BrainName = data.BrainName;

                if (!string.IsNullOrWhiteSpace(data.Brainroot))
                    existingBrain.Brainroot = data.Brainroot;

                if (data.CompanyName != null)
                    existingBrain.CompanyName = data.CompanyName;

                if (data.CompanyDescription != null)
                    existingBrain.CompanyDescription = data.CompanyDescription;

                var result = await _brainRepository.UpdateAsync(existingBrain);

                _logger.LogInformation("Brain atualizado com sucesso. Brain: {BrainName}", existingBrain.BrainName);

                return Result<AgentBrain>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar o brain com ID: {BrainId}", brainId);
                return Result<AgentBrain>.Failure(new[] { "Erro ao atualizar o brain." });
            }
        }

        public async Task<Result<PaginatedResult<AgentBrain>>> GetBrainsPaginated(BrainPaginationRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar brains paginados para o usuário: {UserId}, página: {Page}, tamanho: {PageSize}",
                    request.UserId, request.Page, request.PageSize);

                var result = await _brainRepository.GetBrainsPaginatedAsync(request);

                _logger.LogInformation("Brains paginados recuperados com sucesso para o usuário: {UserId}. Total: {TotalCount}",
                    request.UserId, result.TotalCount);

                return Result<PaginatedResult<AgentBrain>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar brains paginados para o usuário: {UserId}", request.UserId);
                return Result<PaginatedResult<AgentBrain>>.Failure(new[] { "Erro ao recuperar brains paginados." });
            }
        }

        public async Task<Result<IEnumerable<AgentBrain>>> SearchBrainsByName(BrainSearchRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando buscar brains por nome: {Name} para o usuário: {UserId}",
                    request.Name, request.UserId);

                var brains = await _brainRepository.SearchBrainsByNameAsync(request);

                _logger.LogInformation("Busca de brains por nome concluída para o usuário: {UserId}. Encontrados: {Count} brains",
                    request.UserId, brains.Count());

                return Result<IEnumerable<AgentBrain>>.Success(brains);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar brains por nome: {Name} para o usuário: {UserId}",
                    request.Name, request.UserId);
                return Result<IEnumerable<AgentBrain>>.Failure(new[] { "Erro ao buscar brains por nome." });
            }
        }

        public async Task<Result<BrainResponseDto>> GetBrainByName(string name, int userId)
        {
            try
            {
                _logger.LogInformation("Tentando buscar brain específico por nome: {Name} para o usuário: {UserId}",
                    name, userId);

                var brain = await _brainRepository.GetBrainByNameAsync(name, userId);

                if (brain == null)
                {
                    _logger.LogInformation("Brain não encontrado com nome: {Name} para o usuário: {UserId}",
                        name, userId);
                    return Result<BrainResponseDto>.Failure(new[] { "Brain não encontrado com este nome." });
                }

                var brainResponseDto = new BrainResponseDto
                {
                    Id = brain.Id,
                    BrainName = brain.BrainName,
                    Brainroot = brain.Brainroot,
                    CompanyName = brain.CompanyName,
                    CompanyDescription = brain.CompanyDescription,
                    UserId = brain.UserId,
                    DeletedAt = brain.DeletedAt,
                    DeletedBy = brain.DeletedBy
                };

                _logger.LogInformation("Brain encontrado com nome: {Name} para o usuário: {UserId}",
                    name, userId);

                return Result<BrainResponseDto>.Success(brainResponseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar brain por nome: {Name} para o usuário: {UserId}",
                    name, userId);
                return Result<BrainResponseDto>.Failure(new[] { "Erro ao buscar brain por nome." });
            }
        }
    }
}

using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Application.Services
{
    public class ChatService : IChatService
    {
        private readonly IChatRepository _chatRepository;
        private readonly IMessageRepository _messageRepository;
        private readonly ILogger<ChatService> _logger;

        public ChatService(
            IChatRepository chatRepository,
            IMessageRepository messageRepository,
            ILogger<ChatService> logger)
        {
            _chatRepository = chatRepository;
            _messageRepository = messageRepository;
            _logger = logger;
        }

        public async Task<Result<ChatResponseDto>> GetOneById(int chatId)
        {
            try
            {
                _logger.LogInformation("Buscando chat com ID: {ChatId}", chatId);

                var chat = await _chatRepository.GetByIdAsync(chatId);
                if (chat == null)
                {
                    return Result<ChatResponseDto>.Failure(new[] { "Chat não encontrado." });
                }

                var chatResponse = MapToResponseDto(chat);

                // Buscar última mensagem
                var lastMessage = await GetLastMessageForChat(chat.ConversationIdentificator);
                if (lastMessage != null)
                {
                    chatResponse.LastMessage = lastMessage.Content;
                    chatResponse.LastMessageTimestamp = lastMessage.CreatedAt;
                    chatResponse.LastMessageRole = lastMessage.Role;
                }

                _logger.LogInformation("Chat encontrado com ID: {ChatId}", chatId);
                return Result<ChatResponseDto>.Success(chatResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar chat com ID: {ChatId}", chatId);
                return Result<ChatResponseDto>.Failure(new[] { "Erro ao buscar chat." });
            }
        }

        public async Task<Result<IEnumerable<ChatResponseDto>>> GetAll()
        {
            try
            {
                _logger.LogInformation("Buscando todos os chats");

                var chats = await _chatRepository.GetAllAsync();
                var chatResponses = new List<ChatResponseDto>();

                foreach (var chat in chats)
                {
                    var chatResponse = MapToResponseDto(chat);

                    // Buscar última mensagem para cada chat
                    var lastMessage = await GetLastMessageForChat(chat.ConversationIdentificator);
                    if (lastMessage != null)
                    {
                        chatResponse.LastMessage = lastMessage.Content;
                        chatResponse.LastMessageTimestamp = lastMessage.CreatedAt;
                        chatResponse.LastMessageRole = lastMessage.Role;
                    }

                    chatResponses.Add(chatResponse);
                }

                _logger.LogInformation("Encontrados {Count} chats", chatResponses.Count);
                return Result<IEnumerable<ChatResponseDto>>.Success(chatResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar todos os chats");
                return Result<IEnumerable<ChatResponseDto>>.Failure(new[] { "Erro ao buscar chats." });
            }
        }

        public async Task<Result<IEnumerable<ChatResponseDto>>> GetByAgentId(int agentId)
        {
            try
            {
                _logger.LogInformation("Buscando chats para o agente: {AgentId}", agentId);

                var chats = await _chatRepository.GetByAgentIdAsync(agentId);
                var chatResponses = new List<ChatResponseDto>();

                foreach (var chat in chats)
                {
                    var chatResponse = MapToResponseDto(chat);

                    // Buscar última mensagem para cada chat
                    var lastMessage = await GetLastMessageForChat(chat.ConversationIdentificator);
                    if (lastMessage != null)
                    {
                        chatResponse.LastMessage = lastMessage.Content;
                        chatResponse.LastMessageTimestamp = lastMessage.CreatedAt;
                        chatResponse.LastMessageRole = lastMessage.Role;
                    }

                    chatResponses.Add(chatResponse);
                }

                _logger.LogInformation("Encontrados {Count} chats para o agente: {AgentId}", chatResponses.Count, agentId);
                return Result<IEnumerable<ChatResponseDto>>.Success(chatResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar chats para o agente: {AgentId}", agentId);
                return Result<IEnumerable<ChatResponseDto>>.Failure(new[] { "Erro ao buscar chats do agente." });
            }
        }

        public async Task<Result<ChatResponseDto>> GetByConversationIdentificator(string conversationIdentificator)
        {
            try
            {
                _logger.LogInformation("Buscando chat por conversa: {ConversationIdentificator}", conversationIdentificator);

                var chat = await _chatRepository.GetByConversationIdentificatorAsync(conversationIdentificator);
                if (chat == null)
                {
                    return Result<ChatResponseDto>.Failure(new[] { "Chat não encontrado para esta conversa." });
                }

                var chatResponse = MapToResponseDto(chat);

                // Buscar última mensagem
                var lastMessage = await GetLastMessageForChat(chat.ConversationIdentificator);
                if (lastMessage != null)
                {
                    chatResponse.LastMessage = lastMessage.Content;
                    chatResponse.LastMessageTimestamp = lastMessage.CreatedAt;
                    chatResponse.LastMessageRole = lastMessage.Role;
                }

                _logger.LogInformation("Chat encontrado para conversa: {ConversationIdentificator}", conversationIdentificator);
                return Result<ChatResponseDto>.Success(chatResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar chat por conversa: {ConversationIdentificator}", conversationIdentificator);
                return Result<ChatResponseDto>.Failure(new[] { "Erro ao buscar chat por conversa." });
            }
        }

        public async Task<Result<ChatResponseDto>> GetOrCreateByConversation(string conversationIdentificator, int agentId, string? username = null)
        {
            try
            {
                _logger.LogInformation("Buscando ou criando chat para conversa: {ConversationIdentificator}", conversationIdentificator);

                var chat = await _chatRepository.GetOrCreateByConversationAsync(conversationIdentificator, agentId, true, username);

                var chatResponse = MapToResponseDto(chat!);

                // Buscar última mensagem
                var lastMessage = await GetLastMessageForChat(chat!.ConversationIdentificator);
                if (lastMessage != null)
                {
                    chatResponse.LastMessage = lastMessage.Content;
                    chatResponse.LastMessageTimestamp = lastMessage.CreatedAt;
                    chatResponse.LastMessageRole = lastMessage.Role;
                }

                _logger.LogInformation("Chat obtido/criado para conversa: {ConversationIdentificator}", conversationIdentificator);
                return Result<ChatResponseDto>.Success(chatResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar/criar chat para conversa: {ConversationIdentificator}", conversationIdentificator);
                return Result<ChatResponseDto>.Failure(new[] { "Erro ao buscar/criar chat." });
            }
        }

        public async Task<Result<Chat>> Create(ChatDto chatDto)
        {
            try
            {
                _logger.LogInformation("Criando novo chat para agente: {AgentId}", chatDto.AgentId);

                var chat = new Chat
                {
                    AgentId = chatDto.AgentId,
                    ConversationIdentificator = chatDto.ConversationIdentificator,
                    Ia = true, // Sempre cria com IA ativada por padrão
                    Username = chatDto.Username
                };

                await _chatRepository.AddAsync(chat);

                _logger.LogInformation("Chat criado com sucesso. ID: {ChatId}", chat.Id);
                return Result<Chat>.Success(chat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar chat para agente: {AgentId}", chatDto.AgentId);
                return Result<Chat>.Failure(new[] { "Erro ao criar chat." });
            }
        }

        public async Task<Result<Chat>> Update(int chatId, ChatUpdateDto chatDto)
        {
            try
            {
                _logger.LogInformation("Atualizando chat com ID: {ChatId}", chatId);

                var existingChat = await _chatRepository.GetByIdAsync(chatId);
                if (existingChat == null)
                {
                    return Result<Chat>.Failure(new[] { "Chat não encontrado." });
                }

                if (chatDto.AgentId.HasValue)
                    existingChat.AgentId = chatDto.AgentId.Value;

                if (!string.IsNullOrWhiteSpace(chatDto.ConversationIdentificator))
                    existingChat.ConversationIdentificator = chatDto.ConversationIdentificator;

                if (chatDto.Ia.HasValue)
                    existingChat.Ia = chatDto.Ia.Value;

                if (chatDto.Username != null)
                    existingChat.Username = chatDto.Username;

                var result = await _chatRepository.UpdateAsync(existingChat);

                _logger.LogInformation("Chat atualizado com sucesso. ID: {ChatId}", chatId);
                return Result<Chat>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar chat com ID: {ChatId}", chatId);
                return Result<Chat>.Failure(new[] { "Erro ao atualizar chat." });
            }
        }

        public async Task<Result<Chat>> Delete(int chatId, int? deletedBy = null)
        {
            try
            {
                _logger.LogInformation("Deletando chat com ID: {ChatId}", chatId);

                var chat = await _chatRepository.GetByIdAsync(chatId);
                if (chat == null)
                {
                    return Result<Chat>.Failure(new[] { "Chat não encontrado." });
                }

                var result = await _chatRepository.DeleteAsync(chat, deletedBy);

                _logger.LogInformation("Chat deletado com sucesso. ID: {ChatId}", chatId);
                return Result<Chat>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar chat com ID: {ChatId}", chatId);
                return Result<Chat>.Failure(new[] { "Erro ao deletar chat." });
            }
        }

        public async Task<Result<bool>> DeleteByAgentId(int agentId)
        {
            try
            {
                _logger.LogInformation("Deletando chats do agente: {AgentId}", agentId);

                await _chatRepository.DeleteByAgentIdAsync(agentId);

                _logger.LogInformation("Chats do agente deletados com sucesso. AgentId: {AgentId}", agentId);
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar chats do agente: {AgentId}", agentId);
                return Result<bool>.Failure(new[] { "Erro ao deletar chats do agente." });
            }
        }

        public async Task<Result<bool>> DeleteByConversation(string conversationIdentificator)
        {
            try
            {
                _logger.LogInformation("Deletando chat da conversa: {ConversationIdentificator}", conversationIdentificator);

                await _chatRepository.DeleteByConversationAsync(conversationIdentificator);

                _logger.LogInformation("Chat da conversa deletado com sucesso. ConversationIdentificator: {ConversationIdentificator}", conversationIdentificator);
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar chat da conversa: {ConversationIdentificator}", conversationIdentificator);
                return Result<bool>.Failure(new[] { "Erro ao deletar chat da conversa." });
            }
        }

        public async Task<Result<bool>> ExistsByConversationIdentificator(string conversationIdentificator)
        {
            try
            {
                var exists = await _chatRepository.ExistsByConversationIdentificatorAsync(conversationIdentificator);
                return Result<bool>.Success(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao verificar existência do chat para conversa: {ConversationIdentificator}", conversationIdentificator);
                return Result<bool>.Failure(new[] { "Erro ao verificar existência do chat." });
            }
        }

        public async Task<Result<PaginatedResult<Chat>>> GetChatsPaginated(ChatPaginationRequestDto request)
        {
            try
            {
                var result = await _chatRepository.GetChatsPaginatedAsync(request);
                return Result<PaginatedResult<Chat>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar chats paginados");
                return Result<PaginatedResult<Chat>>.Failure(new[] { "Erro ao buscar chats paginados." });
            }
        }

        public async Task<Result<IEnumerable<Chat>>> SearchChats(ChatSearchRequestDto request)
        {
            try
            {
                var chats = await _chatRepository.SearchChatsAsync(request);
                return Result<IEnumerable<Chat>>.Success(chats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar chats");
                return Result<IEnumerable<Chat>>.Failure(new[] { "Erro ao buscar chats." });
            }
        }

        public async Task<Result<IEnumerable<ChatResponseDto>>> GetRecentChatsByAgentId(int agentId, int count)
        {
            try
            {
                var chats = await _chatRepository.GetRecentChatsByAgentIdAsync(agentId, count);
                var chatResponses = new List<ChatResponseDto>();

                foreach (var chat in chats)
                {
                    var chatResponse = MapToResponseDto(chat);

                    // Buscar última mensagem para cada chat
                    var lastMessage = await GetLastMessageForChat(chat.ConversationIdentificator);
                    if (lastMessage != null)
                    {
                        chatResponse.LastMessage = lastMessage.Content;
                        chatResponse.LastMessageTimestamp = lastMessage.CreatedAt;
                        chatResponse.LastMessageRole = lastMessage.Role;
                    }

                    chatResponses.Add(chatResponse);
                }

                return Result<IEnumerable<ChatResponseDto>>.Success(chatResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar chats recentes do agente: {AgentId}", agentId);
                return Result<IEnumerable<ChatResponseDto>>.Failure(new[] { "Erro ao buscar chats recentes." });
            }
        }

        private ChatResponseDto MapToResponseDto(Chat chat)
        {
            return new ChatResponseDto
            {
                Id = chat.Id,
                AgentId = chat.AgentId,
                ConversationIdentificator = chat.ConversationIdentificator,
                Ia = chat.Ia,
                Username = chat.Username,
                CreatedAt = chat.CreatedAt,
                DeletedAt = chat.DeletedAt,
                DeletedBy = chat.DeletedBy
            };
        }

        private async Task<Message?> GetLastMessageForChat(string conversationIdentificator)
        {
            try
            {
                var messages = await _messageRepository.GetRecentByConversationAsync(conversationIdentificator, 1);
                return messages.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao buscar última mensagem para conversa: {ConversationIdentificator}", conversationIdentificator);
                return null;
            }
        }
    }
}

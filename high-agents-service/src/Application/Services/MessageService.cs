using Api.Infrastructure.OpenIa;
using HighAgentsApi.Application.Services;
using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Service.Services
{
    public class MessageService : IMessageService
    {
        private readonly IMessageRepository _messageRepository;
        private readonly ILogger<MessageService> _logger;
        private readonly IAgentService _agentService;
        private readonly IAgentParamsService _agentParamsService;

        private readonly PromptContextService _promptContextText;

        public MessageService(
            IMessageRepository messageRepository,
            ILogger<MessageService> logger,
            IAgentService agentService,
            IAgentParamsService agentParamsService,
            PromptContextService promptContextText
            )
        {
            _messageRepository = messageRepository;
            _logger = logger;
            _agentService = agentService;
            _agentParamsService = agentParamsService;
            _promptContextText = promptContextText;
        }

        public async Task<IEnumerable<MessageDtoList>> GetAllByAgentIdAsync(int agentId, string userEmail)
        {
            try
            {
                var messages = await _messageRepository.GetByAgentIdAsync(agentId);
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now // Se você tiver campo de timestamp real, use m.Timestamp
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar mensagens para o agente: {AgentId}", agentId);
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<IEnumerable<MessageDtoList>> GetByRoleAsync(int agentId, string role)
        {
            try
            {
                var messages = await _messageRepository.GetByRoleAsync(agentId, role);
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar mensagens por role '{Role}' para o agente: {AgentId}", role, agentId);
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<IEnumerable<MessageDtoList>> GetRecentByAgentIdAsync(int agentId, int count)
        {
            try
            {
                var messages = await _messageRepository.GetRecentByAgentIdAsync(agentId, count);
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar últimas {Count} mensagens para o agente: {AgentId}", count, agentId);
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<string> CreateAsync(MessageDto message, string leadName)
        {
            try
            {

                var agentResult = await _agentService.GetOneById(message.AgentId);

                if (!agentResult.Succeeded || agentResult.Value == null)
                {
                    throw new Exception($"Agente com ID {message.AgentId} não encontrado.");
                }

                var userMessage = new Message
                {
                    AgentId = message.AgentId,
                    Role = message.Role ?? "user",
                    Content = message.Content,
                    Agent = agentResult.Value
                };


                await _messageRepository.AddAsync(userMessage);

                _logger.LogInformation("Mensagem criada com sucesso para o agente: {AgentId} pelo usuário", message.AgentId);

                //var apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");
                //if (string.IsNullOrEmpty(apiKey))
                //    throw new Exception("OPENAI_API_KEY não configurada.");

                var agent = await _agentService.GetOneById(message.AgentId);
                if (!agent.Succeeded || agent.Value == null)
                {
                    throw new Exception($"Agente com ID {message.AgentId} não encontrado.");
                }

                var agentParamsResult = await _agentParamsService.GetOneById(agent.Value.AgentParamsId ?? 0);

                if (!agentParamsResult.Succeeded || agentParamsResult.Value == null)
                {
                    throw new Exception($"Parâmetros do agente {agent.Value.Id} não encontrados.");
                }

                var fullContext = _promptContextText.CreateHighTicketPrompt(agentParamsResult.Value, leadName);


                var openIaService = new OpenAIService("********************************************************************************************************************************************************************");
                var aiResponse = await openIaService.GetResponseAsync(fullContext + "=" + message.Content);

                var assistantMessage = new Message
                {
                    AgentId = message.AgentId,
                    Content = aiResponse,
                    Role = "assistant",
                    Agent = agentResult.Value
                };

                await _messageRepository.AddAsync(assistantMessage);

                return assistantMessage.Content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar mensagem para o agente: {AgentId}", message.AgentId);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int messageId)
        {
            try
            {
                var message = await _messageRepository.GetByIdAsync(messageId);
                if (message == null) return false;

                await _messageRepository.DeleteAsync(message);
                _logger.LogInformation("Mensagem deletada com sucesso. ID: {MessageId}", messageId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar mensagem com ID: {MessageId}", messageId);
                return false;
            }
        }

        public async Task<bool> DeleteAllByAgentIdAsync(int agentId)
        {
            try
            {
                await _messageRepository.DeleteByAgentIdAsync(agentId);
                _logger.LogInformation("Todas as mensagens deletadas para o agente: {AgentId}", agentId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar todas as mensagens do agente: {AgentId}", agentId);
                return false;
            }
        }
    }
}

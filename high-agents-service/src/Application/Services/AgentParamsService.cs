using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Service.Services
{
    public class AgentParamsService : IAgentParamsService
    {
        private readonly IAgentParamsRepository _agentParamsRepository;
        private readonly IAgentRepository _agentRepository;
        private readonly ILogger<AgentParamsService> _logger;

        public AgentParamsService(
            IAgentParamsRepository agentParamsRepository,
            IAgentRepository agentRepository,
            ILogger<AgentParamsService> logger)
        {
            _agentRepository = agentRepository;
            _agentParamsRepository = agentParamsRepository;
            _logger = logger;
        }

        public async Task<Result<AgentParams>> Create(AgentParamsDto data)
        {
            try
            {
                // Verificar se já existe um AgentParams com o mesmo nome para o usuário
                if (!string.IsNullOrEmpty(data.ParamName))
                {
                    var existingAgentParams =
                        await _agentParamsRepository.GetAgentParamsByNameAsync(data.ParamName, data.UserId);
                    if (existingAgentParams != null)
                    {
                        _logger.LogWarning(
                            "Tentativa de criar AgentParams com nome duplicado: {ParamName} para o usuário: {UserId}",
                            data.ParamName, data.UserId);
                        return Result<AgentParams>.Failure(new[]
                            { "Já existe um parâmetro com este nome para o usuário." });
                    }
                }


                var agentParams = new AgentParams
                {
                    ParamName = data.ParamName,
                    ContextDescription = data.ContextDescription,
                    Tone = data.Tone,
                    MainMission = data.MainMission,
                    Goals = data.Goals,
                    ConversationSkills = data.ConversationSkills,
                    ConversationGuidelines = data.ConversationGuidelines,
                    AlwaysDo = data.AlwaysDo,
                    NeverDo = data.NeverDo,
                    SpecialSituationsHandling = data.SpecialSituationsHandling,
                    FinalObjective = data.FinalObjective,
                    CreativityLevel = data.CreativityLevel,
                    ResponseLength = data.ResponseLength,
                    ServiceInstructions = data.ServiceInstructions,
                    QualifiedLeadDefinition = data.QualifiedLeadDefinition,
                    DisqualifiedLeadDefinition = data.DisqualifiedLeadDefinition,
                    QualifiedFlow = data.QualifiedFlow,
                    DisqualifiedFlow = data.DisqualifiedFlow,
                    UserId = data.UserId
                };

                await _agentParamsRepository.AddAsync(agentParams);


                if (data.AgentId.HasValue)
                {
                    var agent = await _agentRepository.GetByIdAsync(data.AgentId.Value);
                    if (agent != null)
                    {
                        agent.AgentParamsId = agentParams.Id;
                        await _agentRepository.UpdateAsync(agent);
                    }
                }

                _logger.LogInformation("Parâmetros criados com sucesso para o usuário: {UserId}", data.UserId);
                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar os parâmetros do agente para o usuário: {UserId}", data.UserId);
                return Result<AgentParams>.Failure(new[] { "Erro ao criar parâmetros." });
            }
        }

        public async Task<Result<AgentParams>> Delete(int id, int? deletedBy = null)
        {
            try
            {
                var agentParams = await _agentParamsRepository.GetByIdAsync(id);
                if (agentParams == null)
                {
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });
                }

                var result = await _agentParamsRepository.DeleteAsync(agentParams, deletedBy);

                _logger.LogInformation("Parâmetros deletados com sucesso. ID: {Id}", id);
                return Result<AgentParams>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao deletar parâmetros." });
            }
        }

        public async Task<Result<IEnumerable<AgentParams>>> GetAll(int userId)
        {
            try
            {
                var paramsList = await _agentParamsRepository.GetByUserIdAsync(userId);

                _logger.LogInformation("Parâmetros recuperados com sucesso para o usuário: {UserId}. Total: {Count}", userId, paramsList.Count());
                return Result<IEnumerable<AgentParams>>.Success(paramsList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros para o usuário: {UserId}", userId);
                return Result<IEnumerable<AgentParams>>.Failure(new[] { "Erro ao recuperar parâmetros." });
            }
        }

        public async Task<Result<AgentParams>> GetOneById(int id)
        {
            try
            {
                var agentParams = await _agentParamsRepository.GetByIdAsync(id);
                if (agentParams == null)
                {
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });
                }

                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao recuperar parâmetros." });
            }
        }

        public async Task<Result<AgentParams>> Update(AgentParamsDto data, int id)
        {
            try
            {
                var existing = await _agentParamsRepository.GetByIdAsync(id);
                if (existing == null)
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });

                if (!string.IsNullOrWhiteSpace(data.ParamName))
                    existing.ParamName = data.ParamName;

                if (!string.IsNullOrWhiteSpace(data.ContextDescription))
                    existing.ContextDescription = data.ContextDescription;

                if (!string.IsNullOrWhiteSpace(data.Tone))
                    existing.Tone = data.Tone;

                if (!string.IsNullOrWhiteSpace(data.MainMission))
                    existing.MainMission = data.MainMission;

                if (!string.IsNullOrWhiteSpace(data.Goals))
                    existing.Goals = data.Goals;

                if (!string.IsNullOrWhiteSpace(data.ConversationSkills))
                    existing.ConversationSkills = data.ConversationSkills;

                if (!string.IsNullOrWhiteSpace(data.ConversationGuidelines))
                    existing.ConversationGuidelines = data.ConversationGuidelines;

                if (!string.IsNullOrWhiteSpace(data.AlwaysDo))
                    existing.AlwaysDo = data.AlwaysDo;

                if (!string.IsNullOrWhiteSpace(data.NeverDo))
                    existing.NeverDo = data.NeverDo;

                if (!string.IsNullOrWhiteSpace(data.SpecialSituationsHandling))
                    existing.SpecialSituationsHandling = data.SpecialSituationsHandling;

                if (!string.IsNullOrWhiteSpace(data.FinalObjective))
                    existing.FinalObjective = data.FinalObjective;

                if (!string.IsNullOrWhiteSpace(data.CreativityLevel))
                    existing.CreativityLevel = data.CreativityLevel;

                if (!string.IsNullOrWhiteSpace(data.ResponseLength))
                    existing.ResponseLength = data.ResponseLength;

                if (!string.IsNullOrWhiteSpace(data.ServiceInstructions))
                    existing.ServiceInstructions = data.ServiceInstructions;

                if (!string.IsNullOrWhiteSpace(data.QualifiedLeadDefinition))
                    existing.QualifiedLeadDefinition = data.QualifiedLeadDefinition;

                if (!string.IsNullOrWhiteSpace(data.DisqualifiedLeadDefinition))
                    existing.DisqualifiedLeadDefinition = data.DisqualifiedLeadDefinition;

                if (!string.IsNullOrWhiteSpace(data.QualifiedFlow))
                    existing.QualifiedFlow = data.QualifiedFlow;

                if (!string.IsNullOrWhiteSpace(data.DisqualifiedFlow))
                    existing.DisqualifiedFlow = data.DisqualifiedFlow;

                var result = await _agentParamsRepository.UpdateAsync(existing);

                _logger.LogInformation("Parâmetros atualizados com sucesso. ID: {Id}", id);
                return Result<AgentParams>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao atualizar parâmetros." });
            }
        }

        public async Task<Result<PaginatedResultParams<AgentParams>>> GetAgentParamsPaginated(
            AgentParamsPaginationRequestDto request)
        {
            try
            {
                _logger.LogInformation(
                    "Tentando recuperar parâmetros paginados para o usuário: {UserId}, página: {Page}, tamanho: {PageSize}",
                    request.UserId, request.Page, request.PageSize);

                var result = await _agentParamsRepository.GetAgentParamsPaginatedAsync(request);

                _logger.LogInformation(
                    "Parâmetros paginados recuperados com sucesso para o usuário: {UserId}. Total: {TotalCount}",
                    request.UserId, result.TotalCount);

                return Result<PaginatedResultParams<AgentParams>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros paginados para o usuário: {UserId}", request.UserId);
                return Result<PaginatedResultParams<AgentParams>>.Failure(new[]
                    { "Erro ao recuperar parâmetros paginados." });
            }
        }

        public async Task<Result<IEnumerable<AgentParams>>> SearchAgentParamsByName(AgentParamsSearchRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando buscar parâmetros por nome: {ParamName} para o usuário: {UserId}",
                    request.ParamName, request.UserId);

                var agentParams = await _agentParamsRepository.SearchAgentParamsByNameAsync(request);

                _logger.LogInformation(
                    "Busca de parâmetros por nome concluída para o usuário: {UserId}. Encontrados: {Count} parâmetros",
                    request.UserId, agentParams.Count());

                return Result<IEnumerable<AgentParams>>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar parâmetros por nome: {ParamName} para o usuário: {UserId}",
                    request.ParamName, request.UserId);
                return Result<IEnumerable<AgentParams>>.Failure(new[] { "Erro ao buscar parâmetros por nome." });
            }
        }

        public async Task<Result<AgentParams>> GetAgentParamsByName(string paramName, int userId)
        {
            try
            {
                _logger.LogInformation(
                    "Tentando buscar parâmetro específico por nome: {ParamName} para o usuário: {UserId}",
                    paramName, userId);

                var agentParams = await _agentParamsRepository.GetAgentParamsByNameAsync(paramName, userId);

                if (agentParams == null)
                {
                    _logger.LogInformation("Parâmetro não encontrado com nome: {ParamName} para o usuário: {UserId}",
                        paramName, userId);
                    return Result<AgentParams>.Failure(new[] { "Parâmetro não encontrado com este nome." });
                }

                _logger.LogInformation("Parâmetro encontrado com nome: {ParamName} para o usuário: {UserId}",
                    paramName, userId);

                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar parâmetro por nome: {ParamName} para o usuário: {UserId}",
                    paramName, userId);
                return Result<AgentParams>.Failure(new[] { "Erro ao buscar parâmetro por nome." });
            }
        }
    }
}

using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Service.Services
{
    public class AgentParamsService : IAgentParamsService
    {
        private readonly IAgentParamsRepository _agentParamsRepository;
        private readonly ILogger<AgentParamsService> _logger;

        public AgentParamsService(
            IAgentParamsRepository agentParamsRepository,
            ILogger<AgentParamsService> logger)
        {
            _agentParamsRepository = agentParamsRepository;
            _logger = logger;
        }

        public async Task<Result<int>> Create(AgentParamsDto data)
        {
            try
            {
                var agentParams = new AgentParams
                {
                    CompanyName = data.CompanyName,
                    CompanyDescription = data.CompanyDescription,
                    Tone = data.Tone,
                    Goals = data.Goals,
                    MainMission = data.MainMission,
                    ContextDescription = data.ContextDescription,
                    QualificationRules = data.QualificationRules,
                    ConversationGuidelines = data.ConversationGuidelines,
                    OpeningScript = data.OpeningScript,
                    PreQualificationQuestions = data.PreQualificationQuestions,
                    PainAgitationScript = data.PainAgitationScript,
                    PricingAgitationScript = data.PricingAgitationScript,
                    TraditionalMethods = data.TraditionalMethods,
                    SolutionScript = data.SolutionScript,
                    ValueGenerationScript = data.ValueGenerationScript,
                    FinalQualificationQuestions = data.FinalQualificationQuestions,
                    OpportunityReinforcementScript = data.OpportunityReinforcementScript,
                    EmotionalActivationScript = data.EmotionalActivationScript,
                    CallToActionScript = data.CallToActionScript,
                    DisqualifiedFlowScript = data.DisqualifiedFlowScript,
                    RestrictionsAndLimits = data.RestrictionsAndLimits,
                    AskAvailabilityStyle = data.AskAvailabilityStyle,
                    ConfirmationStyle = data.ConfirmationStyle,
                    UserTone = data.UserTone,
                    AlternativeSuggestionStyle = data.AlternativeSuggestionStyle,
                    ReminderStyle = data.ReminderStyle,
                    ReminderTiming = data.ReminderTiming,
                    RecurrenceStyle = data.RecurrenceStyle,
                    CallToAction = data.CallToAction,
                    CourtesyMessage = data.CourtesyMessage,
                    UserId = data.UserId
                };

                await _agentParamsRepository.AddAsync(agentParams);

                _logger.LogInformation("Parâmetros criados com sucesso para o usuário: {UserId}", data.UserId);
                return Result<int>.Success(agentParams.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar os parâmetros do agente para o usuário: {UserId}", data.UserId);
                return Result<int>.Failure(new[] { "Erro ao criar parâmetros." });
            }
        }
        public async Task<Result> Delete(int id)
        {
            try
            {
                var agentParams = await _agentParamsRepository.GetByIdAsync(id);
                if (agentParams == null)
                {
                    return Result.Failure(new[] { "Parâmetros não encontrados." });
                }

                await _agentParamsRepository.DeleteAsync(agentParams);

                _logger.LogInformation("Parâmetros deletados com sucesso. ID: {Id}", id);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar parâmetros com ID: {Id}", id);
                return Result.Failure(new[] { "Erro ao deletar parâmetros." });
            }
        }

        public async Task<Result<IEnumerable<AgentParams>>> GetAll(int userId)
        {
            try
            {
                var paramsList = await _agentParamsRepository.GetByUserIdAsync(userId);
                if (!paramsList.Any())
                {
                    return Result<IEnumerable<AgentParams>>.Failure(new[] { "Nenhum parâmetro encontrado." });
                }

                _logger.LogInformation("Parâmetros recuperados com sucesso para o usuário: {UserId}", userId);
                return Result<IEnumerable<AgentParams>>.Success(paramsList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros para o usuário: {UserId}", userId);
                return Result<IEnumerable<AgentParams>>.Failure(new[] { "Erro ao recuperar parâmetros." });
            }
        }

        public async Task<Result<AgentParams>> GetOneById(int id)
        {
            try
            {
                var agentParams = await _agentParamsRepository.GetByIdAsync(id);
                if (agentParams == null)
                {
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });
                }

                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao recuperar parâmetros." });
            }
        }

        public async Task<Result> Update(AgentParamsDto data, int id)
        {
            try
            {
                var existing = await _agentParamsRepository.GetByIdAsync(id);
                if (existing == null)
                    return Result.Failure(new[] { "Parâmetros não encontrados." });

                existing.CompanyName = data.CompanyName;
                existing.CompanyDescription = data.CompanyDescription;
                existing.Tone = data.Tone;
                existing.Goals = data.Goals;
                existing.MainMission = data.MainMission;
                existing.ContextDescription = data.ContextDescription;
                existing.QualificationRules = data.QualificationRules;
                existing.ConversationGuidelines = data.ConversationGuidelines;
                existing.OpeningScript = data.OpeningScript;
                existing.PreQualificationQuestions = data.PreQualificationQuestions;
                existing.PainAgitationScript = data.PainAgitationScript;
                existing.PricingAgitationScript = data.PricingAgitationScript;
                existing.TraditionalMethods = data.TraditionalMethods;
                existing.SolutionScript = data.SolutionScript;
                existing.ValueGenerationScript = data.ValueGenerationScript;
                existing.FinalQualificationQuestions = data.FinalQualificationQuestions;
                existing.OpportunityReinforcementScript = data.OpportunityReinforcementScript;
                existing.EmotionalActivationScript = data.EmotionalActivationScript;
                existing.CallToActionScript = data.CallToActionScript;
                existing.DisqualifiedFlowScript = data.DisqualifiedFlowScript;
                existing.RestrictionsAndLimits = data.RestrictionsAndLimits;
                existing.AskAvailabilityStyle = data.AskAvailabilityStyle;
                existing.ConfirmationStyle = data.ConfirmationStyle;
                existing.UserTone = data.UserTone;
                existing.AlternativeSuggestionStyle = data.AlternativeSuggestionStyle;
                existing.ReminderStyle = data.ReminderStyle;
                existing.ReminderTiming = data.ReminderTiming;
                existing.RecurrenceStyle = data.RecurrenceStyle;
                existing.CallToAction = data.CallToAction;
                existing.CourtesyMessage = data.CourtesyMessage;

                await _agentParamsRepository.UpdateAsync(existing);

                _logger.LogInformation("Parâmetros atualizados com sucesso. ID: {Id}", id);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar parâmetros com ID: {Id}", id);
                return Result.Failure(new[] { "Erro ao atualizar parâmetros." });
            }

        }
    }
}

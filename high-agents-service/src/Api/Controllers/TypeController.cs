using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class TypeController : ControllerBase
{
    private readonly ITypeService _typeService;

    public TypeController(ITypeService typeService)
    {
        _typeService = typeService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] TypeDto type)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _typeService.Create(type);

            if (result.Succeeded)
            {
                return Ok(Response<object>.Success(result.Value));
            }

            return BadRequest(Response<object>.Failure(result.Errors));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(Response<object>.Failure(ex.Message));
        }
    }

    [HttpGet("userId/{id}")]
    public async Task<ActionResult> GetAll(int id)
    {
        var result = await _typeService.GetAll(id);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<TypeResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<TypeResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _typeService.GetOneById(id);

        if (result.Succeeded && result.Value != null)
        {
            return Ok(Response<TypeResponseDto>.Success(result.Value));
        }

        if (result.Value == null)
        {
            return NotFound(Response<TypeResponseDto>.Failure("Type not found"));
        }

        return BadRequest(Response<TypeResponseDto>.Failure(result.Errors));
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] TypeUpdateDto request)
    {
        var result = await _typeService.Update(request, id);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id, [FromQuery] int? deletedBy = null)
    {
        var result = await _typeService.Delete(id, deletedBy);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("paginated")]
    public async Task<ActionResult> GetPaginated([FromQuery] TypePaginationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _typeService.GetTypesPaginated(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("search")]
    public async Task<ActionResult> SearchByName([FromQuery] TypeSearchRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _typeService.SearchTypesByName(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("find-by-name")]
    public async Task<ActionResult> GetByName([FromQuery] string name, [FromQuery] int userId)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return BadRequest(Response<TypeResponseDto>.Failure("Nome é obrigatório."));
        }

        if (userId <= 0)
        {
            return BadRequest(Response<TypeResponseDto>.Failure("UserId deve ser maior que zero."));
        }

        var result = await _typeService.GetTypeByName(name, userId);

        if (result.Succeeded)
        {
            return Ok(Response<TypeResponseDto>.Success(result.Value));
        }

        return BadRequest(Response<TypeResponseDto>.Failure(result.Errors));
    }
}

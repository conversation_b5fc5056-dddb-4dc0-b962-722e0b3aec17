using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class AgentParamsController : ControllerBase
{
    private readonly IAgentParamsService _agentParamsService;

    public AgentParamsController(IAgentParamsService agentParamsService)
    {
        _agentParamsService = agentParamsService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] AgentParamsDto agentParams)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _agentParamsService.Create(agentParams);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("userId/{userId}")]
    public async Task<ActionResult> GetAll(int userId)
    {
        var result = await _agentParamsService.GetAll(userId);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<AgentParams>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<AgentParams>>.Failure(result.Errors));
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _agentParamsService.GetOneById(id);

        if (result.Succeeded && result.Value != null)
        {
            return Ok(Response<AgentParams>.Success(result.Value));
        }

        if (result.Value == null)
        {
            return NotFound(Response<AgentParams>.Failure("AgentParams not found"));
        }

        return BadRequest(Response<AgentParams>.Failure(result.Errors));
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] AgentParamsDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _agentParamsService.Update(request, id);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        var result = await _agentParamsService.Delete(id);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("paginated")]
    public async Task<ActionResult> GetPaginated([FromQuery] AgentParamsPaginationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _agentParamsService.GetAgentParamsPaginated(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("search")]
    public async Task<ActionResult> SearchByName([FromQuery] AgentParamsSearchRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _agentParamsService.SearchAgentParamsByName(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("find-by-name")]
    public async Task<ActionResult> GetByName([FromQuery] string paramName, [FromQuery] int userId)
    {
        if (string.IsNullOrWhiteSpace(paramName))
        {
            return BadRequest(Response<AgentParams>.Failure("Nome do parâmetro é obrigatório."));
        }

        if (userId <= 0)
        {
            return BadRequest(Response<AgentParams>.Failure("UserId deve ser maior que zero."));
        }

        var result = await _agentParamsService.GetAgentParamsByName(paramName, userId);

        if (result.Succeeded)
        {
            return Ok(Response<AgentParams>.Success(result.Value));
        }

        return BadRequest(Response<AgentParams>.Failure(result.Errors));
    }
}

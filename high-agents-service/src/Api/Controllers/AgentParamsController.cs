using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class AgentParamsController : ControllerBase
{
    private readonly IAgentParamsService _agentParamsService;

    public AgentParamsController(IAgentParamsService agentParamsService)
    {
        _agentParamsService = agentParamsService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] AgentParamsDto agentParams)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _agentParamsService.Create(agentParams);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { id = result.Value, message = "AgentParams created successfully" });
    }

    [HttpGet("user/{userId}")]
    public async Task<ActionResult> GetAll(int userId)
    {
        var result = await _agentParamsService.GetAll(userId);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result.Value);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _agentParamsService.GetOneById(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        if (result.Value == null)
        {
            return NotFound(new { message = "AgentParams not found" });
        }

        return Ok(result.Value);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] AgentParamsDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _agentParamsService.Update(request, id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { message = "AgentParams updated successfully" });
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        var result = await _agentParamsService.Delete(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { message = "AgentParams deleted successfully" });
    }
}

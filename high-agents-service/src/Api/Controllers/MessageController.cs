using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class MessageController : ControllerBase
{
    private readonly IMessageService _messageService;

    public MessageController(IMessageService messageService)
    {
        _messageService = messageService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] MessageDto message,string leadName)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await _messageService.CreateAsync(message,leadName);

        return Ok(result);
    }

    [HttpGet("agent/{agentId}")]
    public async Task<IActionResult> GetAllByAgent(int agentId, [FromQuery] string userEmail)
    {
        var messages = await _messageService.GetAllByAgentIdAsync(agentId, userEmail);

        if (!messages.Any())
            return NotFound(new { message = "No messages found for this agent" });

        return Ok(messages);
    }

    [HttpGet("agent/{agentId}/role/{role}")]
    public async Task<IActionResult> GetByRole(int agentId, string role)
    {
        var messages = await _messageService.GetByRoleAsync(agentId, role);

        if (!messages.Any())
            return NotFound(new { message = $"No messages found for role '{role}'" });

        return Ok(messages);
    }

    [HttpGet("agent/{agentId}/recent/{count}")]
    public async Task<IActionResult> GetRecentByAgent(int agentId, int count)
    {
        var messages = await _messageService.GetRecentByAgentIdAsync(agentId, count);

        if (!messages.Any())
            return NotFound(new { message = "No recent messages found" });

        return Ok(messages);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var success = await _messageService.DeleteAsync(id);

        if (!success)
            return NotFound(new { message = "Message not found or could not be deleted" });

        return Ok(new { message = "Message deleted successfully" });
    }

    [HttpDelete("agent/{agentId}")]
    public async Task<IActionResult> DeleteAllByAgent(int agentId)
    {
        var success = await _messageService.DeleteAllByAgentIdAsync(agentId);

        if (!success)
            return BadRequest(new { message = "Could not delete messages for this agent" });

        return Ok(new { message = "All messages for the agent deleted successfully" });
    }
}

using System.Linq;
using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Models;
using HighAgentsApi.Domain.Responses;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class MessageController : ControllerBase
{
    private readonly IMessageService _messageService;

    public MessageController(IMessageService messageService)
    {
        _messageService = messageService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] MessageDto message, [FromQuery] string leadName)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _messageService.CreateAsync(message, leadName);

            if (!string.IsNullOrEmpty(result))
            {
                return Ok(Response<string>.Success(result));
            }

            return Ok(Response<string>.Success("Mensagem processada com sucesso."));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpGet("agent/{agentId}")]
    public async Task<IActionResult> GetAllByAgent(int agentId)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        try
        {
            var messages = await _messageService.GetAllByAgentIdAsync(agentId);

            if (!messages.Any())
            {
                return NotFound(Response<IEnumerable<MessageDtoList>>.Failure("Nenhuma mensagem encontrada para este agente."));
            }

            return Ok(Response<IEnumerable<MessageDtoList>>.Success(messages));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpGet("agent/{agentId}/role/{role}")]
    public async Task<IActionResult> GetByRole(int agentId, string role)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        if (string.IsNullOrWhiteSpace(role))
        {
            return BadRequest(Response<object>.Failure("Role é obrigatório."));
        }

        try
        {
            var messages = await _messageService.GetByRoleAsync(agentId, role);

            if (!messages.Any())
            {
                return NotFound(Response<IEnumerable<MessageDtoList>>.Failure($"Nenhuma mensagem encontrada para o role '{role}'."));
            }

            return Ok(Response<IEnumerable<MessageDtoList>>.Success(messages));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpGet("agent/{agentId}/recent/{count}")]
    public async Task<IActionResult> GetRecentByAgent(int agentId, int count)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        if (count <= 0)
        {
            return BadRequest(Response<object>.Failure("Count deve ser maior que zero."));
        }

        try
        {
            var messages = await _messageService.GetRecentByAgentIdAsync(agentId, count);

            if (!messages.Any())
            {
                return NotFound(Response<IEnumerable<MessageDtoList>>.Failure("Nenhuma mensagem recente encontrada."));
            }

            return Ok(Response<IEnumerable<MessageDtoList>>.Success(messages));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        if (id <= 0)
        {
            return BadRequest(Response<object>.Failure("Id deve ser maior que zero."));
        }

        try
        {
            var success = await _messageService.DeleteAsync(id);

            if (!success)
            {
                return NotFound(Response<object>.Failure("Mensagem não encontrada ou não pôde ser deletada."));
            }

            return Ok(Response<object>.Success("Mensagem deletada com sucesso."));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpDelete("agent/{agentId}")]
    public async Task<IActionResult> DeleteAllByAgent(int agentId)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        try
        {
            var success = await _messageService.DeleteAllByAgentIdAsync(agentId);

            if (!success)
            {
                return BadRequest(Response<object>.Failure("Não foi possível deletar as mensagens para este agente."));
            }

            return Ok(Response<object>.Success("Todas as mensagens do agente foram deletadas com sucesso."));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    // Novos endpoints baseados no conversationIdentificator
    [HttpGet("conversation/{conversationIdentificator}")]
    public async Task<IActionResult> GetAllByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        try
        {
            var messages = await _messageService.GetAllByConversationAsync(conversationIdentificator);

            if (!messages.Any())
            {
                return NotFound(Response<IEnumerable<MessageDtoList>>.Failure("Nenhuma mensagem encontrada para esta conversa."));
            }

            return Ok(Response<IEnumerable<MessageDtoList>>.Success(messages));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpGet("conversation/{conversationIdentificator}/role/{role}")]
    public async Task<IActionResult> GetByRoleAndConversation(
        string conversationIdentificator,
        string role
    )
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        if (string.IsNullOrWhiteSpace(role))
        {
            return BadRequest(Response<object>.Failure("Role é obrigatório."));
        }

        try
        {
            var messages = await _messageService.GetByRoleAndConversationAsync(
                conversationIdentificator,
                role
            );

            if (!messages.Any())
            {
                return NotFound(Response<IEnumerable<MessageDtoList>>.Failure($"Nenhuma mensagem encontrada para o role '{role}' nesta conversa."));
            }

            return Ok(Response<IEnumerable<MessageDtoList>>.Success(messages));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpGet("conversation/{conversationIdentificator}/recent/{count}")]
    public async Task<IActionResult> GetRecentByConversation(
        string conversationIdentificator,
        int count
    )
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        if (count <= 0)
        {
            return BadRequest(Response<object>.Failure("Count deve ser maior que zero."));
        }

        try
        {
            var messages = await _messageService.GetRecentByConversationAsync(
                conversationIdentificator,
                count
            );

            if (!messages.Any())
            {
                return NotFound(Response<IEnumerable<MessageDtoList>>.Failure("Nenhuma mensagem recente encontrada para esta conversa."));
            }

            return Ok(Response<IEnumerable<MessageDtoList>>.Success(messages));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpDelete("conversation/{conversationIdentificator}")]
    public async Task<IActionResult> DeleteAllByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        try
        {
            var success = await _messageService.DeleteAllByConversationAsync(conversationIdentificator);

            if (!success)
            {
                return BadRequest(Response<object>.Failure("Não foi possível deletar as mensagens para esta conversa."));
            }

            return Ok(Response<object>.Success("Todas as mensagens da conversa foram deletadas com sucesso."));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    [HttpGet("agent/{agentId}/grouped-by-conversation")]
    public async Task<IActionResult> GetMessagesGroupedByConversation(int agentId)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        try
        {
            var result = await _messageService.GetMessagesGroupedByConversationAsync(agentId);

            if (result.TotalConversations == 0)
            {
                return NotFound(Response<MessagesGroupedByConversationDto>.Failure("Nenhuma conversa encontrada para este agente."));
            }

            return Ok(Response<MessagesGroupedByConversationDto>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }

    private class ConversationComparer : IEqualityComparer<ConversationModel>
    {
        public bool Equals(ConversationModel? x, ConversationModel? y)
        {
            return x?.ConversationIdentificator == y?.ConversationIdentificator;
        }

        public int GetHashCode(ConversationModel obj)
        {
            return obj.ConversationIdentificator?.GetHashCode() ?? 0;
        }
    }

    [HttpGet("agent/{agentId}/conversations")]
    public async Task<IActionResult> GetConversationIdentificators(int agentId)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        try
        {
            var result = await _messageService.GetConversationsAsync(agentId);

            if (!result.Any())
            {
                return Ok(Response<IEnumerable<ConversationModel>>.Success(new List<ConversationModel>()));
            }

            return Ok(Response<IEnumerable<ConversationModel>>.Success(result.Distinct(new ConversationComparer())));
        }
        catch (Exception ex)
        {
            return BadRequest(Response<object>.Failure(new[] { ex.Message }));
        }
    }
}

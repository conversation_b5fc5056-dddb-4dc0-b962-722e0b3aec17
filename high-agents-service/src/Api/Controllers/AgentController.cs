using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class AgentController : ControllerBase
{
    private readonly IAgentService _agentService;

    public AgentController(IAgentService agentService)
    {
        _agentService = agentService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] AgentDto agent)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<AgentResponseDto>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _agentService.Create(agent);

            if (result.Succeeded)
            {
                return Ok(Response<AgentResponseDto>.Success(result.Value));
            }

            return BadRequest(Response<AgentResponseDto>.Failure(result.Errors));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(Response<AgentResponseDto>.Failure(ex.Message));
        }
    }

    [HttpGet("userId/{id}")]
    public async Task<ActionResult> GetAll(int id)
    {
        var result = await _agentService.GetAll(id);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<AgentResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<AgentResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _agentService.GetOneById(id);

        if (result.Succeeded && result.Value != null)
        {
            return Ok(Response<AgentResponseDto>.Success(result.Value));
        }

        if (result.Value == null)
        {
            return NotFound(Response<AgentResponseDto>.Failure("Agent not found"));
        }

        return BadRequest(Response<AgentResponseDto>.Failure(result.Errors));
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] AgentUpdateDto request)
    {
        var result = await _agentService.Update(request, id);

        if (result.Succeeded)
        {
            return Ok(Response<AgentResponseDto>.Success(result.Value));
        }

        return BadRequest(Response<AgentResponseDto>.Failure(result.Errors));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id, [FromQuery] int? deletedBy = null)
    {
        var result = await _agentService.Delete(id, deletedBy);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("paginated")]
    public async Task<ActionResult> GetPaginated([FromQuery] AgentPaginationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<PaginatedResult<AgentResponseDto>>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _agentService.GetAgentsPaginated(request);

        if (result.Succeeded)
        {
            return Ok(Response<PaginatedResult<AgentResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<PaginatedResult<AgentResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("search")]
    public async Task<ActionResult> SearchByName([FromQuery] AgentSearchRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<IEnumerable<AgentResponseDto>>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _agentService.SearchAgentsByName(request);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<AgentResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<AgentResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("find-by-name")]
    public async Task<ActionResult> GetByName([FromQuery] string name, [FromQuery] int userId)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return BadRequest(Response<AgentResponseDto>.Failure("Nome é obrigatório."));
        }

        if (userId <= 0)
        {
            return BadRequest(Response<AgentResponseDto>.Failure("UserId deve ser maior que zero."));
        }

        var result = await _agentService.GetAgentByName(name, userId);

        if (result.Succeeded)
        {
            return Ok(Response<AgentResponseDto>.Success(result.Value));
        }

        return BadRequest(Response<AgentResponseDto>.Failure(result.Errors));
    }
}

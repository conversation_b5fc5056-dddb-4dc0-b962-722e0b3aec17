using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class AgentController : ControllerBase
{
    private readonly IAgentService _agentService;

    public AgentController(IAgentService agentService)
    {
        _agentService = agentService;
    }


    [HttpPost]
    public async Task<IActionResult> Create([FromBody] AgentDto agent)
    {

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {

            var result = await this._agentService.Create(agent);


            return Ok(result);



        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { errors = ex });
        }

    }


    [HttpGet("userId/{id}")]
    public async Task<ActionResult> GetAll(int id)
    {
        var result = await _agentService.GetAll(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result.Value);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _agentService.GetOneById(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        if (result.Value == null)
        {
            return NotFound(new { message = "Agent not found" });
        }

        return Ok(result.Value);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] AgentUpdateDto request)
    {
        var result = await _agentService.Update(request, id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { message = "Agent updated successfully" });
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        var result = await _agentService.Delete(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { message = "Agent deleted successfully" });
    }
}


using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class ChatController : ControllerBase
{
    private readonly IChatService _chatService;

    public ChatController(IChatService chatService)
    {
        _chatService = chatService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] ChatDto chat)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _chatService.Create(chat);

            if (result.Succeeded)
            {
                return Ok(Response<object>.Success(result.Value));
            }

            return BadRequest(Response<object>.Failure(result.Errors));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(Response<object>.Failure(ex.Message));
        }
    }

    [HttpGet]
    public async Task<ActionResult> GetAll()
    {
        var result = await _chatService.GetAll();

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<ChatResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<ChatResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _chatService.GetOneById(id);

        if (result.Succeeded && result.Value != null)
        {
            return Ok(Response<ChatResponseDto>.Success(result.Value));
        }

        if (result.Value == null)
        {
            return NotFound(Response<ChatResponseDto>.Failure("Chat not found"));
        }

        return BadRequest(Response<ChatResponseDto>.Failure(result.Errors));
    }

    [HttpGet("agent/{agentId}")]
    public async Task<ActionResult> GetByAgentId(int agentId)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<IEnumerable<ChatResponseDto>>.Failure("AgentId deve ser maior que zero."));
        }

        var result = await _chatService.GetByAgentId(agentId);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<ChatResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<ChatResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("conversation/{conversationIdentificator}")]
    public async Task<ActionResult> GetByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<ChatResponseDto>.Failure("ConversationIdentificator é obrigatório."));
        }

        var result = await _chatService.GetByConversationIdentificator(conversationIdentificator);

        if (result.Succeeded && result.Value != null)
        {
            return Ok(Response<ChatResponseDto>.Success(result.Value));
        }

        if (result.Value == null)
        {
            return NotFound(Response<ChatResponseDto>.Failure("Chat não encontrado para esta conversa."));
        }

        return BadRequest(Response<ChatResponseDto>.Failure(result.Errors));
    }

    [HttpPost("get-or-create")]
    public async Task<ActionResult> GetOrCreateByConversation([FromBody] GetOrCreateChatRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _chatService.GetOrCreateByConversation(
                request.ConversationIdentificator,
                request.AgentId,
                request.Username);

            if (result.Succeeded)
            {
                return Ok(Response<ChatResponseDto>.Success(result.Value));
            }

            return BadRequest(Response<ChatResponseDto>.Failure(result.Errors));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(Response<object>.Failure(ex.Message));
        }
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] ChatUpdateDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _chatService.Update(id, request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id, [FromQuery] int? deletedBy = null)
    {
        var result = await _chatService.Delete(id, deletedBy);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpDelete("agent/{agentId}")]
    public async Task<ActionResult> DeleteByAgentId(int agentId)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        var result = await _chatService.DeleteByAgentId(agentId);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success("Chats do agente deletados com sucesso."));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpDelete("conversation/{conversationIdentificator}")]
    public async Task<ActionResult> DeleteByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        var result = await _chatService.DeleteByConversation(conversationIdentificator);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success("Chat da conversa deletado com sucesso."));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("exists/conversation/{conversationIdentificator}")]
    public async Task<ActionResult> ExistsByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        var result = await _chatService.ExistsByConversationIdentificator(conversationIdentificator);

        if (result.Succeeded)
        {
            return Ok(Response<bool>.Success(result.Value));
        }

        return BadRequest(Response<bool>.Failure(result.Errors));
    }

    [HttpGet("paginated")]
    public async Task<ActionResult> GetPaginated([FromQuery] ChatPaginationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _chatService.GetChatsPaginated(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("search")]
    public async Task<ActionResult> Search([FromQuery] ChatSearchRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _chatService.SearchChats(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("agent/{agentId}/recent/{count}")]
    public async Task<ActionResult> GetRecentByAgentId(int agentId, int count)
    {
        if (agentId <= 0)
        {
            return BadRequest(Response<object>.Failure("AgentId deve ser maior que zero."));
        }

        if (count <= 0)
        {
            return BadRequest(Response<object>.Failure("Count deve ser maior que zero."));
        }

        var result = await _chatService.GetRecentChatsByAgentId(agentId, count);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<ChatResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<ChatResponseDto>>.Failure(result.Errors));
    }

    [HttpPut("conversation/{conversationIdentificator}/toggle-ia")]
    public async Task<ActionResult> ToggleIaByConversation(string conversationIdentificator, [FromBody] ToggleIaRequestDto request)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
        {
            return BadRequest(Response<object>.Failure("ConversationIdentificator é obrigatório."));
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        // Primeiro buscar o chat pela conversa
        var chatResult = await _chatService.GetByConversationIdentificator(conversationIdentificator);
        if (!chatResult.Succeeded || chatResult.Value == null)
        {
            return NotFound(Response<object>.Failure("Chat não encontrado para esta conversa."));
        }

        // Atualizar o status da IA
        var updateResult = await _chatService.Update(chatResult.Value.Id, new ChatUpdateDto
        {
            Ia = request.Ia
        });

        if (updateResult.Succeeded)
        {
            var message = request.Ia ? "IA ativada com sucesso." : "IA desativada com sucesso.";
            return Ok(Response<object>.Success(message));
        }

        return BadRequest(Response<object>.Failure(updateResult.Errors));
    }
}

// DTO para o endpoint GetOrCreateByConversation
public class GetOrCreateChatRequestDto
{
    public required string ConversationIdentificator { get; set; }
    public required int AgentId { get; set; }
    public string? Username { get; set; }
}

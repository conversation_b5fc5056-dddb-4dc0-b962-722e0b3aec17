using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class BrainController : ControllerBase
{
    private readonly IBrainService _brainService;

    public BrainController(IBrainService brainService)
    {
        _brainService = brainService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] BrainDto brain)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _brainService.Create(brain);

            if (result.Succeeded)
            {
                return Ok(Response<object>.Success(result.Value));
            }

            return BadRequest(Response<object>.Failure(result.Errors));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(Response<object>.Failure(ex.Message));
        }
    }

    [HttpGet("userId/{id}")]
    public async Task<ActionResult> GetAll(int id)
    {
        var result = await _brainService.GetAll(id);

        if (result.Succeeded)
        {
            return Ok(Response<IEnumerable<BrainResponseDto>>.Success(result.Value));
        }

        return BadRequest(Response<IEnumerable<BrainResponseDto>>.Failure(result.Errors));
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _brainService.GetOneById(id);

        if (result.Succeeded && result.Value != null)
        {
            return Ok(Response<BrainResponseDto>.Success(result.Value));
        }

        if (result.Value == null)
        {
            return NotFound(Response<BrainResponseDto>.Failure("Brain not found"));
        }

        return BadRequest(Response<BrainResponseDto>.Failure(result.Errors));
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] BrainUpdateDto request)
    {
        var result = await _brainService.Update(request, id);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id, [FromQuery] int? deletedBy = null)
    {
        var result = await _brainService.Delete(id, deletedBy);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("paginated")]
    public async Task<ActionResult> GetPaginated([FromQuery] BrainPaginationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _brainService.GetBrainsPaginated(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("search")]
    public async Task<ActionResult> SearchByName([FromQuery] BrainSearchRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(Response<object>.Failure(ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))));
        }

        var result = await _brainService.SearchBrainsByName(request);

        if (result.Succeeded)
        {
            return Ok(Response<object>.Success(result.Value));
        }

        return BadRequest(Response<object>.Failure(result.Errors));
    }

    [HttpGet("find-by-name")]
    public async Task<ActionResult> GetByName([FromQuery] string name, [FromQuery] int userId)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return BadRequest(Response<BrainResponseDto>.Failure("Nome é obrigatório."));
        }

        if (userId <= 0)
        {
            return BadRequest(Response<BrainResponseDto>.Failure("UserId deve ser maior que zero."));
        }

        var result = await _brainService.GetBrainByName(name, userId);

        if (result.Succeeded)
        {
            return Ok(Response<BrainResponseDto>.Success(result.Value));
        }

        return BadRequest(Response<BrainResponseDto>.Failure(result.Errors));
    }
}

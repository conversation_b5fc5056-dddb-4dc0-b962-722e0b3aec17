using HighCapital.Core.Dependencies;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.AddInfrastructureServices();
builder.AddApplicationServices();
builder.AddWebServices();
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddOpenApiDocumentation();

var app = builder.Build();

app.UseCors(builder =>
{
    builder
        .AllowAnyHeader()
        .AllowAnyMethod()
        .AllowCredentials()
        .WithOrigins("http://localhost:5173")
        .WithOrigins("http://localhost:3000");
});

app.UseExceptionHandler();
app.UseAuthentication();
app.UseAuthorization();
app.UseOpenApiAndScalarDocumentation();

app.MapControllers();

app.Run();

public partial class Program { }

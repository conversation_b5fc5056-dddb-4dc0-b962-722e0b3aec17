using HighCapital.Core.Domain.Entities.HighAgents;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Dtos;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Database;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class ChatRepository : IChatRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<Chat> _dataset;

        public ChatRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<Chat>();
        }

        public async Task<IEnumerable<Chat>> GetAllAsync()
        {
            return await _dataset.Where(chat => chat.DeletedAt == null).ToListAsync();
        }

        public async Task<Chat?> GetByIdAsync(int id)
        {
            return await _dataset.FirstOrDefaultAsync(chat => chat.Id == id && chat.DeletedAt == null);
        }

        public async Task AddAsync(Chat entity)
        {
            entity.CreatedAt = DateTime.UtcNow;
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<Chat> UpdateAsync(Chat entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        public async Task<Chat> DeleteAsync(Chat entity, int? deletedBy = null)
        {
            // Soft delete
            entity.DeletedAt = DateTime.UtcNow;
            entity.DeletedBy = deletedBy?.ToString();
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        public async Task<Chat?> GetByConversationIdentificatorAsync(string conversationIdentificator)
        {
            return await _dataset
                .FirstOrDefaultAsync(chat =>
                    chat.ConversationIdentificator == conversationIdentificator &&
                    chat.DeletedAt == null);
        }

        public async Task<IEnumerable<Chat>> GetByAgentIdAsync(int agentId)
        {
            return await _dataset
                .Where(chat => chat.AgentId == agentId && chat.DeletedAt == null)
                .OrderByDescending(chat => chat.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Chat>> GetByAgentIdAndConversationAsync(int agentId, string conversationIdentificator)
        {
            return await _dataset
                .Where(chat =>
                    chat.AgentId == agentId &&
                    chat.ConversationIdentificator == conversationIdentificator &&
                    chat.DeletedAt == null)
                .OrderByDescending(chat => chat.CreatedAt)
                .ToListAsync();
        }

        public async Task<Chat?> GetOrCreateByConversationAsync(string conversationIdentificator, int agentId, bool ia, string? username = null)
        {
            var existingChat = await GetByConversationIdentificatorAsync(conversationIdentificator);

            if (existingChat != null)
            {
                return existingChat;
            }

            var newChat = new Chat
            {
                AgentId = agentId,
                ConversationIdentificator = conversationIdentificator,
                Ia = ia,
                Username = username,
                CreatedAt = DateTime.UtcNow
            };

            await AddAsync(newChat);
            return newChat;
        }

        public async Task<bool> ExistsByConversationIdentificatorAsync(string conversationIdentificator)
        {
            return await _dataset
                .AnyAsync(chat =>
                    chat.ConversationIdentificator == conversationIdentificator &&
                    chat.DeletedAt == null);
        }

        public async Task DeleteByAgentIdAsync(int agentId)
        {
            var chats = await _dataset
                .Where(chat => chat.AgentId == agentId && chat.DeletedAt == null)
                .ToListAsync();

            foreach (var chat in chats)
            {
                chat.DeletedAt = DateTime.UtcNow;
                _dataset.Update(chat);
            }

            await _context.SaveChangesAsync();
        }

        public async Task DeleteByConversationAsync(string conversationIdentificator)
        {
            var chats = await _dataset
                .Where(chat =>
                    chat.ConversationIdentificator == conversationIdentificator &&
                    chat.DeletedAt == null)
                .ToListAsync();

            foreach (var chat in chats)
            {
                chat.DeletedAt = DateTime.UtcNow;
                _dataset.Update(chat);
            }

            await _context.SaveChangesAsync();
        }

        public async Task<PaginatedResult<Chat>> GetChatsPaginatedAsync(ChatPaginationRequestDto request)
        {
            var query = _dataset.Where(chat => chat.DeletedAt == null);

            if (request.AgentId > 0)
            {
                query = query.Where(chat => chat.AgentId == request.AgentId);
            }

            var totalCount = await query.CountAsync();

            var chats = await query
                .OrderByDescending(chat => chat.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return new PaginatedResult<Chat>
            {
                Data = chats,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        public async Task<IEnumerable<Chat>> SearchChatsAsync(ChatSearchRequestDto request)
        {
            var query = _dataset.Where(chat => chat.DeletedAt == null);

            if (!string.IsNullOrEmpty(request.ConversationIdentificator))
            {
                query = query.Where(chat =>
                    chat.ConversationIdentificator.Contains(request.ConversationIdentificator));
            }

            if (request.AgentId.HasValue && request.AgentId > 0)
            {
                query = query.Where(chat => chat.AgentId == request.AgentId.Value);
            }

            if (request.Ia.HasValue)
            {
                query = query.Where(chat => chat.Ia == request.Ia.Value);
            }

            if (!string.IsNullOrEmpty(request.Username))
            {
                query = query.Where(chat =>
                    chat.Username != null &&
                    chat.Username.Contains(request.Username));
            }

            return await query
                .OrderByDescending(chat => chat.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Chat>> GetRecentChatsByAgentIdAsync(int agentId, int count)
        {
            return await _dataset
                .Where(chat => chat.AgentId == agentId && chat.DeletedAt == null)
                .OrderByDescending(chat => chat.CreatedAt)
                .Take(count)
                .ToListAsync();
        }
    }
}

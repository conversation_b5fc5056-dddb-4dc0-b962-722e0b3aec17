using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Interfaces.Repository;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Database;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class AgentRepository : IAgentRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<Agent> _dataset;

        public AgentRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<Agent>();
        }

        public async Task<IEnumerable<Agent>> GetAgentByUserIdAsync(int userId)
        {
            return await _dataset.Where(agent => agent.UserId == userId).ToListAsync();
        }

        public async Task AddAsync(Agent entity)
        {
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Agent entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Agent entity)
        {
            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Agent>> GetAllAsync()
        {
            return await _dataset.ToListAsync();
        }

        public async Task<Agent?> GetByIdAsync(int id)
        {
            return await _dataset.FindAsync(id);
        }
    }
}

using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Interfaces.Repository;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Database;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class AgentParamsRepository : IAgentParamsRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<AgentParams> _dataset;

        public AgentParamsRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<AgentParams>();
        }

        public async Task<IEnumerable<AgentParams>> GetByUserIdAsync(int userId)
        {
            return await _dataset
                .Where(p => p.UserId == userId)
                .ToListAsync();
        }

        public async Task AddAsync(AgentParams entity)
        {
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(AgentParams entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(AgentParams entity)
        {
            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<AgentParams>> GetAllAsync()
        {
            return await _dataset.ToListAsync();
        }

        public async Task<AgentParams?> GetByIdAsync(int id)
        {
            return await _dataset.FindAsync(id);
        }
    }
}

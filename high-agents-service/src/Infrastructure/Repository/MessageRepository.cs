using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Interfaces.Repository;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Database;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class MessageRepository : IMessageRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<Message> _dataset;

        public MessageRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<Message>();
        }

        public async Task<IEnumerable<Message>> GetAllAsync()
        {
            return await _dataset.ToListAsync();
        }

        public async Task<Message?> GetByIdAsync(int id)
        {
            return await _dataset.FindAsync(id);
        }

        public async Task AddAsync(Message entity)
        {
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Message entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Message entity)
        {
            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Message>> GetByAgentIdAsync(int agentId)
        {
            return await _dataset
                .Where(m => m.AgentId == agentId)
                .ToListAsync();
        }

        public async Task DeleteByAgentIdAsync(int agentId)
        {
            var messages = await _dataset
                .Where(m => m.AgentId == agentId)
                .ToListAsync();

            _dataset.RemoveRange(messages);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Message>> GetByRoleAsync(int agentId, string role)
        {
            return await _dataset
                .Where(m => m.AgentId == agentId && m.Role == role)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetRecentByAgentIdAsync(int agentId, int count)
        {
            return await _dataset
                .Where(m => m.AgentId == agentId)
                .OrderByDescending(m => m.Id)
                .Take(count)
                .ToListAsync();
        }
    }
}

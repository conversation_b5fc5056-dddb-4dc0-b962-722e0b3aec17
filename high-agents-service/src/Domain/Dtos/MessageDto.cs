using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace HighAgentsApi.Domain.Dto
{
    public class MessageDto
    {
        [JsonIgnore]
        public int? Id { get; set; }

        [JsonIgnore]
        public string? Role { get; set; }  // "user" ou "assistant", ser<PERSON> definido no backend

        [Required]
        public int AgentId { get; set; }  // Alterado para int

        [Required]
        [DefaultValue("Olá")]
        public required string Content { get; set; }

        [JsonIgnore]
        public DateTime? Timestamp { get; set; }  // Hora de criação da mensagem
    }

    public class MessageDtoList
    {
        [Required]
        public required int Id { get; set; }

        [Required]
        public required string Role { get; set; }

        [Required]
        public int AgentId { get; set; }  // Alterado para int

        [Required]
        [DefaultValue("Olá")]
        public required string Content { get; set; }

        [Required]
        public required DateTime Timestamp { get; set; }  // Hora de envio da mensagem
    }
}

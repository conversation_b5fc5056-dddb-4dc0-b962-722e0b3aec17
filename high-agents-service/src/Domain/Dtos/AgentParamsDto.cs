using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighAgentsApi.Domain.Dtos
{
    public class AgentParamsDto
    {
        [DefaultValue("Default")]
        public string? ParamName { get; set; }

        [DefaultValue("Contexto de atuação em funil de vendas e mentoria")]
        public string? ContextDescription { get; set; }

        [DefaultValue("Estratégico e direto")]
        public string? Tone { get; set; }

        [DefaultValue("Missão principal: ajudar clientes a escalar negócios")]
        public string? MainMission { get; set; }

        [DefaultValue("Agendar reuniões com leads qualificados e gerar valor")]
        public string? Goals { get; set; }

        [DefaultValue("Habilidades de conversação")]
        public string? ConversationSkills { get; set; }

        [DefaultValue("Fluxo natural, perguntas personalizadas, escuta ativa")]
        public string? ConversationGuidelines { get; set; }

        [DefaultValue("Sempre fazer")]
        public string? AlwaysDo { get; set; }

        [DefaultValue("Nunca fazer")]
        public string? NeverDo { get; set; }

        [DefaultValue("Como lidar com situações especiais")]
        public string? SpecialSituationsHandling { get; set; }

        [DefaultValue("Objetivo final")]
        public string? FinalObjective { get; set; }

        [DefaultValue("Nível de criatividade")]
        public string? CreativityLevel { get; set; }

        [DefaultValue("Tamanho da resposta")]
        public string? ResponseLength { get; set; }

        [DefaultValue("Instruções de serviço")]
        public string? ServiceInstructions { get; set; }

        [DefaultValue("Definição de lead qualificado")]
        public string? QualifiedLeadDefinition { get; set; }

        [DefaultValue("Definição de lead desqualificado")]
        public string? DisqualifiedLeadDefinition { get; set; }

        [DefaultValue("Fluxo para leads qualificados")]
        public string? QualifiedFlow { get; set; }

        [DefaultValue("Fluxo para leads desqualificados")]
        public string? DisqualifiedFlow { get; set; }

        // Relacionamentos
        [DefaultValue(1)]
        public int UserId { get; set; }
        
        [DefaultValue(97)]
        public int? AgentId { get; set; }
    }

    public class AgentParamsPaginationRequestDto
    {
        [DefaultValue(1)]
        public int Page { get; set; } = 1;

        [DefaultValue(10)]
        public int PageSize { get; set; } = 10;

        [DefaultValue(2)]
        public int UserId { get; set; }
    }

    public class AgentParamsSearchRequestDto
    {
        [Required]
        public required string ParamName { get; set; }

        [DefaultValue(2)]
        public int UserId { get; set; }
    }

    public class PaginatedResultParams<T>
    {
        public IEnumerable<T> Data { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;
    }
}

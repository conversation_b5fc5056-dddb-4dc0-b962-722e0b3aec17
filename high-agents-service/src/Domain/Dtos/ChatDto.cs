using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace HighAgentsApi.Domain.Dtos
{
    public class ChatDto
    {
        [Required]
        public required int AgentId { get; set; }

        [Required]
        public required string ConversationIdentificator { get; set; }

        [Required]
        [DefaultValue(false)]
        public required bool Ia { get; set; }

        public string? Username { get; set; }
    }

    public class ChatUpdateDto
    {
        public int? AgentId { get; set; }
        public string? ConversationIdentificator { get; set; }
        public bool? Ia { get; set; }
        public string? Username { get; set; }
    }

    public class ChatResponseDto
    {
        public int Id { get; set; }
        public int AgentId { get; set; }
        public string ConversationIdentificator { get; set; } = string.Empty;
        public bool Ia { get; set; }
        public string? Username { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }

        // Campos da última mensagem
        public string? LastMessage { get; set; }
        public DateTime? LastMessageTimestamp { get; set; }
        public string? LastMessageRole { get; set; }
    }

    public class ChatPaginationRequestDto
    {
        [DefaultValue(1)]
        public int Page { get; set; } = 1;

        [DefaultValue(10)]
        public int PageSize { get; set; } = 10;

        [DefaultValue(0)]
        public int AgentId { get; set; }
    }

    public class ChatSearchRequestDto
    {
        public string? ConversationIdentificator { get; set; }
        public int? AgentId { get; set; }
        public bool? Ia { get; set; }
        public string? Username { get; set; }
    }

    public class ToggleIaRequestDto
    {
        [Required]
        public required bool Ia { get; set; }
    }
}

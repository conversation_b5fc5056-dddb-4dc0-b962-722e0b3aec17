using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighAgentsApi.Domain.Dtos
{
    public class BrainDto
    {
        [Required]
        [DefaultValue("Default Brain")]
        public required string BrainName { get; set; }

        [DefaultValue("Root do cérebro")]
        public string? Brainroot { get; set; }

        [DefaultValue("Nome da empresa")]
        public string? CompanyName { get; set; }

        [DefaultValue("Descrição da empresa")]
        public string? CompanyDescription { get; set; }

        [Required]
        [DefaultValue(1)]
        public required int UserId { get; set; }

        [DefaultValue(97)]
        public int? AgentId { get; set; }
    }

    public class BrainUpdateDto
    {
        public string? BrainName { get; set; }
        public string? Brainroot { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyDescription { get; set; }
    }

    public class BrainPaginationRequestDto
    {
        [DefaultValue(1)]
        public int Page { get; set; } = 1;

        [DefaultValue(10)]
        public int PageSize { get; set; } = 10;

        [DefaultValue(1)]
        public int UserId { get; set; }
    }

    public class BrainSearchRequestDto
    {
        [Required]
        public required string Name { get; set; }

        [DefaultValue(1)]
        public int UserId { get; set; }
    }

    public class BrainResponseDto
    {
        public int Id { get; set; }
        public string? BrainName { get; set; }
        public string? Brainroot { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyDescription { get; set; }
        public int? UserId { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}

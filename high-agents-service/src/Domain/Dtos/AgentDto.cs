using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace HighAgentsApi.Domain.Dtos
{
    public class AgentDto
    {

        [Required]
        [DefaultValue("Bender")]
        public required string Name { get; set; }

        [Required]
        [DefaultValue("um assistente")]
        public required string Context { get; set; }

        [Required]
        [DefaultValue(2)]
        public required int userId { get; set; }
        
        [DefaultValue(2)]
        public  int? agentParamsId { get; set; }

    }

    public class AgentUpdateDto
    {

        [Required]
        public required string Name { get; set; }

        [Required]
        public required string Context { get; set; }


        [Required]
        public required string userEmail { get; set; }

        [DefaultValue(2)]
        public  int? agentParamsId { get; set; }

    }
}

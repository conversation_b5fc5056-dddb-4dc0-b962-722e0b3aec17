namespace HighAgentsApi.Domain.Responses;

public interface IResponse<T>
{
    T Value { get; }
    bool Succeeded { get; }
    string[] Errors { get; }
    bool Failed { get; }
    string? FirstError { get; }
}

public class Response<T> : IResponse<T>
{
    public T Value { get; init; }
    public bool Succeeded { get; init; }
    public string[] Errors { get; init; }
    public bool Failed => !Succeeded;
    public string? FirstError => Errors.FirstOrDefault();

    internal Response(T value, bool succeeded, IEnumerable<string> errors)
    {
        Value = value;
        Succeeded = succeeded;
        Errors = errors.ToArray();
    }

    public static Response<T> Success(T value)
    {
        return new Response<T>(value, true, Array.Empty<string>());
    }

    public static Response<T> Failure(IEnumerable<string> errors)
    {
        return new Response<T>(default!, false, errors);
    }

    public static Response<T> Failure(string error)
    {
        return new Response<T>(default!, false, new[] { error });
    }

    public static implicit operator Response<T>(T value) => Success(value);
    public static implicit operator Response<T>(string error) => Failure(error);
}

using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IAgentParamsRepository
    {
        Task<IEnumerable<AgentParams>> GetAllAsync();
        Task<AgentParams?> GetByIdAsync(int id);
        Task AddAsync(AgentParams entity);
        Task UpdateAsync(AgentParams entity);
        Task DeleteAsync(AgentParams entity);

        Task<IEnumerable<AgentParams>> GetByUserIdAsync(int userId);
    }
}

using HighCapital.Core.Domain.Entities.HighAgents;
using HighAgentsApi.Domain.Dtos;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IChatRepository
    {
        Task<IEnumerable<Chat>> GetAllAsync();
        Task<Chat?> GetByIdAsync(int id);
        Task AddAsync(Chat entity);
        Task<Chat> UpdateAsync(Chat entity);
        Task<Chat> DeleteAsync(Chat entity, int? deletedBy = null);

        // Métodos específicos para Chat
        Task<Chat?> GetByConversationIdentificatorAsync(string conversationIdentificator);
        Task<IEnumerable<Chat>> GetByAgentIdAsync(int agentId);
        Task<IEnumerable<Chat>> GetByAgentIdAndConversationAsync(int agentId, string conversationIdentificator);
        Task<Chat?> GetOrCreateByConversationAsync(string conversationIdentificator, int agentId, bool ia, string? username = null);
        Task<bool> ExistsByConversationIdentificatorAsync(string conversationIdentificator);
        Task DeleteByAgentIdAsync(int agentId);
        Task DeleteByConversationAsync(string conversationIdentificator);

        // Métodos de paginação e busca
        Task<PaginatedResult<Chat>> GetChatsPaginatedAsync(ChatPaginationRequestDto request);
        Task<IEnumerable<Chat>> SearchChatsAsync(ChatSearchRequestDto request);
        Task<IEnumerable<Chat>> GetRecentChatsByAgentIdAsync(int agentId, int count);
    }
}

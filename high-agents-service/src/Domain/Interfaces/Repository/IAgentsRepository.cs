using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IAgentRepository
    {
        Task<IEnumerable<Agent>> GetAllAsync();
        Task<Agent?> GetByIdAsync(int id);
        Task AddAsync(Agent entity);
        Task UpdateAsync(Agent entity);
        Task DeleteAsync(Agent entity);

        Task<IEnumerable<Agent>> GetAgentByUserIdAsync(int userId);
    }
}

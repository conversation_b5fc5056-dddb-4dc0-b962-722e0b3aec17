using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IMessageRepository
    {
        Task<IEnumerable<Message>> GetAllAsync();
        Task<Message?> GetByIdAsync(int id);
        Task AddAsync(Message entity);
        Task UpdateAsync(Message entity);
        Task DeleteAsync(Message entity);

        // Todas usando int agora
        Task<IEnumerable<Message>> GetByAgentIdAsync(int agentId);
        Task DeleteByAgentIdAsync(int agentId);
        Task<IEnumerable<Message>> GetByRoleAsync(int agentId, string role);
        Task<IEnumerable<Message>> GetRecentByAgentIdAsync(int agentId, int count);
    }
}

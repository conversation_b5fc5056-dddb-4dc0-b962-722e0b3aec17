using HighAgentsApi.Domain.Dto;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IMessageService
    {
        Task<IEnumerable<MessageDtoList>> GetAllByAgentIdAsync(int agentId, string userEmail);
        Task<IEnumerable<MessageDtoList>> GetByRoleAsync(int agentId, string role);
        Task<IEnumerable<MessageDtoList>> GetRecentByAgentIdAsync(int agentId, int count);
        Task<string> CreateAsync(MessageDto message, string leadName);
        Task<bool> DeleteAsync(int messageId);
        Task<bool> DeleteAllByAgentIdAsync(int agentId);
    }
}

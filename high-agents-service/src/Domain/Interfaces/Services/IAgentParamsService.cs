using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IAgentParamsService
    {
        Task<Result<AgentParams>> GetOneById(int agentParamsId);
        Task<Result<IEnumerable<AgentParams>>> GetAll(int userId);
        Task<Result> Update(AgentParamsDto agentParams, int agentParamsId);
        Task<Result<int>> Create(AgentParamsDto agentParams);
        Task<Result> Delete(int agentParamsId);
    }
}

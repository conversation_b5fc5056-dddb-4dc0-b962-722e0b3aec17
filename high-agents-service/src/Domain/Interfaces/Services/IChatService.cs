using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IChatService
    {
        Task<Result<ChatResponseDto>> GetOneById(int chatId);
        Task<Result<IEnumerable<ChatResponseDto>>> GetAll();
        Task<Result<IEnumerable<ChatResponseDto>>> GetByAgentId(int agentId);
        Task<Result<ChatResponseDto>> GetByConversationIdentificator(string conversationIdentificator);
        Task<Result<ChatResponseDto>> GetOrCreateByConversation(string conversationIdentificator, int agentId, string? username = null);
        Task<Result<Chat>> Create(ChatDto chat);
        Task<Result<Chat>> Update(int chatId, ChatUpdateDto chat);
        Task<Result<Chat>> Delete(int chatId, int? deletedBy = null);
        Task<Result<bool>> DeleteByAgentId(int agentId);
        Task<Result<bool>> DeleteByConversation(string conversationIdentificator);
        Task<Result<bool>> ExistsByConversationIdentificator(string conversationIdentificator);

        // Métodos de paginação e busca
        Task<Result<PaginatedResult<Chat>>> GetChatsPaginated(ChatPaginationRequestDto request);
        Task<Result<IEnumerable<Chat>>> SearchChats(ChatSearchRequestDto request);
        Task<Result<IEnumerable<ChatResponseDto>>> GetRecentChatsByAgentId(int agentId, int count);
    }
}

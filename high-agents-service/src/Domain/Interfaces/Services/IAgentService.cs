using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IAgentService
    {
        Task<Result<Agent>> GetOneById(int agentId);
        Task<Result<IEnumerable<Agent>>> GetAll(int userId);
        Task<Result> Update(AgentUpdateDto agent, int agentId);
        Task<Result<int>> Create(AgentDto agent);
        Task<Result> Delete(int agentId);
    }
}

using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.Entities.HighAgents;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IAgentService
    {
        Task<Result<AgentResponseDto>> GetOneById(int agentId);
        Task<Result<IEnumerable<AgentResponseDto>>> GetAll(int userId);
        Task<Result<AgentResponseDto>> Update(AgentUpdateDto agent, int agentId);
        Task<Result<AgentResponseDto>> Create(AgentDto agent);
        Task<Result<Agent>> Delete(int agentId, int? deletedBy = null);
        Task<Result<PaginatedResult<AgentResponseDto>>> GetAgentsPaginated(AgentPaginationRequestDto request);
        Task<Result<IEnumerable<AgentResponseDto>>> SearchAgentsByName(AgentSearchRequestDto request);
        Task<Result<AgentResponseDto>> GetAgentByName(string name, int userId);
    }
}

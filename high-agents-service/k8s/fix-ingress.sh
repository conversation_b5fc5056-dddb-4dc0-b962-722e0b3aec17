#!/bin/bash

echo "🔍 Diagnosticando problemas do Ingress..."

echo "1. Verificando IP estático..."
IP_STATUS=$(gcloud compute addresses describe high-agents-service-ip --global --format="value(address)" 2>/dev/null)
if [ -z "$IP_STATUS" ]; then
    echo "❌ IP estático não encontrado. Criando..."
    gcloud compute addresses create high-agents-service-ip --global
    IP_ADDRESS=$(gcloud compute addresses describe high-agents-service-ip --global --format="value(address)")
    echo "✅ IP criado: $IP_ADDRESS"
else
    echo "✅ IP encontrado: $IP_STATUS"
    IP_ADDRESS=$IP_STATUS
fi

echo "2. Verificando DNS..."
DNS_IP=$(nslookup api.highagents-dev.highcapital.io | grep "Address:" | tail -1 | awk '{print $2}')
if [ "$DNS_IP" != "$IP_ADDRESS" ]; then
    echo "⚠️  DNS não aponta para o IP correto"
    echo "   DNS atual: $DNS_IP"
    echo "   IP esperado: $IP_ADDRESS"
    echo "   Configure o DNS para apontar api.highagents-dev.highcapital.io para $IP_ADDRESS"
else
    echo "✅ DNS configurado corretamente"
fi

echo "3. Removendo recursos antigos..."
kubectl delete ingress high-agents-service-ingress --ignore-not-found=true
kubectl delete managedcertificate high-agents-service-ssl-cert --ignore-not-found=true

echo "4. Aguardando limpeza..."
sleep 15

echo "5. Criando Ingress temporário sem HTTPS para validação..."
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: high-agents-service-ingress-temp
  labels:
    app: high-agents-service
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "high-agents-service-ip"
spec:
  ingressClassName: "gce"
  rules:
  - host: api.highagents-dev.highcapital.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: high-agents-service
            port:
              number: 80
EOF

echo "6. Aguardando Ingress temporário ficar pronto..."
kubectl wait --for=condition=Ready ingress/high-agents-service-ingress-temp --timeout=300s

echo "7. Testando conectividade HTTP..."
sleep 30
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://api.highagents-dev.highcapital.io/ || echo "000")
if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "404" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo "✅ HTTP funcionando (status: $HTTP_STATUS)"
    
    echo "8. Removendo Ingress temporário..."
    kubectl delete ingress high-agents-service-ingress-temp
    
    echo "9. Criando Ingress final com HTTPS..."
    kubectl apply -f k8s/ingress.yml
    
    echo "✅ Configuração aplicada!"
    echo "⏳ O certificado pode levar 5-10 minutos para ser provisionado"
    
else
    echo "❌ HTTP não está funcionando (status: $HTTP_STATUS)"
    echo "   Verifique se o DNS está correto e propagado"
    echo "   Mantenha o Ingress temporário para debug"
fi

echo "🔍 Status atual:"
kubectl get ingress
kubectl get managedcertificate
echo ""
echo "📋 Para monitorar:"
echo "kubectl describe managedcertificate high-agents-service-ssl-cert"
echo "kubectl get ingress high-agents-service-ingress -o yaml"

apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: high-agents-service-tls
  labels:
    app: high-agents-service
spec:
  secretName: high-agents-service-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - api.highagents-dev.highcapital.io
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: gce

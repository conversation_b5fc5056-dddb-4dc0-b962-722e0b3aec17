# Versão alternativa do Ingress - usar se a versão principal não funcionar
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: high-agents-service-ingress
  labels:
    app: high-agents-service
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "high-agents-service-ip"
    networking.gke.io/managed-certificates: "high-agents-service-ssl-cert"
    kubernetes.io/ingress.allow-http: "true"
spec:
  ingressClassName: "gce"
  rules:
  - host: api.highagents-dev.highcapital.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: high-agents-service
            port:
              number: 80
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: high-agents-service
            port:
              number: 80
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: high-agents-service-ssl-cert
  labels:
    app: high-agents-service
spec:
  domains:
  - api.highagents-dev.highcapital.io

#!/bin/bash

echo "🧹 Limpando recursos antigos..."

# Remover Ingress antigo (se existir)
kubectl delete ingress high-agents-service-ingress --ignore-not-found=true

# Remover ManagedCertificate antigo (se existir)
kubectl delete managedcertificate high-agents-service-ssl-cert --ignore-not-found=true

# Remover secrets TLS órfãos (se existirem)
kubectl delete secret high-agents-service-tls-secret --ignore-not-found=true

echo "⏳ Aguardando limpeza completa..."
sleep 10

echo "🚀 Reaplicando configurações..."

# Aplicar ManagedCertificate primeiro
kubectl apply -f k8s/ingress.yml

echo "✅ Recursos aplicados!"

echo "🔍 Verificando status..."
kubectl get managedcertificate high-agents-service-ssl-cert
kubectl get ingress high-agents-service-ingress

echo "📋 Para monitorar o progresso:"
echo "kubectl describe managedcertificate high-agents-service-ssl-cert"
echo "kubectl describe ingress high-agents-service-ingress"

{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "HighAgents API - COMPLETA", "description": "Collection completa da API HighAgents com todas as rotas: Agent, AgentParams, Brain, Type, Message, Chat e WhatsApp. Atualizada com respostas padronizadas IResponse<T> e controle de IA por chat.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Agent", "description": "Endpoints para gerenciamento de agentes com respostas padronizadas", "item": [{"name": "Create Agent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Vendedor Teste\",\n  \"context\": \"Especialista em vendas de software\",\n  \"userId\": 1,\n  \"agentParamsId\": 1,\n  \"AgentTypeId\": 1,\n  \"AgentBrainId\": 1,\n  \"instanceWhatsappName\": \"vendedor-teste\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/agent", "host": ["{{base_url}}"], "path": ["api", "v1", "agent"]}}}, {"name": "Get All Agents by User", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/agent/userId/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "userId", "{{user_id}}"]}}}, {"name": "Get Agent by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/agent/{{agent_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "{{agent_id}}"]}}}, {"name": "Update Agent", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Vendedor Atualizado\",\n  \"context\": \"Especialista em vendas B2B\",\n  \"agentParamsId\": 2,\n  \"AgentTypeId\": 2,\n  \"AgentBrainId\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/agent/{{agent_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "{{agent_id}}"]}}}, {"name": "Delete Agent", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/agent/{{agent_id}}?deletedBy={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "{{agent_id}}"], "query": [{"key": "deletedBy", "value": "{{user_id}}"}]}}}]}, {"name": "AgentParams", "description": "Endpoints para gerenciamento de parâmetros de agentes com respostas padronizadas", "item": [{"name": "Create AgentParams", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paramName\": \"Vendedor Agressivo\",\n  \"contextDescription\": \"Especialista em vendas de software para empresas\",\n  \"tone\": \"Profissional e direto\",\n  \"mainMission\": \"Converter leads qualificados em vendas\",\n  \"goals\": \"Aumentar vendas em 30% no próximo trimestre\",\n  \"conversationSkills\": \"Persuasão, escuta ativa, identificação de necessidades\",\n  \"conversationGuidelines\": \"Sempre identificar a dor do cliente antes de apresentar soluções\",\n  \"alwaysDo\": \"Fazer perguntas abertas, escutar ativamente, ser empático\",\n  \"neverDo\": \"Ser agressivo demais, interromper o cliente, mentir sobre produtos\",\n  \"specialSituationsHandling\": \"Para objeções, usar a técnica de espelhamento\",\n  \"finalObjective\": \"Fechar a venda mantendo relacionamento\",\n  \"creativityLevel\": \"Médio\",\n  \"responseLength\": \"2-3 parágrafos\",\n  \"serviceInstructions\": \"Focar em benefícios, não apenas características\",\n  \"qualifiedLeadDefinition\": \"Empresa com mais de 50 funcionários, orçamento aprovado\",\n  \"disqualifiedLeadDefinition\": \"Empresa muito pequena, sem orçamento definido\",\n  \"qualifiedFlow\": \"Apresentar proposta personalizada e agendar reunião\",\n  \"disqualifiedFlow\": \"Oferecer material educativo e manter contato futuro\",\n  \"userId\": 1,\n  \"agentId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v1/agentparams", "host": ["{{base_url}}"], "path": ["api", "v1", "agentparams"]}}}, {"name": "Get All AgentParams by User", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/agentparams/userId/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agentparams", "userId", "{{user_id}}"]}}}, {"name": "Get AgentParams by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/agentparams/1", "host": ["{{base_url}}"], "path": ["api", "v1", "agentparams", "1"]}}}, {"name": "Update AgentParams", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paramName\": \"Vendedor Atualizado\",\n  \"tone\": \"Mais amigável e consultivo\",\n  \"mainMission\": \"Construir relacionamentos duradouros com clientes\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/agentparams/1", "host": ["{{base_url}}"], "path": ["api", "v1", "agentparams", "1"]}}}, {"name": "Delete AgentParams", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/agentparams/1?deletedBy={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "agentparams", "1"], "query": [{"key": "deletedBy", "value": "{{user_id}}"}]}}}]}, {"name": "Brain", "description": "Endpoints para gerenciamento de cérebros com respostas padronizadas", "item": [{"name": "Create Brain", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"brainName\": \"Cérebro Vendedor\",\n  \"brainroot\": \"Especialista em vendas e negociação\",\n  \"companyName\": \"TechCorp Solutions\",\n  \"companyDescription\": \"Empresa líder em soluções de software empresarial\",\n  \"agentId\": 1,\n  \"userId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v1/brain", "host": ["{{base_url}}"], "path": ["api", "v1", "brain"]}}}, {"name": "Get All Brains by User", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/brain/userId/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "brain", "userId", "{{user_id}}"]}}}, {"name": "Get Brain by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/brain/1", "host": ["{{base_url}}"], "path": ["api", "v1", "brain", "1"]}}}, {"name": "Update Brain", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"brainName\": \"Cérebro Vendedor Atualizado\",\n  \"companyName\": \"TechCorp Solutions Atualizada\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/brain/1", "host": ["{{base_url}}"], "path": ["api", "v1", "brain", "1"]}}}, {"name": "Delete Brain", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/brain/1?deletedBy={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "brain", "1"], "query": [{"key": "deletedBy", "value": "{{user_id}}"}]}}}]}, {"name": "Type", "description": "Endpoints para gerenciamento de tipos com respostas padronizadas", "item": [{"name": "Create Type", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Vendedor Especialista\",\n  \"description\": \"Você é um assistente de vendas especializado em {{{ContextDescription}}}.\\n\\nSeu tom deve ser: {{{Tone}}}\\nSua missão principal: {{{MainMission}}}\\nSeus objetivos: {{{Goals}}}\\n\\nQuando o lead se chama {{{NOME_DO_LEAD}}}, use esta abordagem:\\n{{{QualifiedFlow}}}\\n\\nSempre lembre-se: {{{AlwaysDo}}}\\nNunca faça: {{{NeverDo}}}\",\n  \"userId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v1/type", "host": ["{{base_url}}"], "path": ["api", "v1", "type"]}}}, {"name": "Get All Types by User", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/type/userId/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "type", "userId", "{{user_id}}"]}}}, {"name": "Get Type by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/type/1", "host": ["{{base_url}}"], "path": ["api", "v1", "type", "1"]}}}, {"name": "Update Type", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Vendedor Especialista Atualizado\",\n  \"description\": \"Template atualizado para vendas especializadas\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/type/1", "host": ["{{base_url}}"], "path": ["api", "v1", "type", "1"]}}}, {"name": "Delete Type", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/type/1?deletedBy={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "type", "1"], "query": [{"key": "deletedBy", "value": "{{user_id}}"}]}}}]}, {"name": "Message", "description": "Endpoints para gerenciamento de mensagens", "item": [{"name": "Create Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"agentId\": 1,\n  \"role\": \"user\",\n  \"content\": \"<PERSON><PERSON><PERSON>, gostaria de saber mais sobre seus produtos\",\n  \"conversationIdentificator\": \"conv_123456\",\n  \"username\": \"<PERSON>\",\n  \"timestamp\": \"2024-01-15T10:30:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/message?leadName={{lead_name}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message"], "query": [{"key": "leadName", "value": "{{lead_name}}"}]}}}, {"name": "Get Messages by Agent", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/message/agent/{{agent_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "agent", "{{agent_id}}"]}}}, {"name": "Get Messages by Conversation", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/message/conversation/{{conversation_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "conversation", "{{conversation_id}}"]}}}, {"name": "Get Messages by Role and Conversation", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/message/conversation/{{conversation_id}}/role/{{role}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "conversation", "{{conversation_id}}", "role", "{{role}}"]}}}, {"name": "Get Recent Messages by Conversation", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/message/conversation/{{conversation_id}}/recent/{{count}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "conversation", "{{conversation_id}}", "recent", "{{count}}"]}}}, {"name": "Delete Message", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/message/{{message_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "{{message_id}}"]}}}, {"name": "Delete All Messages by Agent", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/message/agent/{{agent_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "agent", "{{agent_id}}"]}}}, {"name": "Delete All Messages by Conversation", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/message/conversation/{{conversation_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "conversation", "{{conversation_id}}"]}}}, {"name": "Get Messages Grouped by Conversation", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/message/agent/{{agent_id}}/grouped-by-conversation", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "agent", "{{agent_id}}", "grouped-by-conversation"]}}}, {"name": "Get Conversation Identificators", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/message/agent/{{agent_id}}/conversations", "host": ["{{base_url}}"], "path": ["api", "v1", "message", "agent", "{{agent_id}}", "conversations"]}}}]}, {"name": "Cha<PERSON>", "description": "Endpoints para gerenciamento de chats com controle de IA", "item": [{"name": "Get All Chats", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/chat", "host": ["{{base_url}}"], "path": ["api", "v1", "chat"]}}}, {"name": "Get Chats by Agent", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/chat/agent/{{agent_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "chat", "agent", "{{agent_id}}"]}}}, {"name": "Get Chat by Conversation", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/chat/conversation/{{conversation_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "chat", "conversation", "{{conversation_id}}"]}}}, {"name": "Get or Create Cha<PERSON> by Conversation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"conversationIdentificator\": \"conv_123456\",\n  \"agentId\": 1,\n  \"username\": \"<PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/chat/get-or-create", "host": ["{{base_url}}"], "path": ["api", "v1", "chat", "get-or-create"]}}}, {"name": "Toggle IA (Activate/Deactivate)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ia\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/chat/conversation/{{conversation_id}}/toggle-ia", "host": ["{{base_url}}"], "path": ["api", "v1", "chat", "conversation", "{{conversation_id}}", "toggle-ia"]}}}, {"name": "Search Chats", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/chat/search?conversationIdentificator={{conversation_id}}&agentId={{agent_id}}&ia=true", "host": ["{{base_url}}"], "path": ["api", "v1", "chat", "search"], "query": [{"key": "conversationIdentificator", "value": "{{conversation_id}}"}, {"key": "agentId", "value": "{{agent_id}}"}, {"key": "ia", "value": "true"}]}}}]}, {"name": "WhatsApp", "description": "Endpoints para gerenciamento de instâncias e mensagens do WhatsApp", "item": [{"name": "Test WhatsApp Connection", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/whatsapp/test", "host": ["{{base_url}}"], "path": ["api", "v1", "whatsapp", "test"]}}}, {"name": "Create WhatsApp Instance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"instanceName\": \"vendedor-teste\",\n  \"agentId\": 1,\n  \"qrcode\": true,\n  \"integration\": \"WHATSAPP-BAILEYS\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/whatsapp/instance", "host": ["{{base_url}}"], "path": ["api", "v1", "whatsapp", "instance"]}}}, {"name": "Get QR Code", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/whatsapp/vendedor-teste/qrcode", "host": ["{{base_url}}"], "path": ["api", "v1", "whatsapp", "vendedor-teste", "qrcode"]}}}, {"name": "Get Connection Status", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/whatsapp/vendedor-teste/connection-status", "host": ["{{base_url}}"], "path": ["api", "v1", "whatsapp", "vendedor-teste", "connection-status"]}}}, {"name": "Send WhatsApp Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"number\": \"5511999999999\",\n  \"text\": \"Ol<PERSON>! Como posso ajudá-lo hoje?\",\n  \"mediaType\": \"text\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/whatsapp/vendedor-teste/send-message", "host": ["{{base_url}}"], "path": ["api", "v1", "whatsapp", "vendedor-teste", "send-message"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "user_id", "value": "1", "type": "string"}, {"key": "agent_id", "value": "1", "type": "string"}, {"key": "chat_id", "value": "1", "type": "string"}, {"key": "message_id", "value": "1", "type": "string"}, {"key": "conversation_id", "value": "conv_123456", "type": "string"}, {"key": "lead_name", "value": "<PERSON>", "type": "string"}, {"key": "role", "value": "user", "type": "string"}, {"key": "count", "value": "10", "type": "string"}]}
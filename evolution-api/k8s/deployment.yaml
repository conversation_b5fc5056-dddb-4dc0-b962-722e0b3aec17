apiVersion: apps/v1
kind: Deployment
metadata:
  name: evolution-api
  namespace: default
  labels:
    app: evolution-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: evolution-api
  template:
    metadata:
      labels:
        app: evolution-api
    spec:
      containers:
      - name: evolution-api
        image: atendai/evolution-api:v2.1.1
        ports:
        - containerPort: 8080
          name: http
        env:
        # Secrets
        - name: AUTHENTICATION_API_KEY
          valueFrom:
            secretKeyRef:
              name: evolution-api-secret
              key: AUTHENTICATION_API_KEY
        - name: DATABASE_CONNECTION_URI
          valueFrom:
            secretKeyRef:
              name: evolution-api-secret
              key: DATABASE_CONNECTION_URI
        # ConfigMap values
        - name: DATABASE_ENABLED
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_ENABLED
        - name: DATABASE_PROVIDER
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_PROVIDER
        - name: DATABASE_CONNECTION_CLIENT_NAME
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_CONNECTION_CLIENT_NAME
        - name: DATABASE_SAVE_DATA_INSTANCE
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_DATA_INSTANCE
        - name: DATABASE_SAVE_DATA_NEW_MESSAGE
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_DATA_NEW_MESSAGE
        - name: CONFIG_SESSION_PHONE_VERSION
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: CONFIG_SESSION_PHONE_VERSION
        - name: DATABASE_SAVE_MESSAGE_UPDATE
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_MESSAGE_UPDATE
        - name: DATABASE_SAVE_DATA_CONTACTS
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_DATA_CONTACTS
        - name: DATABASE_SAVE_DATA_CHATS
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_DATA_CHATS
        - name: DATABASE_SAVE_DATA_LABELS
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_DATA_LABELS
        - name: DATABASE_SAVE_DATA_HISTORIC
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_SAVE_DATA_HISTORIC
        - name: CACHE_REDIS_ENABLED
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: CACHE_REDIS_ENABLED
        - name: CACHE_REDIS_URI
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: CACHE_REDIS_URI
        - name: CACHE_REDIS_PREFIX_KEY
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: CACHE_REDIS_PREFIX_KEY
        - name: CACHE_REDIS_SAVE_INSTANCES
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: CACHE_REDIS_SAVE_INSTANCES
        - name: CACHE_LOCAL_ENABLED
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: CACHE_LOCAL_ENABLED
        - name: WEBSOCKET_ENABLED
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: WEBSOCKET_ENABLED
        - name: WEBSOCKET_GLOBAL_EVENTS
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: WEBSOCKET_GLOBAL_EVENTS
        volumeMounts:
        - name: evolution-instances
          mountPath: /evolution/instances
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: evolution-instances
        persistentVolumeClaim:
          claimName: evolution-instances-pvc
      restartPolicy: Always

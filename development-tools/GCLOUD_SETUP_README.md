# Scripts de Configuração do Google Cloud CLI

Este repositório contém scripts multiplataforma para instalar e configurar o Google Cloud CLI e o Cloud SQL Auth Proxy em Windows, macOS e Linux.

## Scripts Disponíveis

### 1. `setup-gcloud.sh` (Bash - Linux/macOS/WSL)

Script em Bash que funciona em sistemas Unix-like.

### 2. `setup-gcloud.ps1` (PowerShell - Windows)

Script em PowerShell otimizado para Windows nativo.

### 3. `setup-gcloud.py` (Python - Multiplataforma)

Script em Python que funciona em qualquer sistema com Python 3.6+.

### 4. `connect-highcapital.sh` (Bash - Conexão Direta)

Script específico para conectar rapidamente à instância High Capital.

### 5. `connect-highcapital.ps1` (PowerShell - Conexão Direta)

Script PowerShell específico para conectar rapidamente à instância High Capital.

## Como Usar

### Linux/macOS

```bash
# Tornar o script executável
chmod +x setup-gcloud.sh

# Executar o script
./setup-gcloud.sh
```

### Windows (PowerShell)

```powershell
# Configurar política de execução (se necessário)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Executar o script
.\setup-gcloud.ps1

# Opções disponíveis:
.\setup-gcloud.ps1 -SkipInstall -ProjectId "meu-projeto-id"
```

### Python (Qualquer Sistema)

```bash
# Executar o script Python
python setup-gcloud.py

# Ou com Python 3 explicitamente
python3 setup-gcloud.py

# Opções disponíveis:
python setup-gcloud.py --skip-install --project-id "meu-projeto-id"
```

### Conexão Rápida (Apenas para High Capital)

```bash
# Linux/macOS - Conectar diretamente
chmod +x connect-highcapital.sh
./connect-highcapital.sh

# Windows - Conectar diretamente
.\connect-highcapital.ps1

# Windows com força (ignora porta em uso)
.\connect-highcapital.ps1 -Force
```

## O que os Scripts Fazem

1. **Detectam o sistema operacional** automaticamente
2. **Instalam o Google Cloud CLI** se não estiver presente:
   - Linux: Via repositório APT oficial
   - macOS: Via Homebrew (se disponível) ou instalação manual
   - Windows: Via instalador oficial ou gerenciadores de pacote (Chocolatey/Scoop)
3. **Fazem login no Google Cloud**:
   - `gcloud auth login` (login interativo)
   - `gcloud auth application-default login` (credenciais padrão para aplicações)
4. **Configuram um projeto** (opcional)
5. **Instalam o Cloud SQL Auth Proxy**
6. **Configuram o PATH** automaticamente

## Pré-requisitos

### Geral

- Conexão com a internet
- Conta do Google Cloud Platform
- Permissões para instalar software

### Linux

- `curl` instalado
- `sudo` access para instalação de pacotes

### macOS

- Homebrew (opcional, mas recomendado)
- Xcode Command Line Tools

### Windows

- PowerShell 5.1 ou superior
- Permissões de administrador (para alguns métodos de instalação)

### Python

- Python 3.6 ou superior
- Módulos padrão (urllib, subprocess, pathlib)

## Usando o Cloud SQL Auth Proxy

### Conexão Específica - High Capital

Os scripts já estão configurados para a instância específica do High Capital:

**Instância**: `highcapital-470117:southamerica-east1:postgres-high-capital`

#### Método 1: Script Automático (Recomendado)

```bash
# Linux/macOS
start-cloud-sql-proxy.sh

# Windows
start-cloud-sql-proxy.bat
# ou
start-cloud-sql-proxy.ps1
```

#### Método 2: Comando Direto

```bash
# Linux/macOS
cloud-sql-proxy -instances=highcapital-470117:southamerica-east1:postgres-high-capital=tcp:5432

# Windows
cloud-sql-proxy.exe -instances=highcapital-470117:southamerica-east1:postgres-high-capital=tcp:5432
```

### Configuração de Connection String

Após iniciar o proxy, conecte usando:

```bash
# String de conexão PostgreSQL para High Capital
postgresql://[seu_usuario]:[sua_senha]@127.0.0.1:5432/[nome_do_database]

# Exemplo com dados reais (substitua pelos seus dados)
postgresql://admin:minhasenha@127.0.0.1:5432/highcapital_db
```

### Configurações de Conexão

- **Host**: `127.0.0.1` (localhost)
- **Port**: `5432` (PostgreSQL padrão)
- **Database**: Nome do seu database
- **Username**: Seu usuário do Cloud SQL
- **Password**: Sua senha do Cloud SQL

### Exemplo para Outras Instâncias (Referência)

```bash
# Formato geral
cloud-sql-proxy -instances=PROJECT_ID:REGION:INSTANCE_NAME=tcp:PORT

# Exemplo para MySQL
cloud-sql-proxy -instances=meu-projeto:us-central1:minha-instancia=tcp:3306

# Com múltiplas instâncias
cloud-sql-proxy \
  -instances=projeto1:us-central1:instancia1=tcp:5432 \
  -instances=projeto2:europe-west1:instancia2=tcp:3307
```

## Verificação da Instalação

```bash
# Verificar instalação do gcloud
gcloud version

# Verificar autenticação
gcloud auth list

# Verificar configuração atual
gcloud config list

# Verificar projetos disponíveis
gcloud projects list

# Testar Cloud SQL Auth Proxy
cloud-sql-proxy --version
```

## Solução de Problemas

### Erro de Permissão (Linux/macOS)

```bash
chmod +x setup-gcloud.sh
```

### Política de Execução (Windows PowerShell)

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### gcloud não encontrado após instalação

- Reinicie o terminal/PowerShell
- Verifique se o PATH foi atualizado corretamente
- No Windows, pode ser necessário reiniciar o sistema

### Cloud SQL Auth Proxy não encontrado

- Verifique se `~/.local/bin` está no PATH
- No Windows, verifique se `%USERPROFILE%\.local\bin` está no PATH

### Erro de autenticação

```bash
# Refazer login
gcloud auth login
gcloud auth application-default login

# Verificar credenciais
gcloud auth list
```

## Configuração Manual (se os scripts falharem)

### 1. Instalar Google Cloud CLI manualmente

- **Linux**: <https://cloud.google.com/sdk/docs/install#deb>
- **macOS**: <https://cloud.google.com/sdk/docs/install#mac>
- **Windows**: <https://cloud.google.com/sdk/docs/install#windows>

### 2. Instalar Cloud SQL Auth Proxy manualmente

```bash
# Linux
curl -o cloud-sql-proxy https://dl.google.com/cloudsql/cloud-sql-proxy.linux.amd64
chmod +x cloud-sql-proxy

# macOS
curl -o cloud-sql-proxy https://dl.google.com/cloudsql/cloud-sql-proxy.darwin.amd64
chmod +x cloud-sql-proxy

# Windows
curl -o cloud-sql-proxy.exe https://dl.google.com/cloudsql/cloud-sql-proxy.windows.amd64.exe
```

## Segurança

- Os scripts fazem login interativo, nunca armazenam credenciais
- As credenciais são gerenciadas pelo próprio Google Cloud CLI
- O Cloud SQL Auth Proxy usa as credenciais configuradas automaticamente

## Suporte

Para problemas específicos:

1. Verifique os logs de erro dos scripts
2. Consulte a documentação oficial do Google Cloud
3. Verifique as permissões do sistema
4. Teste a conectividade de rede

## Links Úteis

- [Documentação Google Cloud CLI](https://cloud.google.com/sdk/docs)
- [Documentação Cloud SQL Auth Proxy](https://cloud.google.com/sql/docs/mysql/sql-proxy)
- [Configuração de Credenciais](https://cloud.google.com/docs/authentication/getting-started)

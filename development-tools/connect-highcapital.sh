#!/bin/bash

# Script para conectar diretamente à instância Cloud SQL High Capital
# Instância: highcapital-470117:southamerica-east1:postgres-high-capital

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações da instância
INSTANCE_CONNECTION_NAME="highcapital-470117:us-east1:postgres-high-capital-dev"
LOCAL_PORT="5432"

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Verificar se cloud-sql-proxy está instalado
check_proxy_installed() {
    if command -v cloud-sql-proxy &> /dev/null; then
        return 0
    else
        print_error "Cloud SQL Auth Proxy não encontrado!"
        print_message "Execute primeiro o script de configuração:"
        print_message "  ./setup-gcloud.sh"
        print_message "Ou instale manualmente:"
        print_message "  curl -o cloud-sql-proxy https://dl.google.com/cloudsql/cloud-sql-proxy.linux.amd64"
        print_message "  chmod +x cloud-sql-proxy"
        print_message "  sudo mv cloud-sql-proxy /usr/local/bin/"
        return 1
    fi
}

# Verificar se está autenticado no gcloud
check_gcloud_auth() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
        print_error "Não há contas autenticadas no gcloud!"
        print_message "Execute primeiro:"
        print_message "  gcloud auth login"
        print_message "  gcloud auth application-default login"
        return 1
    fi
    return 0
}

# Verificar se a porta está em uso
check_port_available() {
    if lsof -Pi :$LOCAL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Porta $LOCAL_PORT já está em uso!"
        print_message "Processos usando a porta:"
        lsof -Pi :$LOCAL_PORT -sTCP:LISTEN
        echo ""
        read -p "Deseja continuar mesmo assim? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_message "Operação cancelada."
            exit 1
        fi
    fi
}

# Função principal
main() {
    print_message "=== Conectando ao Cloud SQL High Capital ==="
    print_message "Instância: $INSTANCE_CONNECTION_NAME"
    print_message "Porta local: $LOCAL_PORT"
    echo ""
    
    # Verificações
    print_step "Verificando pré-requisitos..."
    
    if ! check_proxy_installed; then
        exit 1
    fi
    
    if ! check_gcloud_auth; then
        exit 1
    fi
    
    check_port_available
    
    print_step "Iniciando Cloud SQL Auth Proxy..."
    print_message ""
    print_message "Para conectar ao banco de dados, use:"
    print_message "  Host: 127.0.0.1"
    print_message "  Port: $LOCAL_PORT"
    print_message "  Database: [seu_database]"
    print_message "  Username: [seu_usuario]"
    print_message "  Password: [sua_senha]"
    print_message ""
    print_message "String de conexão PostgreSQL:"
    print_message "  postgresql://[usuario]:[senha]@127.0.0.1:$LOCAL_PORT/[database]"
    print_message ""
    print_message "Exemplo com psql:"
    print_message "  psql -h 127.0.0.1 -p $LOCAL_PORT -U [usuario] -d [database]"
    print_message ""
    print_warning "Pressione Ctrl+C para parar o proxy"
    print_message ""
    
    # Iniciar o proxy
   cloud-sql-proxy $INSTANCE_CONNECTION_NAME --address=127.0.0.1 --port=$LOCAL_PORT

}

# Tratamento de sinais
trap 'print_message "\nProxy interrompido. Conexão encerrada."; exit 0' INT TERM

# Executar função principal
main "$@"

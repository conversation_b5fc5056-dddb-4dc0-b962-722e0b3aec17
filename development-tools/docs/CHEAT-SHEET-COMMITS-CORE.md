# 🚀 Cheat Sheet - Commits para Versionamento Automático

## ⚡ Referência Rápida

### 📈 Incrementar Versão MINOR (1.0.4 → 1.1.0)
```bash
git commit -m "feat: add user authentication"
git commit -m "feat: implement password reset"
git commit -m "feat(api): add new user endpoints"
git commit -m "feat(ui): add dashboard components"
```

### 🔧 Incrementar Versão PATCH (1.0.4 → 1.0.5)
```bash
git commit -m "fix: resolve login bug"
git commit -m "fix: correct validation errors"
git commit -m "perf: optimize database queries"
git commit -m "refactor: improve code structure"
```

### 💥 Incrementar Versão MAJOR (1.0.4 → 2.0.0)
```bash
git commit -m "feat!: change API response format"
git commit -m "fix!: remove deprecated methods"
git commit -m "feat(auth)!: require OAuth for all endpoints"
```

### 📝 NÃO Incrementar Versão
```bash
git commit -m "docs: update README"
git commit -m "test: add unit tests"
git commit -m "chore: update dependencies"
git commit -m "style: fix formatting"
git commit -m "ci: update GitHub Actions"
```

## 🎯 Exemplos por Cenário

### 🆕 Adicionando Nova Funcionalidade
```bash
# ✅ Correto - será minor increment
git commit -m "feat: add user profile management"
git commit -m "feat: implement file upload system"
git commit -m "feat(auth): add two-factor authentication"

# ❌ Incorreto - não será detectado
git commit -m "add user profile management"
git commit -m "new file upload system"
```

### 🐛 Corrigindo Bugs
```bash
# ✅ Correto - será patch increment
git commit -m "fix: resolve memory leak in user service"
git commit -m "fix: correct email validation regex"
git commit -m "fix(database): handle null values properly"

# ❌ Incorreto - não será detectado
git commit -m "resolve memory leak"
git commit -m "correct email validation"
```

### 💔 Breaking Changes (Use com cuidado!)
```bash
# ✅ Correto - será major increment
git commit -m "feat!: change user ID from int to GUID"
git commit -m "fix!: remove support for legacy authentication"

# Ou usando footer
git commit -m "feat: update authentication system

BREAKING CHANGE: All existing tokens will be invalidated"
```

### 📚 Documentação e Manutenção
```bash
# ✅ Correto - NÃO incrementa versão
git commit -m "docs: add API usage examples"
git commit -m "docs(readme): improve installation instructions"
git commit -m "test: increase test coverage for auth module"
git commit -m "chore: update NuGet packages"
git commit -m "style: apply consistent code formatting"
git commit -m "ci: optimize build pipeline"
```

## 🔄 Fluxo de Trabalho Diário

### 1. Feature Branch
```bash
# Criar branch
git checkout -b feat/user-notifications

# Commits durante desenvolvimento
git commit -m "feat: add notification model"
git commit -m "feat: implement email notification service"
git commit -m "test: add notification service tests"
git commit -m "docs: document notification API"

# Push da branch
git push origin feat/user-notifications
```

### 2. Pull Request
```bash
# Título do PR (opcional, mas recomendado)
"feat: add user notification system"

# Descrição pode incluir:
## Changes
- Added notification model
- Implemented email service
- Added comprehensive tests

## Breaking Changes
None

## Version Impact
This PR will increment the minor version (new feature)
```

### 3. Merge para Main
```bash
# Quando merged, o GitHub Actions automaticamente:
# 1. Analisa commits: encontra "feat:" 
# 2. Incrementa minor: 1.0.4 → 1.1.0
# 3. Atualiza Core.csproj
# 4. Cria commit: "chore: bump version to 1.1.0 [skip ci]"
# 5. Cria tag: "v1.1.0"
# 6. Publica pacote NuGet
# 7. Cria GitHub Release
```

## 🎨 Templates de Commit

### Para Features
```bash
feat: <descrição clara da funcionalidade>
feat(<escopo>): <descrição com contexto>
feat!: <descrição da mudança que quebra compatibilidade>
```

### Para Fixes
```bash
fix: <descrição clara do problema resolvido>
fix(<escopo>): <descrição com contexto>
fix!: <descrição da correção que quebra compatibilidade>
```

### Para Outros
```bash
docs: <o que foi documentado>
test: <quais testes foram adicionados>
chore: <que manutenção foi feita>
refactor: <o que foi refatorado>
perf: <que otimização foi feita>
style: <que formatação foi aplicada>
ci: <mudança no CI/CD>
```

## 🔍 Como Verificar Antes do Push

### Verificação Local
```bash
# Windows
.\scripts\version-helper.ps1 -Action semantic -DryRun

# Linux/Mac
./scripts/increment-version.sh --version-type auto --dry-run
```

### O que você verá:
```
Analyzing 5 commits for semantic versioning...
  [MINOR] feat: add user authentication system
  [PATCH] fix: resolve login timeout
  [NONE] docs: update API documentation
  [NONE] test: add auth tests
  [NONE] chore: update dependencies

Version Impact Summary:
  Major (breaking): 0
  Minor (features): 1
  Patch (fixes): 1
  None (chores): 3

Current version: 1.0.4
Next version: 1.1.0  # Minor wins over patch
```

## 🚨 Erros Comuns

### ❌ Commit sem tipo
```bash
# Problema
git commit -m "add user authentication"

# Solução
git commit -m "feat: add user authentication"
```

### ❌ Tipo incorreto
```bash
# Problema - nova feature como fix
git commit -m "fix: add new dashboard"

# Solução
git commit -m "feat: add new dashboard"
```

### ❌ Breaking change não marcado
```bash
# Problema - mudança que quebra compatibilidade
git commit -m "feat: change API response format"

# Solução
git commit -m "feat!: change API response format"
```

## 📊 Monitoramento

### GitHub Actions
- Vá para **Actions** → **Publish NuGet**
- Veja logs da etapa **"Increment Version"**
- Confirme que a versão foi incrementada corretamente

### Verificação Pós-Deploy
```bash
# Ver nova versão
git pull
grep -o '<Version>[^<]*' src/Core/Core.csproj

# Ver tags criadas
git tag -l | tail -5

# Ver releases
# Vá para GitHub → Releases
```

---

## 🎯 Resumo Ultra-Rápido

| Quero | Uso | Resultado |
|-------|-----|-----------|
| Nova funcionalidade | `feat:` | Minor (1.0.4 → 1.1.0) |
| Corrigir bug | `fix:` | Patch (1.0.4 → 1.0.5) |
| Mudança que quebra | `feat!:` ou `fix!:` | Major (1.0.4 → 2.0.0) |
| Documentar | `docs:` | Nenhum |
| Testar | `test:` | Nenhum |
| Manutenção | `chore:` | Nenhum |

**🚀 Agora você domina o versionamento automático!**

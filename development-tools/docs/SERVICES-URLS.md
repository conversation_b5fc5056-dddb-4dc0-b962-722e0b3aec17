## Services URLs

DEVELOPMENT
---

### Authentication Service

- **Base URL**: `http://35.231.95.149`

- **Documentation**: [Authentication Service Documentation](http://35.231.95.149/swagger/index.html)

### High Copt Service

- **Base URL**: `http://34.73.178.15`

- **Documentation**: [High Copt Service Documentation](http://34.73.178.15/docs/)

### High Agents Service

- **Base URL**: `http://34.148.147.144`

- **Documentation**: [High Agents Service Documentation](http://34.148.147.144/docs/)

### High Capital Evolution Service

- **Base URL**: `http://34.75.222.36/`

- **Manager**: [Evolution API Manager](http://34.75.222.36/manager)
- **API Key**: `e8df3c66-e837-469a-899c-441cc0dbde08`
- **Postman**: [Evolution API Documentation](https://www.postman.com/agenciadgcode/evolution-api/overview)

### Messages Integration Service

- **Base URL**: `http://34.138.53.67`
- **Documentation**: [Messages Integration Service Documentation](http://34.138.53.67/docs)

---

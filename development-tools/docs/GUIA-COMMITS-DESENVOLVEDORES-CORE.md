# 📝 Guia de Commits para Desenvolvedores - HighCapital.Core

## 🎯 Objetivo

Este guia ensina como fazer commits que funcionam perfeitamente com nosso sistema de versionamento automático. Seus commits determinam automaticamente se a versão será incrementada como **patch**, **minor** ou **major**.

## 🏷️ Formato de Commit (Conventional Commits)

### Estrutura Básica
```
<tipo>[escopo opcional]: <descrição>

[corpo opcional]

[rodapé opcional]
```

### Exemplos Práticos
```bash
feat: add user authentication system
fix: resolve null pointer exception in login
docs: update API documentation
chore: update dependencies
```

## 📊 Tipos de Commit e Impacto na Versão

| Tipo | Quando Usar | Impacto | Exemplo |
|------|-------------|---------|---------|
| `feat:` | Nova funcionalidade | **Minor** 1.0.4 → 1.1.0 | `feat: add password reset feature` |
| `fix:` | Correção de bug | **Patch** 1.0.4 → 1.0.5 | `fix: resolve login timeout issue` |
| `feat!:` | Breaking change | **Major** 1.0.4 → 2.0.0 | `feat!: change API response format` |
| `fix!:` | Breaking fix | **Major** 1.0.4 → 2.0.0 | `fix!: remove deprecated endpoint` |
| `docs:` | Documentação | **Nenhum** | `docs: update README examples` |
| `style:` | Formatação | **Nenhum** | `style: fix code formatting` |
| `refactor:` | Refatoração | **Patch** 1.0.4 → 1.0.5 | `refactor: improve user service performance` |
| `test:` | Testes | **Nenhum** | `test: add unit tests for auth service` |
| `chore:` | Manutenção | **Nenhum** | `chore: update package dependencies` |
| `ci:` | CI/CD | **Nenhum** | `ci: update GitHub Actions workflow` |
| `perf:` | Performance | **Patch** 1.0.4 → 1.0.5 | `perf: optimize database queries` |

## 🎨 Exemplos Detalhados

### ✅ Commits que geram **Minor Version** (novas funcionalidades)

```bash
# Novas funcionalidades
git commit -m "feat: add JWT token authentication"
git commit -m "feat: implement user role management"
git commit -m "feat(api): add new endpoint for user statistics"
git commit -m "feat(database): add support for PostgreSQL"

# Com escopo
git commit -m "feat(auth): add two-factor authentication"
git commit -m "feat(ui): add dark mode support"
```

### ✅ Commits que geram **Patch Version** (correções)

```bash
# Correções de bugs
git commit -m "fix: resolve memory leak in user service"
git commit -m "fix: correct validation error messages"
git commit -m "fix(database): handle connection timeout properly"

# Melhorias de performance
git commit -m "perf: optimize user query performance"
git commit -m "perf(cache): improve Redis connection pooling"

# Refatorações
git commit -m "refactor: simplify authentication logic"
git commit -m "refactor(models): improve entity relationships"
```

### ⚠️ Commits que geram **Major Version** (breaking changes)

```bash
# Breaking changes (use com cuidado!)
git commit -m "feat!: change user API response structure"
git commit -m "fix!: remove deprecated authentication methods"
git commit -m "feat(api)!: require authentication for all endpoints"

# Ou usando footer
git commit -m "feat: add new authentication system

BREAKING CHANGE: Old authentication tokens are no longer valid"
```

### ✅ Commits que **NÃO afetam** a versão

```bash
# Documentação
git commit -m "docs: update installation guide"
git commit -m "docs(api): add examples to endpoint documentation"

# Testes
git commit -m "test: add integration tests for user service"
git commit -m "test(auth): improve test coverage"

# Manutenção
git commit -m "chore: update dependencies to latest versions"
git commit -m "chore(ci): improve build performance"

# Estilo/formatação
git commit -m "style: fix code formatting and linting issues"
git commit -m "style(components): organize imports"
```

## 🔄 Fluxo de Trabalho Recomendado

### 1. Durante o Desenvolvimento
```bash
# Trabalhe em uma branch de feature
git checkout -b feat/user-authentication

# Faça commits pequenos e focados
git commit -m "feat: add user model and validation"
git commit -m "feat: implement JWT token generation"
git commit -m "test: add unit tests for authentication"

# Push da branch
git push origin feat/user-authentication
```

### 2. Pull Request
```bash
# Crie PR para main
# O título do PR pode seguir o mesmo padrão:
# "feat: add user authentication system"
```

### 3. Merge para Main
```bash
# Quando o PR for merged, o GitHub Actions automaticamente:
# 1. Analisa os commits do merge
# 2. Detecta "feat:" → incremento minor
# 3. Atualiza versão 1.0.4 → 1.1.0
# 4. Cria commit automático: "chore: bump version to 1.1.0 [skip ci]"
# 5. Cria tag "v1.1.0"
# 6. Publica pacote NuGet
# 7. Cria release no GitHub
```

## 🚨 Dicas Importantes

### ✅ Boas Práticas

1. **Seja específico na descrição:**
   ```bash
   # ❌ Ruim
   git commit -m "fix: bug"
   
   # ✅ Bom
   git commit -m "fix: resolve null reference exception in user login"
   ```

2. **Use escopo quando apropriado:**
   ```bash
   git commit -m "feat(auth): add password strength validation"
   git commit -m "fix(database): handle connection timeouts"
   ```

3. **Breaking changes devem ser óbvios:**
   ```bash
   git commit -m "feat!: change user ID from int to GUID"
   
   # Ou com footer
   git commit -m "feat: update user authentication
   
   BREAKING CHANGE: All existing user tokens will be invalidated"
   ```

### ⚠️ Cuidados

1. **Evite commits mistos:**
   ```bash
   # ❌ Ruim - mistura tipos
   git commit -m "feat: add new feature and fix bug"
   
   # ✅ Bom - commits separados
   git commit -m "feat: add user profile feature"
   git commit -m "fix: resolve profile image upload bug"
   ```

2. **Use `[skip ci]` quando necessário:**
   ```bash
   # Para commits que não precisam de CI/CD
   git commit -m "docs: update README [skip ci]"
   git commit -m "chore: fix typo in comments [skip ci]"
   ```

## 🔍 Como Verificar o Resultado

### 1. Localmente (antes do push)
```bash
# Simule o que acontecerá
.\scripts\version-helper.ps1 -Action semantic -DryRun

# Ou no Linux/Mac
./scripts/increment-version.sh --version-type auto --dry-run
```

### 2. No GitHub Actions
1. Vá para **Actions** no repositório
2. Veja o workflow **"Publish NuGet"**
3. Verifique os logs da etapa **"Increment Version"**

### 3. Verificar Resultado
```bash
# Ver tags criadas
git tag -l

# Ver últimos commits
git log --oneline -5

# Ver versão atual
grep -o '<Version>[^<]*' src/Core/Core.csproj
```

## 📈 Exemplos de Cenários Reais

### Cenário 1: Nova Feature
```bash
# Seus commits:
git commit -m "feat: add user profile management"
git commit -m "test: add tests for profile endpoints"
git commit -m "docs: document profile API"

# Resultado automático:
# - Versão: 1.0.4 → 1.1.0 (minor)
# - Tag: v1.1.0
# - Release: criado automaticamente
```

### Cenário 2: Correção de Bug
```bash
# Seus commits:
git commit -m "fix: resolve database connection timeout"
git commit -m "test: add test for connection retry logic"

# Resultado automático:
# - Versão: 1.1.0 → 1.1.1 (patch)
# - Tag: v1.1.1
# - Release: criado automaticamente
```

### Cenário 3: Breaking Change
```bash
# Seus commits:
git commit -m "feat!: change API authentication to OAuth2"
git commit -m "docs: update authentication examples"

# Resultado automático:
# - Versão: 1.1.1 → 2.0.0 (major)
# - Tag: v2.0.0
# - Release: criado automaticamente
```

### Cenário 4: Apenas Documentação
```bash
# Seus commits:
git commit -m "docs: improve API documentation"
git commit -m "docs: add code examples"
git commit -m "chore: update README"

# Resultado automático:
# - Versão: mantém 1.1.1 (nenhum incremento)
# - Sem tag nova
# - Sem release
```

## 🛠️ Troubleshooting

### Problema: Versão não incrementou
**Possíveis causas:**
- Commits não seguem o padrão conventional
- Apenas commits de documentação/chore
- Erro no GitHub Actions

**Solução:**
```bash
# Verifique seus commits
git log --oneline -5

# Teste localmente
.\scripts\version-helper.ps1 -Action semantic -DryRun
```

### Problema: Incremento errado
**Exemplo:** Esperava minor mas foi patch

**Causa:** Commit não tem prefixo correto
```bash
# ❌ Não detectado como feature
git commit -m "add new user feature"

# ✅ Detectado como feature
git commit -m "feat: add new user feature"
```

### Problema: Breaking change não detectado
```bash
# ❌ Não detectado
git commit -m "change API format"

# ✅ Detectado
git commit -m "feat!: change API format"
# ou
git commit -m "feat: change API format

BREAKING CHANGE: API response format has changed"
```

## 📚 Recursos Adicionais

- **Conventional Commits:** https://www.conventionalcommits.org/
- **Semantic Versioning:** https://semver.org/
- **GitHub Actions Docs:** https://docs.github.com/en/actions

## 🎯 Resumo Rápido

**Para incrementar versão:**
- `feat:` = nova funcionalidade = **minor**
- `fix:` = correção = **patch**  
- `feat!:` ou `fix!:` = breaking change = **major**

**Para NÃO incrementar versão:**
- `docs:`, `test:`, `chore:`, `style:`, `ci:`

**Lembre-se:** O sistema analisa **todos os commits** desde o último deploy e escolhe o **maior incremento** necessário.

---

**🚀 Agora você está pronto para usar o versionamento automático!**

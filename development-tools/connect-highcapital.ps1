# Script PowerShell para conectar diretamente à instância Cloud SQL High Capital
# Instância: highcapital-470117:southamerica-east1:postgres-high-capital

param(
    [switch]$Force
)

# Configurações da instância
$instanceConnectionName = "highcapital-470117:southamerica-east1:postgres-high-capital"
$localPort = "5432"

# Função para imprimir mensagens coloridas
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorMessage "[INFO] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorMessage "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorMessage "[ERROR] $Message" "Red"
}

function Write-Step {
    param([string]$Message)
    Write-ColorMessage "[STEP] $Message" "Cyan"
}

# Verificar se cloud-sql-proxy está instalado
function Test-ProxyInstalled {
    try {
        $null = Get-Command .\cloud-sql-proxy.exe -ErrorAction Stop
        return $true
    }
    catch {
        Write-Error "Cloud SQL Auth Proxy não encontrado!"
        Write-Info "Execute primeiro o script de configuração:"
        Write-Info "  .\setup-gcloud.ps1"
        Write-Info "Ou baixe manualmente de:"
        Write-Info "  https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.18.1/cloud-sql-proxy.x64.exe"
        return $false
    }
}

# Verificar se está autenticado no gcloud
function Test-GcloudAuth {
    try {
        $accounts = gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>$null
        if ($accounts -and $accounts.Trim()) {
            return $true
        }
        else {
            Write-Error "Não há contas autenticadas no gcloud!"
            Write-Info "Execute primeiro:"
            Write-Info "  gcloud auth login"
            Write-Info "  gcloud auth application-default login"
            return $false
        }
    }
    catch {
        Write-Error "Erro ao verificar autenticação do gcloud: $($_.Exception.Message)"
        return $false
    }
}

# Verificar se a porta está em uso
function Test-PortAvailable {
    try {
        $connection = Get-NetTCPConnection -LocalPort $localPort -State Listen -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Warning "Porta $localPort já está em uso!"
            Write-Info "Processos usando a porta:"
            Get-Process -Id $connection.OwningProcess | Format-Table Name, Id, ProcessName
            
            if (-not $Force) {
                $response = Read-Host "Deseja continuar mesmo assim? (y/N)"
                if ($response -notmatch '^[Yy]$') {
                    Write-Info "Operação cancelada."
                    exit 1
                }
            }
        }
    }
    catch {
        # Se não conseguir verificar, continua
        Write-Warning "Não foi possível verificar se a porta está em uso."
    }
}

# Função principal
function Main {
    Write-Info "=== Conectando ao Cloud SQL High Capital ==="
    Write-Info "Instância: $instanceConnectionName"
    Write-Info "Porta local: $localPort"
    Write-Host ""
    
    # Verificações
    Write-Step "Verificando pré-requisitos..."
    
    if (-not (Test-ProxyInstalled)) {
        exit 1
    }
    
    if (-not (Test-GcloudAuth)) {
        exit 1
    }
    
    Test-PortAvailable
    
    Write-Step "Iniciando Cloud SQL Auth Proxy..."
    Write-Host ""
    Write-Info "Para conectar ao banco de dados, use:"
    Write-ColorMessage "  Host: 127.0.0.1" "White"
    Write-ColorMessage "  Port: $localPort" "White"
    Write-ColorMessage "  Database: [seu_database]" "White"
    Write-ColorMessage "  Username: [seu_usuario]" "White"
    Write-ColorMessage "  Password: [sua_senha]" "White"
    Write-Host ""
    Write-Info "String de conexão PostgreSQL:"
    Write-ColorMessage "  postgresql://[usuario]:[senha]@127.0.0.1:$localPort/[database]" "White"
    Write-Host ""
    Write-Info "Exemplo com psql (se instalado):"
    Write-ColorMessage "  psql -h 127.0.0.1 -p $localPort -U [usuario] -d [database]" "White"
    Write-Host ""
    Write-Warning "Pressione Ctrl+C para parar o proxy"
    Write-Host ""
    
    try {
        # Iniciar o proxy
        .\cloud-sql-proxy.exe --address 0.0.0.0 --port $localPort $instanceConnectionName
    }
    catch {
        Write-Error "Erro ao iniciar o proxy: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Write-Info "Proxy interrompido. Conexão encerrada."
    }
}

# Tratamento de Ctrl+C
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Write-Info "Proxy interrompido. Conexão encerrada."
}

# Executar função principal
try {
    Main
}
catch {
    Write-Error "Erro durante a execução: $($_.Exception.Message)"
    exit 1
}

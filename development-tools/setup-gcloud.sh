#!/bin/bash

# Script para configurar Google Cloud CLI e Cloud SQL Auth Proxy
# Funciona em Linux, macOS e Windows (via Git Bash/WSL)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Detectar sistema operacional
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        print_error "Sistema operacional não suportado: $OSTYPE"
        exit 1
    fi
    print_message "Sistema operacional detectado: $OS"
}

# Verificar se gcloud já está instalado
check_gcloud_installed() {
    if command -v gcloud &> /dev/null; then
        print_message "Google Cloud CLI já está instalado"
        gcloud version
        return 0
    else
        return 1
    fi
}

# Instalar gcloud no Linux
install_gcloud_linux() {
    print_step "Instalando Google Cloud CLI no Linux..."
    
    # Adicionar repositório do Google Cloud
    echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
    
    # Instalar apt-transport-https e ca-certificates
    sudo apt-get update && sudo apt-get install -y apt-transport-https ca-certificates gnupg
    
    # Adicionar chave GPG do Google Cloud
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
    
    # Atualizar e instalar
    sudo apt-get update && sudo apt-get install -y google-cloud-cli
    
    print_message "Google Cloud CLI instalado com sucesso no Linux"
}

# Instalar gcloud no macOS
install_gcloud_macos() {
    print_step "Instalando Google Cloud CLI no macOS..."
    
    if command -v brew &> /dev/null; then
        print_message "Usando Homebrew para instalar..."
        brew install --cask google-cloud-sdk
    else
        print_message "Homebrew não encontrado. Instalando manualmente..."
        
        # Download e instalação manual
        cd /tmp
        curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-456.0.0-darwin-x86_64.tar.gz
        tar -xzf google-cloud-cli-456.0.0-darwin-x86_64.tar.gz
        ./google-cloud-sdk/install.sh --quiet
        
        # Adicionar ao PATH
        echo 'export PATH="$HOME/google-cloud-sdk/bin:$PATH"' >> ~/.bashrc
        echo 'export PATH="$HOME/google-cloud-sdk/bin:$PATH"' >> ~/.zshrc
        
        # Recarregar PATH
        export PATH="$HOME/google-cloud-sdk/bin:$PATH"
    fi
    
    print_message "Google Cloud CLI instalado com sucesso no macOS"
}

# Instalar gcloud no Windows (via Git Bash/WSL)
install_gcloud_windows() {
    print_step "Instalando Google Cloud CLI no Windows..."
    print_warning "Para Windows, recomendamos usar o instalador oficial do Google Cloud CLI"
    print_message "Baixe e execute: https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe"
    print_message "Ou use o PowerShell script: setup-gcloud.ps1"
    
    # Se estiver no WSL, pode tentar instalação Linux
    if grep -q Microsoft /proc/version 2>/dev/null; then
        print_message "WSL detectado. Tentando instalação Linux..."
        install_gcloud_linux
    else
        print_error "Execute o script PowerShell setup-gcloud.ps1 para Windows nativo"
        exit 1
    fi
}

# Instalar gcloud baseado no OS
install_gcloud() {
    case $OS in
        "linux")
            install_gcloud_linux
            ;;
        "macos")
            install_gcloud_macos
            ;;
        "windows")
            install_gcloud_windows
            ;;
        *)
            print_error "Sistema operacional não suportado para instalação automática"
            exit 1
            ;;
    esac
}

# Fazer login no gcloud
gcloud_login() {
    print_step "Fazendo login no Google Cloud..."
    
    # Login interativo
    gcloud auth login
    
    # Configurar credenciais padrão da aplicação
    print_step "Configurando credenciais padrão da aplicação..."
    gcloud auth application-default login
    
    print_message "Login realizado com sucesso!"
}

# Configurar projeto (opcional)
configure_project() {
    print_step "Configurando projeto do Google Cloud..."
    
    # Listar projetos disponíveis
    print_message "Projetos disponíveis:"
    gcloud projects list
    
    echo ""
    read -p "Digite o ID do projeto que deseja usar (ou pressione Enter para pular): " PROJECT_ID
    
    if [ ! -z "$PROJECT_ID" ]; then
        gcloud config set project $PROJECT_ID
        print_message "Projeto configurado: $PROJECT_ID"
    else
        print_warning "Projeto não configurado. Você pode configurar depois com: gcloud config set project PROJECT_ID"
    fi
}

# Instalar Cloud SQL Auth Proxy
install_cloud-sql-proxy() {
    print_step "Instalando Cloud SQL Auth Proxy..."

    # Criar diretório para binários locais se não existir
    mkdir -p ~/.local/bin

    case $OS in
        "linux")
            curl -o ~/.local/bin/cloud-sql-proxy https://dl.google.com/cloudsql/cloud-sql-proxy.linux.amd64
            ;;
        "macos")
            curl -o ~/.local/bin/cloud-sql-proxy https://dl.google.com/cloudsql/cloud-sql-proxy.darwin.amd64
            ;;
        "windows")
            curl -o ~/.local/bin/cloud-sql-proxy.exe https://dl.google.com/cloudsql/cloud-sql-proxy.windows.amd64.exe
            ;;
    esac

    # Tornar executável
    chmod +x ~/.local/bin/cloud-sql-proxy*

    # Adicionar ao PATH se necessário
    if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.zshrc
        export PATH="$HOME/.local/bin:$PATH"
    fi

    print_message "Cloud SQL Auth Proxy instalado com sucesso!"
    print_message "Localização: ~/.local/bin/cloud-sql-proxy"
}

# Configurar e testar conexão com Cloud SQL
setup_cloud_sql_connection() {
    print_step "Configurando conexão com Cloud SQL..."

    # Configurações da instância
    INSTANCE_CONNECTION_NAME="highcapital-470117:southamerica-east1:postgres-high-capital"
    LOCAL_PORT="5432"

    print_message "Instância Cloud SQL: $INSTANCE_CONNECTION_NAME"
    print_message "Porta local: $LOCAL_PORT"

    # Criar script de conexão
    cat > ~/.local/bin/start-cloud-sql-proxy.sh << EOF
#!/bin/bash
# Script para iniciar Cloud SQL Auth Proxy para High Capital
echo "Iniciando Cloud SQL Auth Proxy..."
echo "Instância: $INSTANCE_CONNECTION_NAME"
echo "Porta local: $LOCAL_PORT"
echo ""
echo "Para conectar ao banco, use:"
echo "Host: 127.0.0.1"
echo "Port: $LOCAL_PORT"
echo "Database: [seu_database]"
echo "Username: [seu_usuario]"
echo "Password: [sua_senha]"
echo ""
echo "String de conexão PostgreSQL:"
echo "postgresql://[usuario]:[senha]@127.0.0.1:$LOCAL_PORT/[database]"
echo ""
echo "Pressione Ctrl+C para parar o proxy"
echo ""

cloud-sql-proxy -instances=$INSTANCE_CONNECTION_NAME=tcp:$LOCAL_PORT
EOF

    chmod +x ~/.local/bin/start-cloud-sql-proxy.sh

    print_message "Script de conexão criado: ~/.local/bin/start-cloud-sql-proxy.sh"
    print_message ""
    print_message "Para iniciar o proxy, execute:"
    print_message "  start-cloud-sql-proxy.sh"
    print_message ""
    print_message "Ou execute diretamente:"
    print_message "  cloud-sql-proxy -instances=$INSTANCE_CONNECTION_NAME=tcp:$LOCAL_PORT"
}

# Função principal
main() {
    print_message "=== Configuração do Google Cloud CLI e Cloud SQL Auth Proxy ==="
    
    # Detectar OS
    detect_os
    
    # Verificar se gcloud já está instalado
    if ! check_gcloud_installed; then
        install_gcloud
    fi
    
    # Fazer login
    gcloud_login
    
    # Configurar projeto
    configure_project
    
    # Instalar Cloud SQL Auth Proxy
    install_cloud-sql-proxy

    # Configurar conexão específica
    setup_cloud_sql_connection

    print_message "=== Configuração concluída com sucesso! ==="
    print_message ""
    print_message "Próximos passos:"
    print_message "1. Para conectar ao Cloud SQL High Capital:"
    print_message "   start-cloud-sql-proxy.sh"
    print_message ""
    print_message "2. Ou execute diretamente:"
    print_message "   cloud-sql-proxy -instances=highcapital-470117:southamerica-east1:postgres-high-capital=tcp:5432"
    print_message ""
    print_message "3. Para verificar a configuração:"
    print_message "   gcloud auth list"
    print_message "   gcloud config list"
    print_message ""
    print_message "4. String de conexão PostgreSQL:"
    print_message "   postgresql://[usuario]:[senha]@127.0.0.1:5432/[database]"
    print_message ""
    print_message "5. Para mais informações:"
    print_message "   gcloud --help"
}

# Executar função principal
main "$@"

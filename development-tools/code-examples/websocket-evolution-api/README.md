# Cliente Socket.IO para Evolution API

Este projeto contém um cliente Socket.IO em Node.js que se conecta à Evolution API e faz console.log de todos os eventos recebidos em tempo real.

## Funcionalidades

- ✅ Conexão Socket.IO com a Evolution API
- 📨 Log detalhado de todos os eventos da Evolution API
- 🔄 Reconexão automática em caso de desconexão
- 🌐 Suporte para modo Global e Tradicional
- 📱 Eventos de mensagens, chats, contatos, grupos, etc.
- ❌ Tratamento completo de erros
- 🛑 Graceful shutdown (Ctrl+C)

## Instalação

1. Instale as dependências:
```bash
npm install
```

## Configuração

1. **Configure o servidor e instância** no arquivo `websocket-client.js`:
```javascript
const SERVER_URL = 'http://************'; // IP do servidor Evolution API
const INSTANCE_NAME = 'sua_instancia'; // Nome da sua instância ou null para modo global
```

### Modos de Operação

#### Modo Global
- Configure `INSTANCE_NAME = null`
- Recebe eventos de todas as instâncias
- URL de conexão: `http://************`

#### Modo Tradicional
- Configure `INSTANCE_NAME = 'nome_da_sua_instancia'`
- Recebe eventos apenas da instância específica
- URL de conexão: `http://************/nome_da_sua_instancia`

## Uso

Execute o cliente:
```bash
npm start
```

ou

```bash
node websocket-client.js
```

## Exemplo de Saída

```
🚀 Iniciando cliente Evolution API Socket.IO...
📋 Configurações:
   Servidor: http://************
   Instância: sua_instancia
   URL de conexão: http://************/sua_instancia
────────────────────────────────────────────────────────────
🔌 Conectando ao Socket.IO: http://************/sua_instancia
✅ Conexão Socket.IO estabelecida com sucesso!
📡 Socket ID: abc123def456
📡 Conectado em: 2024-01-15T10:30:45.123Z
────────────────────────────────────────────────────────────

📨 EVENTO EVOLUTION API RECEBIDO:
⏰ Timestamp: 2024-01-15T10:30:46.456Z
🏷️  Evento: message.upsert
📄 Dados:
{
  "key": {
    "remoteJid": "<EMAIL>",
    "fromMe": false,
    "id": "3EB0123456789ABCDEF"
  },
  "message": {
    "conversation": "Olá! Como você está?"
  },
  "messageTimestamp": 1642234567
}
────────────────────────────────────────────────────────────
```

## Eventos Monitorados

O cliente monitora automaticamente todos os eventos da Evolution API, incluindo:

### Eventos de Mensagem
- `message.upsert` - Nova mensagem recebida
- `message.update` - Mensagem atualizada
- `message.delete` - Mensagem deletada

### Eventos de Chat
- `chat.upsert` - Novo chat
- `chat.update` - Chat atualizado
- `chat.delete` - Chat deletado

### Eventos de Contato
- `contact.upsert` - Novo contato
- `contact.update` - Contato atualizado

### Eventos de Grupo
- `group.upsert` - Novo grupo
- `group.update` - Grupo atualizado
- `group.participants.update` - Participantes do grupo atualizados

### Eventos de Conexão
- `connection.update` - Status da conexão
- `qr.updated` - QR Code atualizado
- `qr.code` - Novo QR Code

### E muitos outros eventos...

## Personalização

### Alterar Servidor
```javascript
const SERVER_URL = 'http://seu-servidor.com';
```

### Configurar Timeout e Reconexão
```javascript
const SOCKET_CONFIG = {
    transports: ['websocket'],
    timeout: 20000,
    reconnection: true,
    reconnectionDelay: 5000,
    reconnectionAttempts: 10
};
```

## Exemplos de Uso

O projeto inclui vários exemplos práticos no arquivo `examples.js`:

```bash
# Exemplo 1: Modo Global
node examples.js 1

# Exemplo 2: Modo Tradicional (instância específica)
node examples.js 2

# Exemplo 3: Eventos Filtrados (apenas mensagens)
node examples.js 3

# Exemplo 4: Processamento Customizado
node examples.js 4

# Exemplo 5: Múltiplos Clientes
node examples.js 5
```

## Arquivo de Configuração

Copie `config.example.js` para `config.js` e personalize conforme necessário:

```bash
cp config.example.js config.js
```

## Troubleshooting

### Erro "Invalid namespace"
- Certifique-se de que o servidor Evolution API está rodando
- Tente primeiro o modo global (`INSTANCE_NAME = null`)
- Verifique se o nome da instância está correto

### Erro de conexão
- Verifique se o IP/URL do servidor está correto
- Confirme se a porta está acessível
- Verifique se o WebSocket está habilitado no servidor

## Parar o Cliente

Pressione `Ctrl+C` para parar o cliente de forma segura.

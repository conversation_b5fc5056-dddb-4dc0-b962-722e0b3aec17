const EvolutionAPIClient = require('./websocket-client');

// Exemplo 1: Modo Global (recebe eventos de todas as instâncias)
function exemploModoGlobal() {
    console.log('📡 Exemplo: Modo Global');
    const client = new EvolutionAPIClient('http://************', null);
    client.connect();
    return client;
}

// Exemplo 2: Modo Tradicional (instância específica)
function exemploModoTradicional() {
    console.log('📱 Exemplo: Modo Tradicional');
    const client = new EvolutionAPIClient('http://************', 'minha_instancia');
    client.connect();
    return client;
}

// Exemplo 3: Cliente com eventos filtrados
function exemploEventosFiltrados() {
    console.log('🔍 Exemplo: Eventos Filtrados');
    const client = new EvolutionAPIClient('http://************', null);
    
    // Sobrescrever o método para filtrar apenas mensagens
    const originalHandler = client.handleEvolutionEvent.bind(client);
    client.handleEvolutionEvent = function(eventName, data) {
        // Filtrar apenas eventos de mensagem
        if (eventName.startsWith('message.')) {
            originalHandler(eventName, data);
        }
    };
    
    client.connect();
    return client;
}

// Exemplo 4: Cliente com processamento customizado
function exemploProcessamentoCustomizado() {
    console.log('⚙️ Exemplo: Processamento Customizado');
    const client = new EvolutionAPIClient('http://************', null);
    
    // Sobrescrever o método para processamento customizado
    client.handleEvolutionEvent = function(eventName, data) {
        console.log(`\n🎯 EVENTO CUSTOMIZADO: ${eventName}`);
        
        // Processamento específico por tipo de evento
        switch (eventName) {
            case 'message.upsert':
                console.log('📨 Nova mensagem recebida!');
                if (data && data.message && data.message.conversation) {
                    console.log(`💬 Texto: ${data.message.conversation}`);
                }
                break;
                
            case 'connection.update':
                console.log('🔗 Status de conexão atualizado!');
                if (data && data.state) {
                    console.log(`📊 Estado: ${data.state}`);
                }
                break;
                
            case 'qr.code':
                console.log('📱 Novo QR Code disponível!');
                break;
                
            default:
                console.log(`📄 Dados: ${JSON.stringify(data, null, 2)}`);
        }
        console.log('─'.repeat(50));
    };
    
    client.connect();
    return client;
}

// Exemplo 5: Múltiplos clientes (diferentes instâncias)
function exemploMultiplosClientes() {
    console.log('🔀 Exemplo: Múltiplos Clientes');
    
    const clientes = [
        new EvolutionAPIClient('http://************', null), // Global
        new EvolutionAPIClient('http://************', 'instancia1'),
        new EvolutionAPIClient('http://************', 'instancia2')
    ];
    
    clientes.forEach((client, index) => {
        // Personalizar logs para identificar cada cliente
        const originalHandler = client.handleEvolutionEvent.bind(client);
        client.handleEvolutionEvent = function(eventName, data) {
            console.log(`\n[CLIENTE ${index + 1}] 📨 ${eventName}`);
            console.log(`[CLIENTE ${index + 1}] 📄 ${JSON.stringify(data, null, 2)}`);
            console.log('─'.repeat(50));
        };
        
        client.connect();
    });
    
    return clientes;
}

// Função para executar exemplos
function executarExemplo(numero) {
    console.log(`\n🚀 Executando Exemplo ${numero}...\n`);
    
    switch (numero) {
        case 1:
            return exemploModoGlobal();
        case 2:
            return exemploModoTradicional();
        case 3:
            return exemploEventosFiltrados();
        case 4:
            return exemploProcessamentoCustomizado();
        case 5:
            return exemploMultiplosClientes();
        default:
            console.log('❌ Exemplo não encontrado!');
            console.log('Exemplos disponíveis: 1, 2, 3, 4, 5');
            return null;
    }
}

// Se executado diretamente
if (require.main === module) {
    const exemplo = process.argv[2] || '1';
    const numeroExemplo = parseInt(exemplo);
    
    console.log('🎯 Exemplos de uso do Evolution API Socket.IO Client');
    console.log('1 - Modo Global');
    console.log('2 - Modo Tradicional');
    console.log('3 - Eventos Filtrados');
    console.log('4 - Processamento Customizado');
    console.log('5 - Múltiplos Clientes');
    console.log('\nUso: node examples.js [número_do_exemplo]');
    
    const client = executarExemplo(numeroExemplo);
    
    if (client) {
        // Graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n\n🛑 Parando exemplo...');
            if (Array.isArray(client)) {
                client.forEach(c => c.disconnect());
            } else {
                client.disconnect();
            }
            process.exit(0);
        });
    }
}

module.exports = {
    exemploModoGlobal,
    exemploModoTradicional,
    exemploEventosFiltrados,
    exemploProcessamentoCustomizado,
    exemploMultiplosClientes,
    executarExemplo
};

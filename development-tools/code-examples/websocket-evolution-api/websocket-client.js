const { io } = require('socket.io-client');

// Configuração da Evolution API
const SERVER_URL = 'http://************'; // IP do servidor Evolution API
const INSTANCE_NAME = null; // null para modo global, ou 'nome_instancia' para modo tradicional

// Configuração do Socket.IO
const SOCKET_CONFIG = {
    transports: ['websocket'], // Força uso do WebSocket
    timeout: 20000,
    reconnection: true,
    reconnectionDelay: 5000,
    reconnectionAttempts: 10
};

class EvolutionAPIClient {
    constructor(serverUrl, instanceName = null) {
        this.serverUrl = serverUrl;
        this.instanceName = instanceName;
        this.socket = null;
        this.isConnected = false;

        // URL de conexão baseada no modo (global ou tradicional)
        this.connectionUrl = instanceName ? `${serverUrl}/${instanceName}` : serverUrl;
    }

    connect() {
        try {
            console.log('� Iniciando cliente Evolution API Socket.IO...');
            console.log('📋 Configurações:');
            console.log(`   Servidor: ${this.serverUrl}`);
            console.log(`   Instância: ${this.instanceName || 'Modo Global'}`);
            console.log(`   URL de conexão: ${this.connectionUrl}`);
            console.log('─'.repeat(60));

            console.log(`🔌 Conectando ao Socket.IO: ${this.connectionUrl}`);

            // Criar conexão Socket.IO
            this.socket = io(this.connectionUrl, SOCKET_CONFIG);

            this.setupEventListeners();

        } catch (error) {
            console.error('❌ Erro ao tentar conectar:', error.message);
        }
    }

    setupEventListeners() {
        // Evento: Conexão estabelecida
        this.socket.on('connect', () => {
            this.isConnected = true;
            console.log('✅ Conexão Socket.IO estabelecida com sucesso!');
            console.log(`📡 Socket ID: ${this.socket.id}`);
            console.log(`� Conectado em: ${new Date().toISOString()}`);
            console.log('─'.repeat(60));
        });

        // Evento: Desconexão
        this.socket.on('disconnect', (reason) => {
            this.isConnected = false;
            console.log('\n� DESCONECTADO:');
            console.log('⏰ Timestamp:', new Date().toISOString());
            console.log('📝 Motivo:', reason);
            console.log('─'.repeat(60));
        });

        // Evento: Erro de conexão
        this.socket.on('connect_error', (error) => {
            console.error('\n❌ ERRO DE CONEXÃO:');
            console.error('⏰ Timestamp:', new Date().toISOString());
            console.error('🔥 Erro:', error.message);
            console.error('📝 Descrição:', error.description || 'N/A');
            console.error('🔢 Código:', error.code || 'N/A');
            console.error('─'.repeat(60));
        });

        // Evento: Reconexão
        this.socket.on('reconnect', (attemptNumber) => {
            console.log('\n� RECONECTADO:');
            console.log('⏰ Timestamp:', new Date().toISOString());
            console.log('🔢 Tentativa:', attemptNumber);
            console.log('─'.repeat(60));
        });

        // Evento: Tentativa de reconexão
        this.socket.on('reconnect_attempt', (attemptNumber) => {
            console.log(`🔄 Tentativa de reconexão ${attemptNumber}...`);
        });

        // Evento: Falha na reconexão
        this.socket.on('reconnect_failed', () => {
            console.log('❌ Falha na reconexão. Máximo de tentativas atingido.');
        });

        // Escutar TODOS os eventos possíveis da Evolution API
        this.setupEvolutionAPIEventListeners();
    }

    setupEvolutionAPIEventListeners() {
        // Lista de eventos comuns da Evolution API
        const evolutionEvents = [
            // Eventos de mensagem
            'message.upsert',
            'message.update',
            'message.delete',

            // Eventos de chat
            'chat.upsert',
            'chat.update',
            'chat.delete',

            // Eventos de contato
            'contact.upsert',
            'contact.update',

            // Eventos de presença
            'presence.update',

            // Eventos de grupo
            'group.upsert',
            'group.update',
            'group.participants.update',

            // Eventos de conexão
            'connection.update',
            'qr.updated',
            'qr.code',

            // Eventos de status
            'status.instance',
            'status.find',

            // Eventos de webhook
            'webhook.message',
            'webhook.status',

            // Outros eventos
            'call.upsert',
            'call.update',
            'labels.edit',
            'labels.association'
        ];

        // Registrar listeners para todos os eventos conhecidos
        evolutionEvents.forEach(eventName => {
            this.socket.on(eventName, (data) => {
                this.handleEvolutionEvent(eventName, data);
            });
        });

        // Capturar qualquer outro evento não listado
        this.socket.onAny((eventName, ...args) => {
            if (!evolutionEvents.includes(eventName) &&
                !['connect', 'disconnect', 'connect_error', 'reconnect', 'reconnect_attempt', 'reconnect_failed'].includes(eventName)) {
                this.handleEvolutionEvent(eventName, args);
            }
        });
    }

    handleEvolutionEvent(eventName, data) {
        console.log('\n� EVENTO EVOLUTION API RECEBIDO:');
        console.log('⏰ Timestamp:', new Date().toISOString());
        console.log('🏷️  Evento:', eventName);
        console.log('📄 Dados:');

        try {
            // Se data é um array (do onAny), pega o primeiro elemento
            const eventData = Array.isArray(data) && data.length === 1 ? data[0] : data;
            console.log(JSON.stringify(eventData, null, 2));
        } catch (e) {
            console.log('� Dados (raw):', data);
        }
        console.log('─'.repeat(60));
    }

    // Método para enviar mensagens (se necessário)
    emit(eventName, data) {
        if (this.socket && this.isConnected) {
            console.log('\n📤 ENVIANDO EVENTO:');
            console.log('⏰ Timestamp:', new Date().toISOString());
            console.log('🏷️  Evento:', eventName);
            console.log('📄 Dados:', JSON.stringify(data, null, 2));
            this.socket.emit(eventName, data);
            console.log('─'.repeat(60));
        } else {
            console.log('❌ Socket.IO não está conectado. Não é possível enviar evento.');
        }
    }

    // Método para fechar conexão
    disconnect() {
        if (this.socket) {
            console.log('🔌 Fechando conexão Socket.IO...');
            this.socket.disconnect();
            this.isConnected = false;
        }
    }
}

// Função principal
function main() {
    // Configuração: altere conforme necessário
    const serverUrl = SERVER_URL;
    const instanceName = INSTANCE_NAME; // null para modo global, ou nome da instância para modo tradicional

    console.log('🚀 Iniciando cliente Evolution API Socket.IO...');
    console.log('📋 Configurações:');
    console.log(`   Servidor: ${serverUrl}`);
    console.log(`   Instância: ${instanceName || 'Modo Global'}`);
    console.log('─'.repeat(60));

    const client = new EvolutionAPIClient(serverUrl, instanceName);
    client.connect();

    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n\n🛑 Recebido sinal de interrupção (Ctrl+C)');
        client.disconnect();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n\n🛑 Recebido sinal de término');
        client.disconnect();
        process.exit(0);
    });
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
    main();
}

module.exports = EvolutionAPIClient;

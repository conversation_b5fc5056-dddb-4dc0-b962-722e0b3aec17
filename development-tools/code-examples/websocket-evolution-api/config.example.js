// Exemplo de configuração para Evolution API Socket.IO Client
// Copie este arquivo para config.js e ajuste conforme necessário

module.exports = {
    // Configuração do servidor Evolution API
    server: {
        url: 'http://************', // IP do seu servidor Evolution API
        port: null, // Porta específica se necessário (opcional)
    },

    // Configuração da instância
    instance: {
        name: 'sua_instancia', // Nome da sua instância ou null para modo global
        // name: null, // Descomente para modo global
    },

    // Configuração do Socket.IO
    socketConfig: {
        transports: ['websocket'], // Força uso do WebSocket
        timeout: 20000, // 20 segundos
        reconnection: true,
        reconnectionDelay: 5000, // 5 segundos
        reconnectionAttempts: 10,
        forceNew: true,
    },

    // Configuração de logs
    logging: {
        showTimestamp: true,
        showEventDetails: true,
        showRawData: false, // Mostrar dados brutos além do JSON formatado
        filterEvents: [], // Array de eventos para filtrar (vazio = todos os eventos)
        // filterEvents: ['message.upsert', 'chat.upsert'], // Exemplo: apenas mensagens e chats
    },

    // Eventos específicos da Evolution API para monitorar
    evolutionEvents: [
        // Eventos de mensagem
        'message.upsert',
        'message.update', 
        'message.delete',
        
        // Eventos de chat
        'chat.upsert',
        'chat.update',
        'chat.delete',
        
        // Eventos de contato
        'contact.upsert',
        'contact.update',
        
        // Eventos de presença
        'presence.update',
        
        // Eventos de grupo
        'group.upsert',
        'group.update',
        'group.participants.update',
        
        // Eventos de conexão
        'connection.update',
        'qr.updated',
        'qr.code',
        
        // Eventos de status
        'status.instance',
        'status.find',
        
        // Eventos de webhook
        'webhook.message',
        'webhook.status',
        
        // Outros eventos
        'call.upsert',
        'call.update',
        'labels.edit',
        'labels.association'
    ]
};

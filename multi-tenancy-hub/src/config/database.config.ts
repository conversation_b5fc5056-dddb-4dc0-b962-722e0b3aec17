import { Injectable } from '@nestjs/common';
import * as K<PERSON> from 'knex';

@Injectable()
export class DatabaseConfig {
  private static instance: Knex.Knex;

  static getInstance(): Knex.Knex {
    if (!DatabaseConfig.instance) {
      const config: Knex.Knex.Config = {
        client: 'postgresql',
        connection: {
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432'),
          user: process.env.DB_USER || 'postgres',
          password: process.env.DB_PASSWORD || 'password',
          database: process.env.DB_NAME || 'multi_tenancy_hub_dev',
        },
        pool: {
          min: 2,
          max: 10,
        },
        migrations: {
          tableName: 'knex_migrations',
          directory: './migrations',
        },
        seeds: {
          directory: './seeds',
        },
      };

      DatabaseConfig.instance = Knex.knex(config);
    }

    return DatabaseConfig.instance;
  }

  static async closeConnection(): Promise<void> {
    if (DatabaseConfig.instance) {
      await DatabaseConfig.instance.destroy();
    }
  }
}

export const knex = DatabaseConfig.getInstance();

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { AuditLog } from '../models/audit-log.model';

@Injectable()
export class AuditLogRepository extends BaseRepository<AuditLog> {
  protected tableName = 'audit_logs';

  async findByAdminId(adminId: number): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName).where('admin_id', adminId);
  }

  async findByAction(companyId: number, action: string): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('action', action);
  }

  async findByTableName(companyId: number, tableName: string): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('table_name', tableName);
  }

  async findByRecordId(companyId: number, tableName: string, recordId: number): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('table_name', tableName)
      .where('record_id', recordId);
  }

  async findByDateRange(companyId: number, startDate: Date, endDate: Date): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('created_at', [startDate, endDate]);
  }

  async logAction(
    companyId: number,
    adminId: number | null,
    action: string,
    tableName: string,
    recordId?: number,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Promise<AuditLog> {
    const logData: Partial<AuditLog> = {
      company_id: companyId,
      admin_id: adminId || undefined,
      action,
      table_name: tableName,
      record_id: recordId,
      old_values: oldValues,
      new_values: newValues,
      created_at: new Date()
    };

    return this.create(logData);
  }

  async getRecentActivity(companyId: number, limit: number = 50): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .orderBy('created_at', 'desc')
      .limit(limit);
  }

  async getActivityByAdmin(companyId: number, adminId: number, limit: number = 50): Promise<AuditLog[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('admin_id', adminId)
      .orderBy('created_at', 'desc')
      .limit(limit);
  }
}

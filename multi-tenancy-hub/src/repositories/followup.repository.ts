import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Followup } from '../models/followup.model';

@Injectable()
export class FollowupRepository extends BaseRepository<Followup> {
  protected tableName = 'followups';

  async findByLeadId(leadId: number): Promise<Followup[]> {
    return this.getKnex()(this.tableName).where('lead_id', leadId);
  }

  async findByTeamMemberId(teamMemberId: number): Promise<Followup[]> {
    return this.getKnex()(this.tableName).where('team_member_id', teamMemberId);
  }

  async findByStatus(companyId: number, status: string): Promise<Followup[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', status);
  }

  async findPendingFollowups(companyId: number): Promise<Followup[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', 'pending');
  }

  async findOverdueFollowups(companyId: number): Promise<Followup[]> {
    const currentDate = new Date();
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', 'pending')
      .where('scheduled_date', '<', currentDate);
  }

  async findTodayFollowups(companyId: number): Promise<Followup[]> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('scheduled_date', [startOfDay, endOfDay]);
  }

  async findUpcomingFollowups(companyId: number, days: number = 7): Promise<Followup[]> {
    const currentDate = new Date();
    const futureDate = new Date();
    futureDate.setDate(currentDate.getDate() + days);

    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', 'pending')
      .whereBetween('scheduled_date', [currentDate, futureDate]);
  }

  async updateStatus(id: number, status: string, notes?: string): Promise<Followup> {
    const updateData: any = { status, updated_at: new Date() };
    if (notes) {
      updateData.notes = notes;
    }

    const [result] = await this.getKnex()(this.tableName)
      .where('id', id)
      .update(updateData)
      .returning('*');
    return result;
  }
}

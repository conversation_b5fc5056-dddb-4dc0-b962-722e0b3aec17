import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Admin } from '../models/admin.model';

@Injectable()
export class AdminRepository extends BaseRepository<Admin> {
  protected tableName = 'admins';

  async findByUsername(username: string): Promise<Admin | undefined> {
    return this.getKnex()(this.tableName).where('username', username).first();
  }

  async findByEmail(email: string): Promise<Admin | undefined> {
    return this.getKnex()(this.tableName).where('email', email).first();
  }

  async existsByUsername(username: string): Promise<boolean> {
    const result = await this.getKnex()(this.tableName)
      .where('username', username)
      .count('* as count')
      .first();
    return parseInt(result?.count as string) > 0;
  }

  async findByCompanyIdAndUsername(companyId: number, username: string): Promise<Admin | undefined> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('username', username)
      .first();
  }
}

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Company } from '../models/company.model';

@Injectable()
export class CompanyRepository extends BaseRepository<Company> {
  protected tableName = 'companies';

  async findByName(name: string): Promise<Company | undefined> {
    return this.getKnex()(this.tableName).where('name', name).first();
  }

  async existsByName(name: string): Promise<boolean> {
    const result = await this.getKnex()(this.tableName)
      .where('name', name)
      .count('* as count')
      .first();
    return parseInt(result?.count as string) > 0;
  }
}

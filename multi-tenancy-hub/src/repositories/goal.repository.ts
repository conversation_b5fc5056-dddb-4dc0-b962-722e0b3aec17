import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Goal } from '../models/goal.model';

@Injectable()
export class GoalRepository extends BaseRepository<Goal> {
  protected tableName = 'goals';

  async findByGoalType(companyId: number, goalType: string): Promise<Goal[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('goal_type', goalType);
  }

  async findByPeriodType(companyId: number, periodType: string): Promise<Goal[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('period_type', periodType);
  }

  async findActiveGoals(companyId: number): Promise<Goal[]> {
    const currentDate = new Date();
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('start_date', '<=', currentDate)
      .where('end_date', '>=', currentDate);
  }

  async findExpiredGoals(companyId: number): Promise<Goal[]> {
    const currentDate = new Date();
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('end_date', '<', currentDate);
  }

  async findUpcomingGoals(companyId: number): Promise<Goal[]> {
    const currentDate = new Date();
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('start_date', '>', currentDate);
  }

  async updateCurrentValue(id: number, currentValue: number): Promise<Goal> {
    const [result] = await this.getKnex()(this.tableName)
      .where('id', id)
      .update({ current_value: currentValue, updated_at: new Date() })
      .returning('*');
    return result;
  }

  async getGoalProgress(companyId: number): Promise<any[]> {
    return this.getKnex()(this.tableName)
      .select('*')
      .select(this.getKnex().raw('(current_value / target_value * 100) as progress_percentage'))
      .where('company_id', companyId);
  }
}

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Lead } from '../models/lead.model';

@Injectable()
export class LeadRepository extends BaseRepository<Lead> {
  protected tableName = 'leads';

  async findByEmail(email: string): Promise<Lead | undefined> {
    return this.getKnex()(this.tableName).where('email', email).first();
  }

  async findByStatus(companyId: number, status: string): Promise<Lead[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', status);
  }

  async findBySource(companyId: number, source: string): Promise<Lead[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('source', source);
  }

  async findByName(companyId: number, name: string): Promise<Lead[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('name', 'ilike', `%${name}%`);
  }

  async updateStatus(id: number, status: string): Promise<Lead> {
    const [result] = await this.getKnex()(this.tableName)
      .where('id', id)
      .update({ status, updated_at: new Date() })
      .returning('*');
    return result;
  }

  async getLeadsByDateRange(companyId: number, startDate: Date, endDate: Date): Promise<Lead[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('created_at', [startDate, endDate]);
  }
}

import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Preorder } from '../models/preorder.model';

@Injectable()
export class PreorderRepository extends BaseRepository<Preorder> {
  protected tableName = 'preorders';

  async findByLeadId(leadId: number): Promise<Preorder[]> {
    return this.getKnex()(this.tableName).where('lead_id', leadId);
  }

  async findByProductId(productId: number): Promise<Preorder[]> {
    return this.getKnex()(this.tableName).where('product_id', productId);
  }

  async findByStatus(companyId: number, status: string): Promise<Preorder[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', status);
  }

  async findPendingPreorders(companyId: number): Promise<Preorder[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', 'pending');
  }

  async findConfirmedPreorders(companyId: number): Promise<Preorder[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', 'confirmed');
  }

  async updateStatus(id: number, status: string, notes?: string): Promise<Preorder> {
    const updateData: any = { status, updated_at: new Date() };
    if (notes) {
      updateData.notes = notes;
    }

    const [result] = await this.getKnex()(this.tableName)
      .where('id', id)
      .update(updateData)
      .returning('*');
    return result;
  }

  async getTotalPreorderValue(companyId: number): Promise<number> {
    const result = await this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .sum('amount as total')
      .first();
    return parseFloat(result?.total as string) || 0;
  }

  async getTotalPreorderValueByStatus(companyId: number, status: string): Promise<number> {
    const result = await this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('status', status)
      .sum('amount as total')
      .first();
    return parseFloat(result?.total as string) || 0;
  }

  async getPreordersByDateRange(companyId: number, startDate: Date, endDate: Date): Promise<Preorder[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('created_at', [startDate, endDate]);
  }
}

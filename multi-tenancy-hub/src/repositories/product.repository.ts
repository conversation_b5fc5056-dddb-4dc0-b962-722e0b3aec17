import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Product } from '../models/product.model';

@Injectable()
export class ProductRepository extends BaseRepository<Product> {
  protected tableName = 'products';

  async findByName(companyId: number, name: string): Promise<Product[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('name', 'ilike', `%${name}%`);
  }

  async findByPriceRange(companyId: number, minPrice: number, maxPrice: number): Promise<Product[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('price', [minPrice, maxPrice]);
  }

  async findMostExpensive(companyId: number, limit: number = 10): Promise<Product[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .orderBy('price', 'desc')
      .limit(limit);
  }

  async findCheapest(companyId: number, limit: number = 10): Promise<Product[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .orderBy('price', 'asc')
      .limit(limit);
  }
}

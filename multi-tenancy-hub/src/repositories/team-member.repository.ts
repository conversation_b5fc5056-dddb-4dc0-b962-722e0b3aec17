import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { TeamMember } from '../models/team-member.model';

@Injectable()
export class TeamMemberRepository extends BaseRepository<TeamMember> {
  protected tableName = 'team_members';

  async findByEmail(email: string): Promise<TeamMember | undefined> {
    return this.getKnex()(this.tableName).where('email', email).first();
  }

  async findByPosition(companyId: number, position: string): Promise<TeamMember[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('position', position);
  }

  async findByName(companyId: number, name: string): Promise<TeamMember[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .where('name', 'ilike', `%${name}%`);
  }
}

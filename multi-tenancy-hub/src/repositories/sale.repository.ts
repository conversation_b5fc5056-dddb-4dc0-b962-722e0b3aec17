import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Sale } from '../models/sale.model';

@Injectable()
export class SaleRepository extends BaseRepository<Sale> {
  protected tableName = 'sales';

  async findByLeadId(leadId: number): Promise<Sale[]> {
    return this.getKnex()(this.tableName).where('lead_id', leadId);
  }

  async findByProductId(productId: number): Promise<Sale[]> {
    return this.getKnex()(this.tableName).where('product_id', productId);
  }

  async findByDateRange(companyId: number, startDate: Date, endDate: Date): Promise<Sale[]> {
    return this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('sale_date', [startDate, endDate]);
  }

  async getTotalSalesByCompany(companyId: number): Promise<number> {
    const result = await this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .sum('amount as total')
      .first();
    return parseFloat(result?.total as string) || 0;
  }

  async getTotalSalesByDateRange(companyId: number, startDate: Date, endDate: Date): Promise<number> {
    const result = await this.getKnex()(this.tableName)
      .where('company_id', companyId)
      .whereBetween('sale_date', [startDate, endDate])
      .sum('amount as total')
      .first();
    return parseFloat(result?.total as string) || 0;
  }

  async getTopSellingProducts(companyId: number, limit: number = 10): Promise<any[]> {
    return this.getKnex()(this.tableName)
      .select('product_id')
      .sum('amount as total_sales')
      .count('* as sales_count')
      .where('company_id', companyId)
      .groupBy('product_id')
      .orderBy('total_sales', 'desc')
      .limit(limit);
  }

  async getMonthlySales(companyId: number, year: number): Promise<any[]> {
    return this.getKnex()(this.tableName)
      .select(this.getKnex().raw('EXTRACT(MONTH FROM sale_date) as month'))
      .sum('amount as total_sales')
      .count('* as sales_count')
      .where('company_id', companyId)
      .whereRaw('EXTRACT(YEAR FROM sale_date) = ?', [year])
      .groupByRaw('EXTRACT(MONTH FROM sale_date)')
      .orderByRaw('EXTRACT(MONTH FROM sale_date)');
  }
}

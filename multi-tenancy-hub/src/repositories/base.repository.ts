import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../modules/database/database.service';

@Injectable()
export abstract class BaseRepository<T> {
  protected abstract tableName: string;

  constructor(protected readonly databaseService: DatabaseService) {}

  async findById(id: number): Promise<T | undefined> {
    return this.databaseService.findById<T>(this.tableName, id);
  }

  async findByCompanyId(companyId: number): Promise<T[]> {
    return this.databaseService.findByCompanyId<T>(this.tableName, companyId);
  }

  async create(data: Partial<T>): Promise<T> {
    return this.databaseService.create<T>(this.tableName, data);
  }

  async update(id: number, data: Partial<T>): Promise<T> {
    return this.databaseService.update<T>(this.tableName, id, data);
  }

  async delete(id: number): Promise<boolean> {
    return this.databaseService.delete(this.tableName, id);
  }

  async findAll(limit?: number, offset?: number): Promise<T[]> {
    return this.databaseService.findAll<T>(this.tableName, limit, offset);
  }

  async count(whereClause?: Record<string, any>): Promise<number> {
    return this.databaseService.count(this.tableName, whereClause);
  }

  protected getKnex() {
    return this.databaseService.getKnex();
  }
}

export interface Preorder {
  id: number;
  company_id: number;
  lead_id?: number;
  product_id?: number;
  amount: number;
  status: string; // 'pending', 'confirmed', 'cancelled'
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export class PreorderModel implements Preorder {
  id: number;
  company_id: number;
  lead_id?: number;
  product_id?: number;
  amount: number;
  status: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Preorder>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.lead_id = data.lead_id;
    this.product_id = data.product_id;
    this.amount = data.amount || 0;
    this.status = data.status || 'pending';
    this.notes = data.notes;
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'preorders';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'amount'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        lead_id: { type: 'integer' },
        product_id: { type: 'integer' },
        amount: { type: 'number', minimum: 0 },
        status: { type: 'string', maxLength: 50, enum: ['pending', 'confirmed', 'cancelled'], default: 'pending' },
        notes: { type: 'string' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

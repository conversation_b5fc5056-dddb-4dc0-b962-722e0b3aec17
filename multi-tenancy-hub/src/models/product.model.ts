export interface Product {
  id: number;
  company_id: number;
  name: string;
  price: number;
  created_at: Date;
  updated_at: Date;
}

export class ProductModel implements Product {
  id: number;
  company_id: number;
  name: string;
  price: number;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Product>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.name = data.name || '';
    this.price = data.price || 0;
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'products';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'name', 'price'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        name: { type: 'string', minLength: 1, maxLength: 200 },
        price: { type: 'number', minimum: 0 },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

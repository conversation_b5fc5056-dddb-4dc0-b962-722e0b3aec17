export interface Followup {
  id: number;
  company_id: number;
  lead_id?: number;
  team_member_id?: number;
  title: string;
  description?: string;
  scheduled_date: Date;
  status: string; // 'pending', 'complete', 'declined'
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export class FollowupModel implements Followup {
  id: number;
  company_id: number;
  lead_id?: number;
  team_member_id?: number;
  title: string;
  description?: string;
  scheduled_date: Date;
  status: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Followup>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.lead_id = data.lead_id;
    this.team_member_id = data.team_member_id;
    this.title = data.title || '';
    this.description = data.description;
    this.scheduled_date = data.scheduled_date || new Date();
    this.status = data.status || 'pending';
    this.notes = data.notes;
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'followups';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'title', 'scheduled_date'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        lead_id: { type: 'integer' },
        team_member_id: { type: 'integer' },
        title: { type: 'string', minLength: 1, maxLength: 200 },
        description: { type: 'string' },
        scheduled_date: { type: 'string', format: 'date-time' },
        status: { type: 'string', maxLength: 20, enum: ['pending', 'complete', 'declined'], default: 'pending' },
        notes: { type: 'string' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

export interface TeamMember {
  id: number;
  company_id: number;
  name: string;
  email?: string;
  phone?: string;
  position?: string;
  created_at: Date;
  updated_at: Date;
}

export class TeamMemberModel implements TeamMember {
  id: number;
  company_id: number;
  name: string;
  email?: string;
  phone?: string;
  position?: string;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<TeamMember>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.name = data.name || '';
    this.email = data.email;
    this.phone = data.phone;
    this.position = data.position;
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'team_members';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'name'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        name: { type: 'string', minLength: 1, maxLength: 100 },
        email: { type: 'string', maxLength: 100 },
        phone: { type: 'string', maxLength: 20 },
        position: { type: 'string', maxLength: 100 },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

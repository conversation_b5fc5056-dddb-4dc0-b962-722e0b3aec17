export interface Company {
  id: number;
  name: string;
  created_at: Date;
  updated_at: Date;
}

export class CompanyModel implements Company {
  id: number;
  name: string;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Company>) {
    this.id = data.id || 0;
    this.name = data.name || '';
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'companies';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name'],
      properties: {
        id: { type: 'integer' },
        name: { type: 'string', minLength: 1, maxLength: 100 },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

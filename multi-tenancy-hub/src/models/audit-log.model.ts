export interface AuditLog {
  id: number;
  company_id: number;
  admin_id?: number;
  action: string; // 'create', 'update', 'delete'
  table_name: string;
  record_id?: number;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  created_at: Date;
}

export class AuditLogModel implements AuditLog {
  id: number;
  company_id: number;
  admin_id?: number;
  action: string;
  table_name: string;
  record_id?: number;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  created_at: Date;

  constructor(data: Partial<AuditLog>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.admin_id = data.admin_id;
    this.action = data.action || '';
    this.table_name = data.table_name || '';
    this.record_id = data.record_id;
    this.old_values = data.old_values;
    this.new_values = data.new_values;
    this.created_at = data.created_at || new Date();
  }

  static tableName = 'audit_logs';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'action', 'table_name'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        admin_id: { type: 'integer' },
        action: { type: 'string', maxLength: 50, enum: ['create', 'update', 'delete'] },
        table_name: { type: 'string', maxLength: 50 },
        record_id: { type: 'integer' },
        old_values: { type: 'object' },
        new_values: { type: 'object' },
        created_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

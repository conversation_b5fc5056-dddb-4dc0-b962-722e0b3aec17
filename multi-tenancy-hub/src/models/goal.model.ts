export interface Goal {
  id: number;
  company_id: number;
  title: string;
  description?: string;
  target_value: number;
  current_value: number;
  goal_type: string; // 'sales', 'leads', 'revenue'
  period_type: string; // 'monthly', 'quarterly', 'yearly'
  start_date: Date;
  end_date: Date;
  created_at: Date;
  updated_at: Date;
}

export class GoalModel implements Goal {
  id: number;
  company_id: number;
  title: string;
  description?: string;
  target_value: number;
  current_value: number;
  goal_type: string;
  period_type: string;
  start_date: Date;
  end_date: Date;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Goal>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.title = data.title || '';
    this.description = data.description;
    this.target_value = data.target_value || 0;
    this.current_value = data.current_value || 0;
    this.goal_type = data.goal_type || '';
    this.period_type = data.period_type || '';
    this.start_date = data.start_date || new Date();
    this.end_date = data.end_date || new Date();
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'goals';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'title', 'target_value', 'goal_type', 'period_type', 'start_date', 'end_date'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        title: { type: 'string', minLength: 1, maxLength: 200 },
        description: { type: 'string' },
        target_value: { type: 'number', minimum: 0 },
        current_value: { type: 'number', minimum: 0, default: 0 },
        goal_type: { type: 'string', maxLength: 50, enum: ['sales', 'leads', 'revenue'] },
        period_type: { type: 'string', maxLength: 20, enum: ['monthly', 'quarterly', 'yearly'] },
        start_date: { type: 'string', format: 'date' },
        end_date: { type: 'string', format: 'date' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

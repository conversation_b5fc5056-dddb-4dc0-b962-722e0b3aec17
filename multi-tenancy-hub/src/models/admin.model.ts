export interface Admin {
  id: number;
  company_id: number;
  username: string;
  password_hash: string;
  email?: string;
  created_at: Date;
  updated_at: Date;
}

export class AdminModel implements Admin {
  id: number;
  company_id: number;
  username: string;
  password_hash: string;
  email?: string;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Admin>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.username = data.username || '';
    this.password_hash = data.password_hash || '';
    this.email = data.email;
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'admins';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'username', 'password_hash'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        username: { type: 'string', minLength: 1, maxLength: 50 },
        password_hash: { type: 'string', minLength: 1, maxLength: 255 },
        email: { type: 'string', maxLength: 100 },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

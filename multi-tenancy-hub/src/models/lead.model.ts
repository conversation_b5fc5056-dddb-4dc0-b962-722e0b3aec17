export interface Lead {
  id: number;
  company_id: number;
  name: string;
  email?: string;
  phone?: string;
  social_media?: string;
  source?: string;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export class LeadModel implements Lead {
  id: number;
  company_id: number;
  name: string;
  email?: string;
  phone?: string;
  social_media?: string;
  source?: string;
  status: string;
  created_at: Date;
  updated_at: Date;

  constructor(data: Partial<Lead>) {
    this.id = data.id || 0;
    this.company_id = data.company_id || 0;
    this.name = data.name || '';
    this.email = data.email;
    this.phone = data.phone;
    this.social_media = data.social_media;
    this.source = data.source;
    this.status = data.status || 'new';
    this.created_at = data.created_at || new Date();
    this.updated_at = data.updated_at || new Date();
  }

  static tableName = 'leads';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['company_id', 'name'],
      properties: {
        id: { type: 'integer' },
        company_id: { type: 'integer' },
        name: { type: 'string', minLength: 1, maxLength: 100 },
        email: { type: 'string', maxLength: 100 },
        phone: { type: 'string', maxLength: 20 },
        social_media: { type: 'string', maxLength: 200 },
        source: { type: 'string', maxLength: 100 },
        status: { type: 'string', maxLength: 50, default: 'new' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
      }
    };
  }
}

import { Injectable, OnModuleD<PERSON>roy } from '@nestjs/common';
import { Knex } from 'knex';
import { DatabaseConfig } from '../../config/database.config';

@Injectable()
export class DatabaseService implements OnModuleDestroy {
  private readonly knex: Knex;

  constructor() {
    this.knex = DatabaseConfig.getInstance();
  }

  getKnex(): Knex {
    return this.knex;
  }

  async onModuleDestroy() {
    await DatabaseConfig.closeConnection();
  }

  // Helper methods for common database operations
  async findById<T>(tableName: string, id: number): Promise<T | undefined> {
    return this.knex(tableName).where('id', id).first();
  }

  async findByCompanyId<T>(tableName: string, companyId: number): Promise<T[]> {
    return this.knex(tableName).where('company_id', companyId);
  }

  async create<T>(tableName: string, data: Partial<T>): Promise<T> {
    const [result] = await this.knex(tableName).insert(data).returning('*');
    return result;
  }

  async update<T>(tableName: string, id: number, data: Partial<T>): Promise<T> {
    const [result] = await this.knex(tableName)
      .where('id', id)
      .update({ ...data, updated_at: new Date() })
      .returning('*');
    return result;
  }

  async delete(tableName: string, id: number): Promise<boolean> {
    const deletedRows = await this.knex(tableName).where('id', id).del();
    return deletedRows > 0;
  }

  async findAll<T>(tableName: string, limit?: number, offset?: number): Promise<T[]> {
    let query = this.knex(tableName);
    
    if (limit) {
      query = query.limit(limit);
    }
    
    if (offset) {
      query = query.offset(offset);
    }
    
    return query;
  }

  async count(tableName: string, whereClause?: Record<string, any>): Promise<number> {
    let query = this.knex(tableName);
    
    if (whereClause) {
      query = query.where(whereClause);
    }
    
    const result = await query.count('* as count').first();
    return parseInt(result?.count as string) || 0;
  }
}

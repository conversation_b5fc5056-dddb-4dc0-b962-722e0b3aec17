import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database/database.module';
import {
  CompanyRepository,
  AdminRepository,
  TeamMemberRepository,
  ProductRepository,
  LeadRepository,
  SaleRepository,
  GoalRepository,
  FollowupRepository,
  PreorderRepository,
  AuditLogRepository
} from '../../repositories';

@Module({
  imports: [DatabaseModule],
  providers: [
    CompanyRepository,
    AdminRepository,
    TeamMemberRepository,
    ProductRepository,
    LeadRepository,
    SaleRepository,
    GoalRepository,
    FollowupRepository,
    PreorderRepository,
    AuditLogRepository
  ],
  exports: [
    CompanyRepository,
    AdminRepository,
    TeamMemberRepository,
    ProductRepository,
    LeadRepository,
    SaleRepository,
    GoalRepository,
    FollowupRepository,
    PreorderRepository,
    AuditLogRepository
  ]
})
export class RepositoriesModule {}

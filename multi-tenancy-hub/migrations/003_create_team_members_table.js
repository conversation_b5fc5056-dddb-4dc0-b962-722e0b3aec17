/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('team_members', function(table) {
    table.increments('id').primary();
    table.integer('company_id').unsigned().notNullable();
    table.string('name', 100).notNullable();
    table.string('email', 100);
    table.string('phone', 20);
    table.string('position', 100);
    table.timestamps(true, true);
    
    // Foreign key constraint
    table.foreign('company_id').references('id').inTable('companies').onDelete('CASCADE');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('team_members');
};

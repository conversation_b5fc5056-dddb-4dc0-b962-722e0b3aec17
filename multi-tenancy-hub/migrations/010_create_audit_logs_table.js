/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('audit_logs', function(table) {
    table.increments('id').primary();
    table.integer('company_id').unsigned().notNullable();
    table.integer('admin_id').unsigned();
    table.string('action', 50).notNullable(); // 'create', 'update', 'delete'
    table.string('table_name', 50).notNullable();
    table.integer('record_id');
    table.json('old_values');
    table.json('new_values');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    
    // Foreign key constraints
    table.foreign('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.foreign('admin_id').references('id').inTable('admins').onDelete('SET NULL');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('audit_logs');
};

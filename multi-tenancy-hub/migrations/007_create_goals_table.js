/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('goals', function(table) {
    table.increments('id').primary();
    table.integer('company_id').unsigned().notNullable();
    table.string('title', 200).notNullable();
    table.text('description');
    table.decimal('target_value', 10, 2).notNullable();
    table.decimal('current_value', 10, 2).defaultTo(0);
    table.string('goal_type', 50).notNullable(); // 'sales', 'leads', 'revenue'
    table.string('period_type', 20).notNullable(); // 'monthly', 'quarterly', 'yearly'
    table.date('start_date').notNullable();
    table.date('end_date').notNullable();
    table.timestamps(true, true);
    
    // Foreign key constraint
    table.foreign('company_id').references('id').inTable('companies').onDelete('CASCADE');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('goals');
};

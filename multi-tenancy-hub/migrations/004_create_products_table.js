/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('products', function(table) {
    table.increments('id').primary();
    table.integer('company_id').unsigned().notNullable();
    table.string('name', 200).notNullable();
    table.decimal('price', 10, 2).notNullable();
    table.timestamps(true, true);
    
    // Foreign key constraint
    table.foreign('company_id').references('id').inTable('companies').onDelete('CASCADE');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('products');
};

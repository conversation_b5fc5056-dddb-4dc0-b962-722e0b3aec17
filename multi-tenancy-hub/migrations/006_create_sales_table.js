/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('sales', function(table) {
    table.increments('id').primary();
    table.integer('company_id').unsigned().notNullable();
    table.integer('lead_id').unsigned();
    table.integer('product_id').unsigned();
    table.decimal('amount', 10, 2).notNullable();
    table.date('sale_date').notNullable();
    table.text('notes');
    table.timestamps(true, true);
    
    // Foreign key constraints
    table.foreign('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.foreign('lead_id').references('id').inTable('leads').onDelete('SET NULL');
    table.foreign('product_id').references('id').inTable('products').onDelete('SET NULL');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('sales');
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('followups', function(table) {
    table.increments('id').primary();
    table.integer('company_id').unsigned().notNullable();
    table.integer('lead_id').unsigned();
    table.integer('team_member_id').unsigned();
    table.string('title', 200).notNullable();
    table.text('description');
    table.datetime('scheduled_date').notNullable();
    table.string('status', 20).defaultTo('pending'); // 'pending', 'complete', 'declined'
    table.text('notes');
    table.timestamps(true, true);
    
    // Foreign key constraints
    table.foreign('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.foreign('lead_id').references('id').inTable('leads').onDelete('SET NULL');
    table.foreign('team_member_id').references('id').inTable('team_members').onDelete('SET NULL');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('followups');
};

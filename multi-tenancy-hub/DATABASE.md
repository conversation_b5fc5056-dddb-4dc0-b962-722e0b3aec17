# Database Models and Structure

Este projeto utiliza Knex.js como query builder para PostgreSQL. Todos os models foram criados baseados na estrutura SQLAlchemy fornecida.

## Estrutura dos Models

### 1. Company (Empresa)
- **Tabela**: `companies`
- **Campos**: id, name, created_at, updated_at
- **Relacionamentos**: Possui admins, team_members, products, leads, sales, goals, followups, preorders, audit_logs

### 2. Admin (Administrador)
- **Tabela**: `admins`
- **Campos**: id, company_id, username, password_hash, email, created_at, updated_at
- **Relacionamentos**: Pertence a company, possui audit_logs

### 3. TeamMember (Membro da Equipe)
- **Tabela**: `team_members`
- **Campos**: id, company_id, name, email, phone, position, created_at, updated_at
- **Relacionamentos**: Pertence a company, possui followups

### 4. Product (Produto)
- **Tabela**: `products`
- **Campos**: id, company_id, name, price, created_at, updated_at
- **Relacionamentos**: Pertence a company, possui sales e preorders

### 5. Lead (Lead/Prospecto)
- **Tabela**: `leads`
- **Campos**: id, company_id, name, email, phone, social_media, source, status, created_at, updated_at
- **Relacionamentos**: Pertence a company, possui sales, followups e preorders

### 6. Sale (Venda)
- **Tabela**: `sales`
- **Campos**: id, company_id, lead_id, product_id, amount, sale_date, notes, created_at, updated_at
- **Relacionamentos**: Pertence a company, lead e product

### 7. Goal (Meta)
- **Tabela**: `goals`
- **Campos**: id, company_id, title, description, target_value, current_value, goal_type, period_type, start_date, end_date, created_at, updated_at
- **Relacionamentos**: Pertence a company

### 8. Followup (Acompanhamento)
- **Tabela**: `followups`
- **Campos**: id, company_id, lead_id, team_member_id, title, description, scheduled_date, status, notes, created_at, updated_at
- **Relacionamentos**: Pertence a company, lead e team_member

### 9. Preorder (Pré-venda)
- **Tabela**: `preorders`
- **Campos**: id, company_id, lead_id, product_id, amount, status, notes, created_at, updated_at
- **Relacionamentos**: Pertence a company, lead e product

### 10. AuditLog (Log de Auditoria)
- **Tabela**: `audit_logs`
- **Campos**: id, company_id, admin_id, action, table_name, record_id, old_values, new_values, created_at
- **Relacionamentos**: Pertence a company e admin

## Como usar

### 1. Configurar o banco de dados
```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Configure as variáveis de ambiente no arquivo .env
```

### 2. Executar as migrations
```bash
# Instalar knex globalmente (se não tiver)
npm install -g knex

# Executar as migrations
knex migrate:latest

# Para reverter migrations
knex migrate:rollback
```

### 3. Usar os repositórios
```typescript
import { CompanyRepository } from './repositories/company.repository';

// Exemplo de uso em um service
@Injectable()
export class CompanyService {
  constructor(private readonly companyRepository: CompanyRepository) {}

  async createCompany(name: string) {
    return this.companyRepository.create({ name });
  }

  async getCompanyById(id: number) {
    return this.companyRepository.findById(id);
  }
}
```

## Funcionalidades dos Repositórios

Cada repositório herda de `BaseRepository` que fornece métodos básicos:
- `findById(id)` - Buscar por ID
- `findByCompanyId(companyId)` - Buscar por empresa
- `create(data)` - Criar novo registro
- `update(id, data)` - Atualizar registro
- `delete(id)` - Deletar registro
- `findAll(limit?, offset?)` - Buscar todos
- `count(whereClause?)` - Contar registros

Além disso, cada repositório possui métodos específicos para suas necessidades.

## Multi-tenancy

O sistema é projetado para multi-tenancy, onde cada empresa (`company`) tem seus próprios dados isolados. Todos os models (exceto `Company` e `Admin`) possuem `company_id` para garantir o isolamento dos dados.

## Auditoria

O sistema possui um sistema de auditoria através da tabela `audit_logs` que registra todas as ações de criação, atualização e exclusão realizadas pelos administradores.

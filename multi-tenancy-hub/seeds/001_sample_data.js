/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('audit_logs').del();
  await knex('preorders').del();
  await knex('followups').del();
  await knex('goals').del();
  await knex('sales').del();
  await knex('leads').del();
  await knex('products').del();
  await knex('team_members').del();
  await knex('admins').del();
  await knex('companies').del();

  // Insert sample companies
  const [company1, company2] = await knex('companies').insert([
    { name: 'Tech Solutions Inc.' },
    { name: 'Marketing Pro Ltd.' }
  ]).returning('*');

  // Insert sample admins
  await knex('admins').insert([
    {
      company_id: company1.id,
      username: 'admin1',
      password_hash: '$2b$10$example.hash.here', // In real app, use bcrypt
      email: '<EMAIL>'
    },
    {
      company_id: company2.id,
      username: 'admin2',
      password_hash: '$2b$10$example.hash.here',
      email: '<EMAIL>'
    }
  ]);

  // Insert sample team members
  await knex('team_members').insert([
    {
      company_id: company1.id,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+**********',
      position: 'Sales Manager'
    },
    {
      company_id: company1.id,
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+**********',
      position: 'Account Executive'
    },
    {
      company_id: company2.id,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      phone: '+**********',
      position: 'Marketing Specialist'
    }
  ]);

  // Insert sample products
  await knex('products').insert([
    {
      company_id: company1.id,
      name: 'Software License - Basic',
      price: 99.99
    },
    {
      company_id: company1.id,
      name: 'Software License - Premium',
      price: 199.99
    },
    {
      company_id: company2.id,
      name: 'Marketing Campaign - Starter',
      price: 500.00
    },
    {
      company_id: company2.id,
      name: 'Marketing Campaign - Professional',
      price: 1500.00
    }
  ]);

  // Insert sample leads
  await knex('leads').insert([
    {
      company_id: company1.id,
      name: 'Alice Cooper',
      email: '<EMAIL>',
      phone: '+**********',
      source: 'Website',
      status: 'new'
    },
    {
      company_id: company1.id,
      name: 'Charlie Brown',
      email: '<EMAIL>',
      phone: '+**********',
      source: 'Referral',
      status: 'contacted'
    },
    {
      company_id: company2.id,
      name: 'Diana Prince',
      email: '<EMAIL>',
      phone: '+**********',
      source: 'Social Media',
      status: 'qualified'
    }
  ]);

  // Insert sample goals
  await knex('goals').insert([
    {
      company_id: company1.id,
      title: 'Q1 Sales Target',
      description: 'Achieve $50,000 in sales for Q1',
      target_value: 50000.00,
      current_value: 15000.00,
      goal_type: 'revenue',
      period_type: 'quarterly',
      start_date: '2024-01-01',
      end_date: '2024-03-31'
    },
    {
      company_id: company2.id,
      title: 'Monthly Lead Generation',
      description: 'Generate 100 new leads this month',
      target_value: 100,
      current_value: 35,
      goal_type: 'leads',
      period_type: 'monthly',
      start_date: '2024-01-01',
      end_date: '2024-01-31'
    }
  ]);
};

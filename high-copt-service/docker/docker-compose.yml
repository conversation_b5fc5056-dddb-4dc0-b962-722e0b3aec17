version: "3.8"

services:
  firestore:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    container_name: highcopy-firestore
    command: >
      sh -c "gcloud config set project highcopy &&
             gcloud emulators firestore start --host-port=0.0.0.0:8080 --quiet"
    ports:
      - "8080:8080"
    environment:
      - FIRESTORE_PROJECT_ID=highcopy
      - FIRESTORE_EMULATOR_USER=high-copy
      - FIRESTORE_EMULATOR_PASSWORD=highcopy123
    volumes:
      - ./firestore-data:/firestore-data

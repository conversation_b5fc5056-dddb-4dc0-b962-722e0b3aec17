using Domain.Interfaces.Repositories;
using Infrastructure.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services)
    {
        // Repositories
        services.AddScoped<IExpertRepository, ExpertRepository>();
        services.AddScoped<ICampaignRepository, CampaingRepository>();
        services.AddScoped<IProductRepository, ProductRepository>();
        services.AddScoped<IPublicRepository, PublicRepository>();
        services.AddScoped<IMessageRepository, MessageRepository>();
        
        return services;
    }
}
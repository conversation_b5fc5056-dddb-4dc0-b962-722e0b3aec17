using Domain.Interfaces;
using Google.Cloud.Firestore;

namespace Infrastructure.Repositories;

public abstract class BaseRepository<T> where T : class, IEntity
{
    protected readonly FirestoreDb _firestoreDb;
    protected readonly CollectionReference _collection;

    protected BaseRepository(FirestoreDb firestoreDb, string collectionName)
    {
        _firestoreDb = firestoreDb;
        _collection = _firestoreDb.Collection(collectionName);
    }

    public async Task<T> GetByIdAsync(int id)
    {
        var snapshot = await _collection.Document(id.ToString()).GetSnapshotAsync();
        if (!snapshot.Exists)
        {
            throw new KeyNotFoundException($"{typeof(T).Name} with Id {id} not found.");
        }
        return snapshot.ConvertTo<T>();
    }

    public async Task<T> GetByExternalIdAsync(string externalId)
    {
        var query = _collection.WhereEqualTo("ExternalId", externalId);
        var snapshot = await query.GetSnapshotAsync();
        if (snapshot.Count == 0)
        {
            throw new KeyNotFoundException($"{typeof(T).Name} with ExternalId {externalId} not found.");
        }
        return snapshot.Documents[0].ConvertTo<T>();
    }

    public async Task<IEnumerable<T>> GetAllAsync()
    {
        var snapshot = await _collection.GetSnapshotAsync();
        return snapshot.Documents.Select(s => s.ConvertTo<T>());
    }

    public async Task AddAsync(T entity)
    {
        // Se Id não foi fornecido, gerar um automaticamente usando Firestore
        if (entity.Id == 0)
        {
            // Gerar documento com ID automático do Firestore
            var docRef = await _collection.AddAsync(entity);
            // Usar o ID gerado como hash para criar um ID numérico único
            entity.Id = Math.Abs(docRef.Id.GetHashCode());
            // Atualizar o documento com o ID gerado
            await docRef.SetAsync(entity);
        }
        else
        {
            // Usar Id fornecido como chave do documento
            await _collection.Document(entity.Id.ToString()).SetAsync(entity);
        }
    }

    public async Task UpdateAsync(T entity)
    {
        // Usar Id como chave primária do documento no Firestore
        await _collection.Document(entity.Id.ToString()).SetAsync(entity, SetOptions.MergeAll);
    }

    public async Task DeleteAsync(int id)
    {
        await _collection.Document(id.ToString()).DeleteAsync();
    }


    public Task SaveChangesAsync()
    {
        // Firestore does not have a SaveChangesAsync method
        return Task.CompletedTask;
    }
}
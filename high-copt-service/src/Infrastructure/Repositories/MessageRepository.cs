using Domain.Entities;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;
using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Repositories;

public class MessageRepository : IMessageRepository
{
    private readonly FirestoreDb _firestoreDb;
    private readonly IIdGenerationService _idGenerationService;
    private readonly ILogger<MessageRepository> _logger;
    private const string CollectionName = "messages";

    public MessageRepository(FirestoreDb firestoreDb, IIdGenerationService idGenerationService, ILogger<MessageRepository> logger)
    {
        _firestoreDb = firestoreDb;
        _idGenerationService = idGenerationService;
        _logger = logger;
    }

    public async Task<Message> CreateAsync(Message message)
    {
        try
        {
            var allMessages = await GetAllMessagesInternalAsync();
            message.Id = await _idGenerationService.GenerateUniqueIdAsync<Message>(() => Task.FromResult(allMessages));
            message.ExternalId = _idGenerationService.GenerateExternalId();
            message.CreatedAt = DateTime.UtcNow;
            message.UpdatedAt = DateTime.UtcNow;

            var docRef = _firestoreDb.Collection(CollectionName).Document(message.Id.ToString());
            await docRef.SetAsync(message);

            _logger.LogInformation("Message created successfully with ID: {MessageId}", message.Id);
            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating message");
            throw;
        }
    }

    public async Task<Message?> GetByIdAsync(int id)
    {
        try
        {
            var docRef = _firestoreDb.Collection(CollectionName).Document(id.ToString());
            var snapshot = await docRef.GetSnapshotAsync();

            if (!snapshot.Exists)
            {
                return null;
            }

            return snapshot.ConvertTo<Message>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message by ID: {MessageId}", id);
            throw;
        }
    }

    public async Task<Message?> GetByExternalIdAsync(string externalId)
    {
        try
        {
            var query = _firestoreDb.Collection(CollectionName)
                .WhereEqualTo("external_id", externalId)
                .Limit(1);

            var snapshot = await query.GetSnapshotAsync();
            var document = snapshot.Documents.FirstOrDefault();

            return document?.ConvertTo<Message>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message by external ID: {ExternalId}", externalId);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> GetByUserAndDateRangeAsync(string userId, DateTime startDate, DateTime endDate)
    {
        try
        {
            var query = _firestoreDb.Collection(CollectionName)
                .WhereGreaterThanOrEqualTo("created_at", Timestamp.FromDateTime(startDate.ToUniversalTime()))
                .WhereLessThanOrEqualTo("created_at", Timestamp.FromDateTime(endDate.ToUniversalTime()))
                .WhereEqualTo("is_active", true)
                .OrderByDescending("created_at");

            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<Message>()).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages by user and date range");
            throw;
        }
    }

    public async Task<Message> UpdateAsync(Message message)
    {
        try
        {
            message.UpdatedAt = DateTime.UtcNow;

            var docRef = _firestoreDb.Collection(CollectionName).Document(message.Id.ToString());
            await docRef.SetAsync(message, SetOptions.MergeAll);

            _logger.LogInformation("Message updated successfully with ID: {MessageId}", message.Id);
            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating message with ID: {MessageId}", message.Id);
            throw;
        }
    }

    public async Task DeleteAsync(int id)
    {
        try
        {
            var message = await GetByIdAsync(id);
            if (message == null)
            {
                throw new KeyNotFoundException($"Message with ID {id} not found");
            }

            message.IsActive = false;
            await UpdateAsync(message);

            _logger.LogInformation("Message soft deleted with ID: {MessageId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message with ID: {MessageId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<Message>> GetRecentMessagesAsync(int count = 50)
    {
        try
        {
            var query = _firestoreDb.Collection(CollectionName)
                .WhereEqualTo("is_active", true)
                .OrderByDescending("created_at")
                .Limit(count);

            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<Message>()).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent messages");
            throw;
        }
    }

    private static string GenerateExternalId()
    {
        return $"msg_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N")[..8]}";
    }

    private async Task<IEnumerable<Message>> GetAllMessagesInternalAsync()
    {
        try
        {
            var query = _firestoreDb.Collection(CollectionName);
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<Message>()).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all messages for ID generation");
            return new List<Message>();
        }
    }
}

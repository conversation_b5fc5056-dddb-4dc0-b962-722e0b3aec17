# Sistema de IA com SOLID e Clean Architecture

## 📋 Visão Geral

Sistema completo de integração com múltiplos provedores de IA (ChatGPT e Gemini) implementando todos os princípios SOLID e Clean Architecture.

## 🏗️ Arquitetura

### Princípios SOLID Implementados:

1. **SRP (Single Responsibility Principle)**
   - `ChatGptService`: Comunicação exclusiva com OpenAI
   - `GeminiService`: Comunicação exclusiva com Google Gemini
   - `AiOrchestrator`: Orquestração e seleção de provedores
   - `AiServiceFactory`: Criação de instâncias dos serviços

2. **OCP (Open/Closed Principle)**
   - Sistema extensível para novos provedores de IA
   - Adicione novos provedores implementando `IGenerativeAiService`

3. **LSP (Liskov Substitution Principle)**
   - Todos os serviços são intercambiáveis via interface comum

4. **ISP (Interface Segregation Principle)**
   - Interfaces focadas e específicas para cada responsabilidade

5. **DIP (Dependency Inversion Principle)**
   - Dependências via abstrações (interfaces)
   - Injeção de dependência configurada

### Clean Architecture:

```
📁 Domain/
  └── Interfaces/Services/
      └── IGenerativeAiService.cs

📁 Application/
  ├── Models/AI/
  │   └── ChatGptModels.cs
  ├── Services/AI/
  │   ├── Requests/
  │   │   ├── ChatGptService.cs
  │   │   └── GeminiService.cs
  │   ├── AiServiceFactory.cs
  │   └── AiOrchestrator.cs
  └── Setups/
      └── AiServicesConfiguration.cs

📁 Api/
  └── Controllers/
      └── ContentController.cs
```

## 🚀 Como Usar

### 1. Configuração

No `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "OPENAI_API_KEY": "sua-chave-openai",
    "GEMINI_API_KEY": "sua-chave-gemini"
  }
}
```

### 2. Registro de Serviços

No `Program.cs`:
```csharp
using Application.Services.Setups;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddAiServices(); // ✅ Já configurado
```

### 3. Uso via API

#### Gerar Conteúdo (ChatGPT padrão):
```http
POST /api/content/generate
Content-Type: application/json

{
  "prompt": "Escreva um texto sobre Clean Architecture"
}
```

#### Gerar Conteúdo (Gemini específico):
```http
POST /api/content/generate
Content-Type: application/json

{
  "prompt": "Explique os princípios SOLID",
  "provider": "gemini"
}
```

#### Listar Provedores Disponíveis:
```http
GET /api/content/providers
```

### 4. Uso via Injeção de Dependência

```csharp
public class MeuController : ControllerBase
{
    private readonly IAiOrchestrator _aiOrchestrator;
    
    public MeuController(IAiOrchestrator aiOrchestrator)
    {
        _aiOrchestrator = aiOrchestrator;
    }
    
    public async Task<string> GerarConteudo(string prompt)
    {
        // Usa ChatGPT por padrão com fallback automático
        return await _aiOrchestrator.GenerateContentAsync(prompt);
        
        // Ou especifica provedor
        return await _aiOrchestrator.GenerateContentAsync(prompt, "gemini");
    }
}
```

## 🔧 Funcionalidades

### ✅ Implementadas:

- **Multi-Provider**: ChatGPT e Gemini
- **Fallback Automático**: Se um provedor falhar, tenta o padrão
- **Logging Estruturado**: Logs detalhados para debugging
- **Tratamento de Erros**: Exceções específicas e informativas
- **Validação de Entrada**: Validação de prompts vazios
- **Dependency Injection**: DI completo configurado
- **Factory Pattern**: Criação dinâmica de serviços
- **Strategy Pattern**: Seleção de provedor em runtime

### 🎯 Benefícios:

- **Testabilidade**: Interfaces permitem mock fácil
- **Manutenibilidade**: Separação clara de responsabilidades
- **Extensibilidade**: Adicione novos provedores facilmente
- **Confiabilidade**: Fallback automático e tratamento robusto de erros
- **Performance**: HttpClient reutilizado via DI

## 🔄 Adicionando Novo Provedor

1. **Implemente a Interface**:
```csharp
public class NovoProvedorService : IGenerativeAiService
{
    public async Task<string> GenerateAsync(string prompt)
    {
        // Implementação específica
    }
}
```

2. **Registre na Factory**:
```csharp
// Em AiServiceFactory.cs
return providerName.ToLowerInvariant() switch
{
    "chatgpt" or "openai" => _serviceProvider.GetRequiredService<ChatGptService>(),
    "gemini" or "google" => _serviceProvider.GetRequiredService<GeminiService>(),
    "novo" => _serviceProvider.GetRequiredService<NovoProvedorService>(), // ✅ Adicione aqui
    _ => throw new NotSupportedException($"Provedor '{providerName}' não suportado.")
};
```

3. **Configure DI**:
```csharp
// Em AiServicesConfiguration.cs
services.AddTransient<NovoProvedorService>(); // ✅ Registre o serviço
```

## 🎉 Sistema Finalizado!

O sistema está completamente implementado seguindo **todos os princípios SOLID** e **Clean Architecture**, pronto para uso em produção com alta testabilidade, manutenibilidade e extensibilidade.

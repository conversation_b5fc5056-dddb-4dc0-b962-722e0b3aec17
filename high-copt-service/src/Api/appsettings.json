{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"OPENAI_API_KEY": "********************************************************************************************************************************************************************", "GEMINI_API_KEY": "AIzaSyDBcR2az4i-gLP5FGCzE73ggKaUF1OZrkE"}, "AI": {"Prompts": {"FileAnalysis": "Analyze the following file and provide a detailed explanation about:\n1. What type of file this is\n2. What is the main content\n3. What could be the purpose or use of this file\n4. Summary of the most important data/information\n\nFile name: {fileName}\nSize: {fileSize} bytes\n\nFile content:\n{fileContent}\n\nPlease provide a clear and structured analysis in English."}}}
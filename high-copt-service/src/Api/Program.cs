using Application;
using Google.Cloud.Firestore;
using HighCapital.Core.Dependencies;
using Infrastructure;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();

builder.Services.AddHealthChecks();
builder.Services.AddCoreCorsPolicy();
builder.Services.AddInfrastructureServices();
builder.Services.AddApplicationServices();
builder.Services.AddOpenApiDocumentation();

var firestorePath = Path.Combine(
    AppDomain.CurrentDomain.BaseDirectory,
    "firestore-credentials.json"
);
builder.Services.AddSingleton(
    new FirestoreDbBuilder
    {
        ProjectId = "highcapital-470117",
        DatabaseId = "high-copt-dev",

        Credential = Google
            .Apis.Auth.OAuth2.GoogleCredential.FromFile(firestorePath)
            .CreateScoped("https://www.googleapis.com/auth/datastore"),
    }.Build()
);

var app = builder.Build();

app.UseOpenApiAndScalarDocumentation();
app.UseHttpsRedirection();
app.MapControllers();
app.UseCoreCorsPolicy();

app.Run();

public partial class Program { }

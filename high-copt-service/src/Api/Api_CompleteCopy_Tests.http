# Complete Copy Generation API Tests
# Este arquivo testa o endpoint que combina todos os setups para gerar copy

### 1. Health Check
GET http://localhost:5000/api/content/health

### 2. Get Available Providers
GET http://localhost:5000/api/content/providers

### 3. Analyze Expert Setup
POST http://localhost:5000/api/content/analyze-setup/expert
Content-Type: application/json

{
  "name": "Dr. <PERSON>",
  "areaOfExpertise": "Educação Financeira e Investimentos",
  "biography": "25 anos como consultor financeiro",
  "moment": "Perdeu tudo na crise de 2008",
  "dolor": "Viu a família passar necessidades",
  "credibility": "PhD em Economia, autor de 3 livros",
  "recognition": "Reconhecido pela Forbes",
  "trackRecord": "50.000+ alunos transformados",
  "howProductWorks": "Método F.O.C.O em 4 pilares",
  "voicePersonality": "<PERSON><PERSON><PERSON>, emp<PERSON><PERSON><PERSON>, baseado em experiência",
  "essentialValues": "Transparência, educação genuína",
  "enterprise": ""
}

### 4. Improve Expert Setup
POST http://localhost:5000/api/content/improve-setup/expert
Content-Type: application/json

{
  "name": "João",
  "areaOfExpertise": "Finanças",
  "biography": "Trabalho com finanças",
  "moment": "",
  "dolor": "",
  "credibility": "Tenho experiência",
  "recognition": "",
  "trackRecord": "",
  "howProductWorks": "",
  "voicePersonality": "",
  "essentialValues": "",
  "enterprise": ""
}

### 5. Validate Product Setup
POST http://localhost:5000/api/content/validate-setup/product
Content-Type: application/json

{
  "name": "Método F.O.C.O - Liberdade Financeira",
  "benefits": "Organização financeira completa, estratégias de investimento",
  "socialProof": "Mais de 10.000 alunos, média de 300% de aumento na poupança",
  "metodology": "4 módulos semanais + mentorias ao vivo + planilhas personalizadas",
  "guarantee": "Garantia incondicional de 30 dias",
  "deliverables": "SessionMentoring",
  "customerJourney": "ThreSixMonths"
}

### 6. Generate Email from Expert Template
POST http://localhost:5000/api/content/generate-from-template/expert
Content-Type: application/json

{
  "entity": {
    "name": "Dr. João Silva",
    "areaOfExpertise": "Educação Financeira e Investimentos",
    "biography": "25 anos como consultor financeiro, saiu da pobreza aos 35 anos e construiu patrimônio de R$ 10 milhões",
    "moment": "Perdeu tudo na crise de 2008 e teve que recomeçar do zero aos 40 anos",
    "dolor": "Viu a família passar necessidades por falta de educação financeira",
    "credibility": "PhD em Economia, autor de 3 livros bestseller, palestrante TEDx",
    "recognition": "Reconhecido pela revista Forbes como Top 10 educadores financeiros do Brasil",
    "trackRecord": "Mais de 50.000 alunos transformaram suas vidas financeiras",
    "howProductWorks": "Método F.O.C.O: Fundamentos, Organização, Crescimento e Otimização em 4 pilares",
    "voicePersonality": "Direto, empático, baseado em experiência real",
    "essentialValues": "Transparência, educação genuína, resultados sustentáveis",
    "enterprise": ""
  },
  "contentRequest": "Email de apresentação pessoal de 250 palavras para novos seguidores",
  "context": "Expert",
  "provider": "chatgpt"
}

### 7. Analyze Campaign Setup
POST http://localhost:5000/api/content/analyze-setup/campaign
Content-Type: application/json

{
  "name": "Lançamento Método F.O.C.O",
  "type": "Launch",
  "conscienceLevel": "AwareOfProblem",
  "sophisticationLevel": "FirstPresent",
  "ideia": "Método revolucionário para transformar a relação com o dinheiro",
  "emotion": "Esperança e urgência de mudança financeira",
  "belief": "Qualquer pessoa pode alcançar liberdade financeira"
}

### 8. Improve Public Setup
POST http://localhost:5000/api/content/improve-setup/public
Content-Type: application/json

{
  "name": "Pessoas interessadas em finanças",
  "local": "Brasil",
  "familySituation": "Variada",
  "personality": "Querem aprender",
  "hobbies": "",
  "lifestyle": "",
  "personalValue": "Crescimento",
  "roof": "",
  "nextLevel": "",
  "dropOfWater": "",
  "beliefs": "",
  "selfVisions": "",
  "possibleObjections": "",
  "ownCommunication": ""
}

### 9. Generate Post from Product Template
POST http://localhost:5000/api/content/generate-from-template/product
Content-Type: application/json

{
  "entity": {
    "name": "Método F.O.C.O - Liberdade Financeira em 30 Dias",
    "benefits": "Organização financeira completa, estratégias de investimento, mentalidade de abundância",
    "socialProof": "Mais de 10.000 alunos, média de 300% de aumento na poupança em 6 meses",
    "metodology": "4 módulos semanais + mentorias ao vivo + planilhas personalizadas",
    "guarantee": "Garantia incondicional de 30 dias",
    "deliverables": "SessionMentoring",
    "customerJourney": "ThreSixMonths"
  },
  "contentRequest": "Post para LinkedIn sobre os benefícios do produto com call-to-action",
  "provider": "gemini"
}

### 10. Validate Expert Setup - Incomplete
POST http://localhost:5000/api/content/validate-setup/expert?provider=chatgpt
Content-Type: application/json

{
  "name": "João",
  "areaOfExpertise": "",
  "biography": "",
  "moment": "",
  "dolor": "",
  "credibility": "",
  "recognition": "",
  "trackRecord": "",
  "howProductWorks": "",
  "voicePersonality": "",
  "essentialValues": "",
  "enterprise": ""
}

### 11. Generate Complete Copy - Email de Lançamento
POST http://localhost:5000/api/content/generate-complete-copy
Content-Type: application/json

{
  "campaign": {
    "name": "Lançamento Método F.O.C.O",
    "type": "Launch",
    "conscienceLevel": "AwareOfProblem",
    "sophisticationLevel": "FirstPresent",
    "ideia": "Método revolucionário para transformar a relação com o dinheiro em 30 dias",
    "emotion": "Esperança e urgência de mudança financeira",
    "belief": "Qualquer pessoa pode alcançar liberdade financeira com o método certo"
  },
  "expert": {
    "name": "Dr. João Silva",
    "areaOfExpertise": "Educação Financeira e Investimentos",
    "biography": "25 anos como consultor financeiro, saiu da pobreza aos 35 anos e construiu patrimônio de R$ 10 milhões",
    "moment": "Perdeu tudo na crise de 2008 e teve que recomeçar do zero aos 40 anos",
    "dolor": "Viu a família passar necessidades por falta de educação financeira",
    "credibility": "PhD em Economia, autor de 3 livros bestseller, palestrante TEDx",
    "recognition": "Reconhecido pela revista Forbes como Top 10 educadores financeiros do Brasil",
    "trackRecord": "Mais de 50.000 alunos transformaram suas vidas financeiras com seus métodos",
    "howProductWorks": "Método F.O.C.O: Fundamentos, Organização, Crescimento e Otimização em 4 pilares",
    "voicePersonality": "Direto, empático, baseado em experiência real, sem promessas irreais",
    "essentialValues": "Transparência, educação genuína, resultados sustentáveis, ética acima do lucro",
    "enterprise": ""
  },
  "product": {
    "name": "Método F.O.C.O - Liberdade Financeira em 30 Dias",
    "benefits": "Organização financeira completa, estratégias de investimento, mentalidade de abundância, plano de aposentadoria",
    "socialProof": "Mais de 10.000 alunos, média de 300% de aumento na poupança em 6 meses",
    "metodology": "4 módulos semanais + mentorias ao vivo + planilhas personalizadas + comunidade exclusiva",
    "guarantee": "Garantia incondicional de 30 dias - se não organizar suas finanças, devolvemos 100% do valor",
    "deliverables": "SessionMentoring",
    "customerJourney": "ThreSixMonths"
  },
  "public": {
    "name": "Maria, profissional liberal de 35-45 anos",
    "local": "São Paulo, classe média",
    "familySituation": "Casada, 2 filhos, renda familiar entre R$ 8-15 mil",
    "personality": "Responsável, ansiosa com dinheiro, busca segurança financeira",
    "hobbies": "Leitura, cursos online, tempo com família",
    "lifestyle": "Vida corrida, quer otimizar tempo e dinheiro",
    "personalValue": "Segurança para os filhos, independência financeira",
    "roof": "Tem renda boa mas não consegue poupar consistentemente",
    "nextLevel": "Quer ter reserva de emergência robusta e começar a investir",
    "dropOfWater": "Medo de perder dinheiro em investimentos errados",
    "beliefs": "Acredita que investir é só para ricos, tem medo do mercado financeiro",
    "selfVisions": "Se vê como alguém que não entende de dinheiro",
    "possibleObjections": "Não tenho tempo, é muito complexo, já tentei antes e não deu certo",
    "ownCommunication": "Pragmática, quer exemplos práticos, gosta de passo-a-passo"
  },
  "copyRequest": "Email de lançamento de 400 palavras com assunto envolvente",
  "provider": "chatgpt"
}

### 4. Generate Complete Copy - Post para Instagram
POST http://localhost:5000/api/content/generate-complete-copy
Content-Type: application/json

{
  "campaign": {
    "name": "Engajamento Instagram",
    "type": "DirectTraffic",
    "conscienceLevel": "TotallyUnconscious",
    "sophisticationLevel": "FirstPresent",
    "ideia": "Transforme sua mentalidade sobre dinheiro",
    "emotion": "Inspiração e motivação",
    "belief": "Pequenas mudanças geram grandes resultados"
  },
  "expert": {
    "name": "Dr. João Silva",
    "areaOfExpertise": "Educação Financeira e Investimentos",
    "biography": "Expert em finanças que saiu da pobreza",
    "moment": "Perdeu tudo e reconstruiu sua vida financeira",
    "dolor": "Cresceu em família com dificuldades financeiras",
    "credibility": "PhD em Economia, 25 anos de experiência",
    "recognition": "Autor bestseller, palestrante reconhecido",
    "trackRecord": "50.000+ vidas transformadas",
    "howProductWorks": "Método F.O.C.O comprovado",
    "voicePersonality": "Inspirador, motivacional, próximo",
    "essentialValues": "Transformação real, educação acessível",
    "enterprise": ""
  },
  "product": {
    "name": "Método F.O.C.O",
    "benefits": "Organização financeira, investimentos inteligentes",
    "socialProof": "10.000+ alunos satisfeitos",
    "metodology": "Método simples em 4 etapas",
    "guarantee": "Garantia de 30 dias",
    "deliverables": "Others",
    "customerJourney": "OneTwoMonths"
  },
  "public": {
    "name": "Jovens profissionais 25-35 anos",
    "local": "Grandes centros urbanos",
    "familySituation": "Solteiros ou recém-casados",
    "personality": "Ambiciosos, conectados, querem crescer",
    "hobbies": "Redes sociais, cursos, networking",
    "lifestyle": "Digital, busca praticidade",
    "personalValue": "Crescimento pessoal e profissional",
    "roof": "Ganham bem mas gastam tudo",
    "nextLevel": "Querem construir patrimônio",
    "dropOfWater": "Falta de conhecimento financeiro",
    "beliefs": "Acham que é muito novo para investir",
    "selfVisions": "Querem ser bem-sucedidos financeiramente",
    "possibleObjections": "Muito jovem, primeiro quero curtir a vida",
    "ownCommunication": "Linguagem informal, visual, exemplos práticos"
  },
  "copyRequest": "Post para Instagram com 3 slides sobre os 3 maiores erros financeiros dos jovens",
  "provider": "gemini"
}

### 5. Generate Complete Copy - Sales Letter
POST http://localhost:5000/api/content/generate-complete-copy
Content-Type: application/json

{
  "campaign": {
    "name": "Conversão Premium",
    "type": "AplicationDirect",
    "conscienceLevel": "MoreConscient",
    "sophisticationLevel": "FocusIdentification",
    "ideia": "O único método que realmente funciona para liberdade financeira",
    "emotion": "Urgência e decisão",
    "belief": "Esta é a última oportunidade de mudar de vida"
  },
  "expert": {
    "name": "Dr. João Silva",
    "areaOfExpertise": "Educação Financeira e Investimentos",
    "biography": "25 anos transformando vidas através da educação financeira, patrimônio pessoal de R$ 10 milhões",
    "moment": "Quebrou na crise de 2008 e reconstruiu tudo usando este método exato",
    "dolor": "Viu a própria família perder a casa por falta de planejamento financeiro",
    "credibility": "PhD em Economia pela USP, CFP certificado, 3 livros bestseller na Amazon",
    "recognition": "Forbes Top 10, palestrante TEDx com 2M+ visualizações, consultor de grandes empresas",
    "trackRecord": "Mais de 50.000 alunos, média de 300% aumento na poupança, 89% conseguem reserva de emergência em 90 dias",
    "howProductWorks": "Método F.O.C.O: sistema proprietário de 4 pilares que funciona mesmo em crises econômicas",
    "voicePersonality": "Autoridade máxima, direto, sem enrolação, baseado em dados e resultados reais",
    "essentialValues": "Resultados comprovados, transparência total, ética acima de tudo",
    "enterprise": ""
  },
  "product": {
    "name": "Método F.O.C.O - Fórmula da Liberdade Financeira",
    "benefits": "Sistema completo: organização total das finanças, estratégias avançadas de investimento, mentalidade milionária, plano de aposentadoria personalizado",
    "socialProof": "10.000+ alunos ativos, 4.9/5 estrelas, R$ 2.3 bilhões movimentados pelos alunos",
    "metodology": "12 semanas intensivas + 52 mentorias ao vivo + planilhas automáticas + comunidade VIP + suporte 24/7",
    "guarantee": "Garantia blindada de 60 dias + bônus vitalício se não ficar satisfeito",
    "deliverables": "MeetingsInPerson",
    "customerJourney": "SevenTwelveMonths"
  },
  "public": {
    "name": "Executivos e empresários de 40-55 anos",
    "local": "Grandes centros, alta renda",
    "familySituation": "Casados, filhos adolescentes/universitários, renda R$ 20-50 mil",
    "personality": "Ambiciosos, exigentes, querem o melhor, valorizam tempo",
    "hobbies": "Golfe, viagens, networking, leitura de negócios",
    "lifestyle": "Corrido, focado em resultados, busca eficiência máxima",
    "personalValue": "Legado para os filhos, liberdade total, status",
    "roof": "Renda alta mas sem estratégia de multiplicação",
    "nextLevel": "Quer ser milionário, aposentadoria precoce, liberdade geográfica",
    "dropOfWater": "Tempo escasso, medo de escolhas erradas que custem caro",
    "beliefs": "Só confia em especialistas de alto nível, quer garantias sólidas",
    "selfVisions": "Líder bem-sucedido que merece o melhor",
    "possibleObjections": "Muito caro, não tenho tempo, já tenho consultor",
    "ownCommunication": "Formal, dados concretos, ROI claro, comparações com mercado"
  },
  "copyRequest": "Sales letter de alta conversão com 1500 palavras focada em urgência e escassez",
  "provider": "chatgpt"
}

### 6. Generate Complete Copy - Webinar Script
POST http://localhost:5000/api/content/generate-complete-copy
Content-Type: application/json

{
  "campaign": {
    "name": "Webinar Educativo",
    "type": "WebinarOurEvent",
    "conscienceLevel": "ConscientDaSolution",
    "sophisticationLevel": "IntrodutionMechanism",
    "ideia": "Os 3 pilares fundamentais que todo investidor precisa dominar",
    "emotion": "Curiosidade e aprendizado",
    "belief": "Conhecimento é poder quando aplicado corretamente"
  },
  "expert": {
    "name": "Dr. João Silva",
    "areaOfExpertise": "Educação Financeira e Investimentos",
    "biography": "Professor e consultor financeiro há 25 anos, especialista em democratizar o conhecimento sobre investimentos",
    "moment": "Começou a carreira ajudando a própria família a sair das dívidas",
    "dolor": "Cresceu vendo pessoas próximas tomarem decisões financeiras ruins por falta de informação",
    "credibility": "PhD em Economia, professor universitário, 25 anos de mercado",
    "recognition": "Colunista em grandes veículos, consultor de fundos de investimento",
    "trackRecord": "Educou milhares através de palestras e cursos gratuitos",
    "howProductWorks": "Metodologia didática que simplifica conceitos complexos",
    "voicePersonality": "Professor dedicado, explicativo, paciente, didático",
    "essentialValues": "Educação de qualidade, conhecimento acessível a todos",
    "enterprise": ""
  },
  "product": {
    "name": "Fundamentos do Investimento Inteligente",
    "benefits": "Base sólida em investimentos, estratégias para iniciantes, proteção contra erros comuns",
    "socialProof": "Milhares de participantes em webinars anteriores",
    "metodology": "Aula prática com exemplos reais e cases de sucesso",
    "guarantee": "Conteúdo gratuito de alta qualidade",
    "deliverables": "FollowUpSuport",
    "customerJourney": "OneTwoMonths"
  },
  "public": {
    "name": "Iniciantes em investimentos, 25-50 anos",
    "local": "Todo Brasil",
    "familySituation": "Variada, foco em estabilidade",
    "personality": "Cautelosos, querem aprender antes de agir",
    "hobbies": "Leitura, cursos online, pesquisa",
    "lifestyle": "Buscam conhecimento, querem segurança",
    "personalValue": "Aprendizado, crescimento gradual",
    "roof": "Dinheiro parado na poupança ou conta corrente",
    "nextLevel": "Começar a investir com segurança",
    "dropOfWater": "Medo de perder dinheiro, complexidade do mercado",
    "beliefs": "Investir é arriscado, melhor ir devagar",
    "selfVisions": "Querem ser investidores conscientes",
    "possibleObjections": "Muito complexo, preciso de mais conhecimento primeiro",
    "ownCommunication": "Explicações claras, exemplos práticos, passo a passo"
  },
  "copyRequest": "Roteiro completo para webinar de 60 minutos com estrutura de ensino e call-to-action sutil",
  "provider": "gemini"
}

### 12. Analyze Product Setup
POST http://localhost:5000/api/content/analyze-setup/product
Content-Type: application/json

{
  "name": "Método F.O.C.O - Liberdade Financeira",
  "benefits": "Organização financeira completa, estratégias de investimento",
  "socialProof": "Mais de 10.000 alunos, média de 300% de aumento na poupança",
  "metodology": "4 módulos semanais + mentorias ao vivo + planilhas personalizadas",
  "guarantee": "Garantia incondicional de 30 dias",
  "deliverables": "SessionMentoring",
  "customerJourney": "ThreSixMonths"
}

### 13. Improve Campaign Setup
POST http://localhost:5000/api/content/improve-setup/campaign
Content-Type: application/json

{
  "name": "Lançamento Método F.O.C.O",
  "type": "",
  "conscienceLevel": "",
  "sophisticationLevel": "",
  "ideia": "",
  "emotion": "",
  "belief": ""
}

### 14. Validate Campaign Setup
POST http://localhost:5000/api/content/validate-setup/campaign
Content-Type: application/json

{
  "name": "Lançamento Método F.O.C.O",
  "type": "Launch",
  "conscienceLevel": "AwareOfProblem",
  "sophisticationLevel": "FirstPresent",
  "ideia": "Método revolucionário para transformar a relação com o dinheiro",
  "emotion": "Esperança e urgência de mudança financeira",
  "belief": "Qualquer pessoa pode alcançar liberdade financeira"
}

### 15. Validate Public Setup
POST http://localhost:5000/api/content/validate-setup/public
Content-Type: application/json

{
  "name": "Pessoas interessadas em finanças",
  "local": "Brasil",
  "familySituation": "Variada",
  "personality": "Querem aprender",
  "hobbies": "",
  "lifestyle": "",
  "personalValue": "Crescimento",
  "roof": "",
  "nextLevel": "",
  "dropOfWater": "",
  "beliefs": "",
  "selfVisions": "",
  "possibleObjections": "",
  "ownCommunication": ""
}

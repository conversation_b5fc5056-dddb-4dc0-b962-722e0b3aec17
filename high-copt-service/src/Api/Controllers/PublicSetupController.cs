using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Application.Contracts.DTOs;

namespace Api.Controllers;

[ApiController]
[Route("api/public-setup")]
public class PublicSetupController(IPublicService publicService, ILogger<PublicSetupController> logger) : ControllerBase
{
    [HttpGet("{identifier}/template")]
    public async Task<IActionResult> GenerateSetupTemplate(string identifier)
    {
        try
        {
            logger.LogInformation("Generating public setup template for identifier: {Identifier}", identifier);
            
            var template = await publicService.GenerateSetupTemplateAsync(identifier);
            
            return Ok(new SetupTemplateResponse
            {
                Success = true,
                Template = template,
                Message = "Public setup template generated successfully",
                EntityType = "Public",
                Identifier = identifier
            });
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "Public not found with identifier: {Identifier}", identifier);
            return NotFound(new SetupTemplateResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating public setup template for identifier: {Identifier}", identifier);
            return StatusCode(500, new SetupTemplateResponse
            {
                Success = false,
                Message = "Internal server error while generating template"
            });
        }
    }

    [HttpGet("{identifier}")]
    public async Task<IActionResult> GetPublic(string identifier)
    {
        try
        {
            var publicEntity = await publicService.GetByIdOrExternalIdAsync(identifier);
            return Ok(publicEntity);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new ApiErrorResponse { Message = ex.Message });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving public with identifier: {Identifier}", identifier);
            return StatusCode(500, new ApiErrorResponse { Message = "Internal server error" });
        }
    }

    [HttpPost]
    [ProducesResponseType<Domain.Entities.Public>(201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> CreatePublic([FromBody] CreatePublicDto createPublicDto)
    {
        try
        {
            logger.LogInformation("Creating a new public entity.");
            var publicEntity = new Domain.Entities.Public
            {
                Name = createPublicDto.Name,
                Local = createPublicDto.Local,
                FamilySituation = createPublicDto.FamilySituation,
                Personality = createPublicDto.Personality,
                Hobbies = createPublicDto.Hobbies,
                Lifestyle = createPublicDto.Lifestyle,
                PersonalValue = createPublicDto.PersonalValue,
                Roof = createPublicDto.Roof,
                NextLevel = createPublicDto.NextLevel,
                DropOfWater = createPublicDto.DropOfWater,
                Beliefs = createPublicDto.Beliefs,
                SelfVisions = createPublicDto.SelfVisions,
                PossibleObjections = createPublicDto.PossibleObjections,
                OwnCommunication = createPublicDto.OwnCommunication,
                Gender = createPublicDto.Gender,
                EducationLevel = createPublicDto.EducationLevel
            };

            var createdPublic = await publicService.CreatePublicAsync(publicEntity);
            
            var location = Url.Action(nameof(GetPublic), new { identifier = createdPublic.ExternalId });
            return Created(location, createdPublic);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while creating the public entity.");
            return StatusCode(500, new ApiErrorResponse { Message = "An error occurred while creating the public entity.", Details = ex.Message });
        }
    }
}

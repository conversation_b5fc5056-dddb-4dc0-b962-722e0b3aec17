using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Application.Contracts.DTOs;

namespace Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MessagesController(
    IMessageService messageService,
    ILogger<MessagesController> logger) : ControllerBase
{
    [HttpGet("{id}")]
    [ProducesResponseType<MessageResponse>(200)]
    [ProducesResponseType<ApiErrorResponse>(404)]
    [ProducesResponseType<ApiErrorResponse>(500)]
    public async Task<IActionResult> GetMessage(int id)
    {
        try
        {
            var message = await messageService.GetMessageByIdAsync(id);
            
            if (message == null)
            {
                return NotFound(new ApiErrorResponse { Message = $"Message not found with ID: {id}" });
            }

            return Ok(new MessageResponse
            {
                Id = message.Id,
                ExternalId = message.ExternalId,
                UserPrompt = message.UserPrompt,
                AiResponse = message.AiResponse,
                Provider = message.Provider,
                MessageType = message.MessageType,
                Metadata = message.Metadata?.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.ToString() ?? string.Empty
                ) ?? new Dictionary<string, string>(),
                CreatedAt = message.CreatedAt,
                UpdatedAt = message.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving message with ID: {MessageId}", id);
            return StatusCode(500, new ApiErrorResponse { Message = "Internal server error" });
        }
    }

    [HttpGet("external/{externalId}")]
    [ProducesResponseType<MessageResponse>(200)]
    [ProducesResponseType<ApiErrorResponse>(404)]
    [ProducesResponseType<ApiErrorResponse>(500)]
    public async Task<IActionResult> GetMessageByExternalId(string externalId)
    {
        try
        {
            var message = await messageService.GetMessageByExternalIdAsync(externalId);
            
            if (message == null)
            {
                return NotFound(new ApiErrorResponse { Message = $"Message not found with external ID: {externalId}" });
            }

            return Ok(new MessageResponse
            {
                Id = message.Id,
                ExternalId = message.ExternalId,
                UserPrompt = message.UserPrompt,
                AiResponse = message.AiResponse,
                Provider = message.Provider,
                MessageType = message.MessageType,
                Metadata = message.Metadata?.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.ToString() ?? string.Empty
                ) ?? new Dictionary<string, string>(),
                CreatedAt = message.CreatedAt,
                UpdatedAt = message.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving message with external ID: {ExternalId}", externalId);
            return StatusCode(500, new ApiErrorResponse { Message = "Internal server error" });
        }
    }


    [HttpGet("recent")]
    [ProducesResponseType<MessageResponse[]>(200)]
    [ProducesResponseType<ApiErrorResponse>(500)]
    public async Task<IActionResult> GetRecentMessages([FromQuery] int count = 50)
    {
        try
        {
            if (count <= 0 || count > 100)
            {
                return BadRequest(new ApiErrorResponse { Message = "Count must be between 1 and 100" });
            }

            var messages = await messageService.GetRecentMessagesAsync(count);
            
            var messageResponses = messages.Select(m => new MessageResponse
            {
                Id = m.Id,
                ExternalId = m.ExternalId,
                UserPrompt = m.UserPrompt,
                AiResponse = m.AiResponse,
                Provider = m.Provider,
                MessageType = m.MessageType,
                Metadata = m.Metadata?.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.ToString() ?? string.Empty
                ) ?? new Dictionary<string, string>(),
                CreatedAt = m.CreatedAt,
                UpdatedAt = m.UpdatedAt
            }).ToArray();

            return Ok(messageResponses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving recent messages");
            return StatusCode(500, new ApiErrorResponse { Message = "Internal server error" });
        }
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(204)]
    [ProducesResponseType<ApiErrorResponse>(404)]
    [ProducesResponseType<ApiErrorResponse>(500)]
    public async Task<IActionResult> DeleteMessage(int id)
    {
        try
        {
            await messageService.DeleteMessageAsync(id);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new ApiErrorResponse { Message = $"Message not found with ID: {id}" });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deleting message with ID: {MessageId}", id);
            return StatusCode(500, new ApiErrorResponse { Message = "Internal server error" });
        }
    }
}

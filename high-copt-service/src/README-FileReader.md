# File Reader Service - Documentação Completa

Um serviço completo para leitura de arquivos de diferentes tipos e retorno do conteúdo como string.

## 🚀 Métodos Principais

### **ReadFileAsync() - Converte qualquer arquivo em string**
```csharp
// Lê arquivo por caminho - detecta tipo automaticamente
string conteudo = await _fileReaderService.ReadFileAsync(@"C:\arquivo.pdf");

// Lê arquivo de upload/stream
using var stream = arquivo.OpenReadStream();
string conteudo = await _fileReaderService.ReadFileAsync(stream, arquivo.FileName);
```

### **IsFileTypeSupported() - Verifica se arquivo é suportado**
```csharp
bool suportado = _fileReaderService.IsFileTypeSupported("documento.pdf"); // true
bool suportado = _fileReaderService.IsFileTypeSupported("arquivo.exe");   // false
```

### **GetSupportedExtensions() - Lista tipos aceitos**
```csharp
var tipos = _fileReaderService.GetSupportedExtensions();
// Retorna: [".pdf", ".csv", ".txt", ".docx", ".xlsx", ".xls", ".json", ".xml", ...]
```

## 📋 Índice
- [Tipos de Arquivo Suportados](#tipos-de-arquivo-suportados)
- [Como o ReadFileAsync Funciona](#como-o-readfileasync-funciona)
- [Métodos Disponíveis](#métodos-disponíveis)
- [Exemplos Práticos](#exemplos-práticos)
- [Fluxo Interno Detalhado](#fluxo-interno-detalhado)
- [Tratamento de Erros](#tratamento-de-erros)
- [Dependências](#dependências)

## 📁 Tipos de Arquivo Suportados

| Tipo | Extensões | Biblioteca Usada | O que Extrai |
|------|-----------|------------------|--------------|
| **Texto** | `.txt`, `.json`, `.xml`, `.html`, `.htm`, `.md`, `.log` | System.IO | Todo o conteúdo como texto |
| **CSV** | `.csv` | CsvHelper | Headers + dados formatados |
| **PDF** | `.pdf` | PdfPig | Texto extraído de todas as páginas |
| **Word** | `.docx` | DocumentFormat.OpenXml | Texto de todos os parágrafos |
| **Excel** | `.xlsx`, `.xls` | ExcelDataReader | Dados de todas as planilhas |

## 🔧 Como o ReadFileAsync Funciona

### **Método Principal:**
```csharp
Task<string> ReadFileAsync(string filePath)
Task<string> ReadFileAsync(Stream fileStream, string fileName)
```

### **Fluxo Interno Passo a Passo:**

#### **1. Detecção Automática do Tipo**
```csharp
// O serviço extrai a extensão do arquivo
var extension = Path.GetExtension(fileName); // Ex: ".pdf"

// Busca o leitor correto no dicionário interno
_fileReaders.TryGetValue(extension, out var reader)
```

#### **2. Mapeamento Interno (Dicionários)**
```csharp
// No construtor, o serviço cria dois dicionários:
_fileReaders = new Dictionary<string, Func<string, Task<string>>>()
{
    { ".pdf", ReadPdfFileAsync },      // PDF → ReadPdfFileAsync
    { ".csv", ReadCsvFileAsync },      // CSV → ReadCsvFileAsync  
    { ".docx", ReadDocxFileAsync },    // Word → ReadDocxFileAsync
    { ".xlsx", ReadExcelFileAsync },   // Excel → ReadExcelFileAsync
    // ... etc
};

_streamReaders = new Dictionary<string, Func<Stream, Task<string>>>()
{
    { ".pdf", ReadPdfStreamAsync },    // Para streams
    { ".csv", ReadCsvStreamAsync },
    // ... etc
};
```

#### **3. Execução do Leitor Específico**
```csharp
// Chama automaticamente o método correto
return await reader(filePath); // Ex: ReadPdfFileAsync(filePath)
```

## 📚 Métodos Disponíveis

### **1. ReadFileAsync(string filePath)**
```csharp
// Lê arquivo por caminho
string conteudo = await _fileReaderService.ReadFileAsync(@"C:\arquivo.pdf");
```

**O que faz:**
1. Verifica se arquivo existe
2. Extrai extensão (`.pdf`)
3. Busca leitor correto (`ReadPdfFileAsync`)
4. Executa e retorna string

### **2. ReadFileAsync(Stream stream, string fileName)**
```csharp
// Lê arquivo de stream (uploads)
using var stream = file.OpenReadStream();
string conteudo = await _fileReaderService.ReadFileAsync(stream, file.FileName);
```

**O que faz:**
1. Extrai extensão do nome (`fileName`)
2. Busca leitor de stream correto
3. Processa stream diretamente
4. Retorna string

### **3. IsFileTypeSupported(string fileName)**
```csharp
// Verifica se tipo é suportado
bool suportado = _fileReaderService.IsFileTypeSupported("documento.pdf");
```

### **4. GetSupportedExtensions()**
```csharp
// Lista todas as extensões suportadas
var extensoes = _fileReaderService.GetSupportedExtensions();
// Retorna: [".pdf", ".csv", ".txt", ".docx", ".xlsx", ...]
```

## 💡 Exemplos Práticos

### **Exemplo 1: Upload de Arquivo**
```csharp
[HttpPost("processar")]
public async Task<IActionResult> ProcessarArquivo(IFormFile arquivo)
{
    // Só esta linha faz tudo!
    using var stream = arquivo.OpenReadStream();
    string conteudo = await _fileReaderService.ReadFileAsync(stream, arquivo.FileName);
    
    // Agora 'conteudo' tem todo o texto do arquivo
    return Ok(new { 
        Arquivo = arquivo.FileName,
        Conteudo = conteudo,
        Tamanho = conteudo.Length 
    });
}
```

### **Exemplo 2: Processar Arquivo Local**
```csharp
public async Task<string> ProcessarDocumento(string caminho)
{
    // Detecta automaticamente se é PDF, Word, Excel, etc.
    string texto = await _fileReaderService.ReadFileAsync(caminho);
    
    // Fazer algo com o texto...
    return texto.ToUpper(); // Exemplo: converter para maiúsculo
}
```

### **Exemplo 3: Validação Antes de Processar**
```csharp
public async Task<IActionResult> ProcessarComValidacao(IFormFile arquivo)
{
    // Verifica se é suportado
    if (!_fileReaderService.IsFileTypeSupported(arquivo.FileName))
    {
        return BadRequest($"Tipo não suportado. Aceitos: {string.Join(", ", _fileReaderService.GetSupportedExtensions())}");
    }
    
    // Processa
    using var stream = arquivo.OpenReadStream();
    string conteudo = await _fileReaderService.ReadFileAsync(stream, arquivo.FileName);
    
    return Ok(conteudo);
}
```

## 🔍 Fluxo Interno Detalhado

### **Para PDF:**
```csharp
private async Task<string> ReadPdfStreamAsync(Stream stream)
{
    // 1. Abre PDF usando PdfPig
    using var document = PdfDocument.Open(stream);
    var text = new StringBuilder();

    // 2. Itera por cada página
    foreach (Page page in document.GetPages())
    {
        // 3. Extrai texto da página
        text.AppendLine(page.Text);
    }

    // 4. Retorna todo o texto
    return text.ToString();
}
```

### **Para CSV:**
```csharp
private async Task<string> ReadCsvStreamAsync(Stream stream)
{
    using var reader = new StreamReader(stream);
    using var csv = new CsvReader(reader, config);

    // 1. Lê headers
    await csv.ReadAsync();
    csv.ReadHeader();
    var headers = csv.HeaderRecord;

    // 2. Lê dados linha por linha
    var records = new List<Dictionary<string, object>>();
    while (await csv.ReadAsync())
    {
        // Processa cada linha...
    }

    // 3. Formata como string
    return "Header1, Header2\nDado1, Dado2\n...";
}
```

### **Para Word (.docx):**
```csharp
private async Task<string> ReadDocxStreamAsync(Stream stream)
{
    // 1. Abre documento Word
    using var doc = WordprocessingDocument.Open(stream, false);
    var body = doc.MainDocumentPart?.Document?.Body;
    
    var text = new StringBuilder();
    
    // 2. Extrai texto de cada parágrafo
    foreach (var paragraph in body.Elements<Paragraph>())
    {
        text.AppendLine(paragraph.InnerText);
    }

    return text.ToString();
}
```

## 🚨 Tratamento de Erros

### **Tipos de Exceção:**
```csharp
try 
{
    string conteudo = await _fileReaderService.ReadFileAsync(caminho);
}
catch (FileNotFoundException)
{
    // Arquivo não encontrado
}
catch (NotSupportedException ex)
{
    // Tipo de arquivo não suportado
    // ex.Message contém extensões suportadas
}
catch (InvalidOperationException ex)
{
    // Erro ao ler/processar arquivo
    // ex.InnerException tem detalhes específicos
}
```

### **Validação Preventiva:**
```csharp
// Sempre verifique antes de processar
if (!File.Exists(caminho))
{
    throw new FileNotFoundException($"Arquivo não encontrado: {caminho}");
}

if (!_fileReaderService.IsFileTypeSupported(nomeArquivo))
{
    throw new NotSupportedException($"Tipo não suportado: {Path.GetExtension(nomeArquivo)}");
}
```

## 📦 Dependências

### **Pacotes NuGet Necessários:**
```xml
<PackageReference Include="PdfPig" Version="0.1.8" />
<PackageReference Include="CsvHelper" Version="30.0.1" />
<PackageReference Include="DocumentFormat.OpenXml" Version="3.0.1" />
<PackageReference Include="ExcelDataReader" Version="3.6.0" />
<PackageReference Include="ExcelDataReader.DataSet" Version="3.6.0" />
```

### **Registro no DI Container:**
```csharp
// Program.cs
builder.Services.AddScoped<IFileReaderService, FileReaderService>();
```

### **Encoding Provider (para Excel):**
```csharp
// No construtor do FileReaderService
Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
```

## 🎯 Resumo

**O FileReaderService é um conversor universal:**
- **Entrada**: Qualquer arquivo suportado (PDF, CSV, Word, Excel, etc.)
- **Saída**: Sempre uma `string` com todo o conteúdo
- **Detecção**: Automática pela extensão do arquivo
- **Uso**: Um único método `ReadFileAsync()`

**Você nunca precisa saber o tipo do arquivo** - o serviço detecta e processa automaticamente!

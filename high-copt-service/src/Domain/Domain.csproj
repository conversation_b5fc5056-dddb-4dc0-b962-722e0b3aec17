﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>HighCopy.Domain</AssemblyName>
    <RootNamespace>Domain</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Google.Cloud.Firestore" />
    <PackageReference Include="HighCapital.Core" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="entities\AudienceSetup.cs" />
    <Compile Remove="entities\ExpertSetup.cs" />
    <Compile Update="Enums\Public\PublicAgeGroupEnum.cs">
      <Link>Enums\PublicAgeGroupEnum.cs</Link>
    </Compile>
  </ItemGroup>

</Project>

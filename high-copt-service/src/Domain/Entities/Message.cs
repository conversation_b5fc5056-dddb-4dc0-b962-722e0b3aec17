using Domain.Interfaces;
using Google.Cloud.Firestore;

namespace Domain.Entities;

[FirestoreData]
public class Message : IEntity
{
    [FirestoreProperty("id")]
    public int Id { get; set; }

    [FirestoreProperty]
    public string ExternalId { get; set; } = string.Empty;

    [FirestoreProperty]
    public string UserPrompt { get; set; } = string.Empty;

    [FirestoreProperty]
    public string AiResponse { get; set; } = string.Empty;

    [FirestoreProperty]
    public string Provider { get; set; } = string.Empty;

    [FirestoreProperty]
    public string MessageType { get; set; } = string.Empty;


    [FirestoreProperty]
    public Dictionary<string, string> Metadata { get; set; } = new();

    [FirestoreProperty]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [FirestoreProperty]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    [FirestoreProperty]
    public bool IsActive { get; set; } = true;
}

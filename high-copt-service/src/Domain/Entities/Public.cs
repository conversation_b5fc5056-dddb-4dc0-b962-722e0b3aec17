using Domain.Enums.Public;

namespace Domain.Entities;

using Domain.Interfaces;
using Google.Cloud.Firestore;

[FirestoreData]
public class Public : IEntity
{
  [FirestoreProperty]
  public int Id { get; set; }
  
  [FirestoreProperty]
  public string ExternalId { get; set; } = string.Empty;
  
  [FirestoreProperty]
  public required string Name { get; set; }
  
  [FirestoreProperty]
  public required string Local { get; set; }
  
  [FirestoreProperty]
  public required string FamilySituation { get; set; }
  
  [FirestoreProperty]
  public required string Personality { get; set; }
  
  [FirestoreProperty]
  public required string Hobbies { get; set; }
  
  [FirestoreProperty]
  public required string Lifestyle { get; set; }
  
  [FirestoreProperty]
  public required string PersonalValue { get; set; }
  
  [FirestoreProperty]
  public required string Roof { get; set; }
  
  [FirestoreProperty]
  public required string NextLevel { get; set; }
  
  [FirestoreProperty]
  public required string DropOfWater { get; set; }
  
  [FirestoreProperty]
  public required string Beliefs { get; set; }
  
  [FirestoreProperty]
  public required string SelfVisions { get; set; }
  
  [FirestoreProperty]
  public required string PossibleObjections { get; set; }
  
  [FirestoreProperty]
  public required string OwnCommunication { get; set; }
  
  [FirestoreProperty]
  public required PublicEducationLevelEnum EducationLevel { get; set; }
  
  [FirestoreProperty]
  public required PublicGenderEnum Gender { get; set; }
}
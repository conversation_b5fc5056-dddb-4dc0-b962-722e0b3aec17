using Domain.Enums.Campaing;

namespace Domain.Entities; 

using Domain.Interfaces;
using Google.Cloud.Firestore;

[FirestoreData]
public sealed class Campaign : IEntity
{
    [FirestoreProperty]
    public int Id {get; set;}
    
    [FirestoreProperty]
    public string ExternalId { get; set; } = string.Empty;
    
    [FirestoreProperty]
    public required string Name {get; set;}
    
    [FirestoreProperty]
    public required string Ideia { get; set; }

    [FirestoreProperty]
    public required string Emotion { get; set; }

    [FirestoreProperty]
    public required string Belief { get; set; }
    
    [FirestoreProperty]
    public required CampaingTypeEnum Type { get; set; }
                            
    [FirestoreProperty]
    public required CampaingConscienceLevelEnum ConscienceLevel { get; set; }
    
    [FirestoreProperty]
    public required CampaingSophisticationLevelEnum SophisticationLevel { get; set; }
}
using Domain.Entities;

namespace Domain.Interfaces.Repositories
{
    public interface IPublicRepository
    {
        Task<Public> GetByIdAsync (int id);
        Task<Public> GetByExternalIdAsync (string externalId);
        Task<IEnumerable<Public>> GetAllAsync ();
        Task AddAsync (Public publicEntity);
        Task UpdateAsync (Public publicEntity);
        Task DeleteAsync (int id);
        Task SaveChangesAsync ();
    }
}
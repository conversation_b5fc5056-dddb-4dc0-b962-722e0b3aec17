using Domain.Entities;

namespace Domain.Interfaces.Repositories;

public interface IMessageRepository
{
    Task<Message> CreateAsync(Message message);
    Task<Message?> GetByIdAsync(int id);
    Task<Message?> GetByExternalIdAsync(string externalId);
    Task<IEnumerable<Message>> GetByUserAndDateRangeAsync(string userId, DateTime startDate, DateTime endDate);
    Task<Message> UpdateAsync(Message message);
    Task DeleteAsync(int id);
    Task<IEnumerable<Message>> GetRecentMessagesAsync(int count = 50);
}

using Domain.Entities;
using Domain.Enums.Messages;

namespace Domain.Interfaces.Services;

public interface IMessageService
{
    Task<Message> SaveMessageAsync(string userPrompt, string aiResponse, string provider, MessageTypeEnum messageType, Dictionary<string, string>? metadata = null);
    Task<Message?> GetMessageByIdAsync(int id);
    Task<Message?> GetMessageByExternalIdAsync(string externalId);
    Task<IEnumerable<Message>> GetRecentMessagesAsync(int count = 50);
    Task<Message> UpdateMessageAsync(Message message);
    Task DeleteMessageAsync(int id);
}

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>HighCopy.Application</AssemblyName>
    <RootNamespace>Application</RootNamespace>
  </PropertyGroup>


  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Google_GenerativeAI" />
    <PackageReference Include="PdfPig" />
    <PackageReference Include="CsvHelper" />
    <PackageReference Include="DocumentFormat.OpenXml" />
    <PackageReference Include="ExcelDataReader" />
    <PackageReference Include="ExcelDataReader.DataSet" />
    <PackageReference Include="Scriban" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Templates\**\*.sbn">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>



</Project>

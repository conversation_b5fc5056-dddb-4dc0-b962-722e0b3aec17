using Domain.Enums.Public;

namespace Application.Contracts.DTOs;

public class CreatePublicDto
{
    public required string Name { get; set; }
    public required string Local { get; set; }
    public required string FamilySituation { get; set; }
    public required string Personality { get; set; }
    public required string Hobbies { get; set; }
    public required string Lifestyle { get; set; }
    public required string PersonalValue { get; set; }
    public required string Roof { get; set; }
    public required string NextLevel { get; set; }
    public required string DropOfWater { get; set; }
    public required string Beliefs { get; set; }
    public required string SelfVisions { get; set; }
    public required string PossibleObjections { get; set; }
    public required string OwnCommunication { get; set; }
    public required PublicEducationLevelEnum EducationLevel { get; set; }
    public required PublicGenderEnum Gender { get; set; }
}

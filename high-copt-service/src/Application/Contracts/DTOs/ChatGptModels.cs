using System.Text.Json.Serialization;

namespace Application.Models.AI;

public class ChatGptRequest
{
    [JsonPropertyName("model")]
    public string Model { get; set; } = "gpt-4o-mini";
    
    [JsonPropertyName("messages")]
    public ChatGptMessage[] Messages { get; set; } = [];
}

public class ChatGptMessage
{
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;
    
    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;
}

public class ChatGptResponse
{
    [JsonPropertyName("choices")]
    public ChatGptChoice[]? Choices { get; set; }
}

public class ChatGptChoice
{
    [JsonPropertyName("message")]
    public ChatGptMessage? Message { get; set; }
}

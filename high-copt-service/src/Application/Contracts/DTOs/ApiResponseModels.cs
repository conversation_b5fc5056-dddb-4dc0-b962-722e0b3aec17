using Domain.Enums.Campaing;
using Domain.Enums.Products;
using Domain.Enums.Public;

namespace Application.Contracts.DTOs;

public class GenerateContentRequest
{
    public string Prompt { get; set; } = string.Empty;
    public string? PreferredProvider { get; set; }
}

public class GenerateContentResponse
{
    public bool Success { get; set; }
    public string Content { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Provider { get; set; }
}

public class ProviderResponse
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
}

public class HealthResponse
{
    public string Status { get; set; } = string.Empty;
    public string Service { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public DateTime CheckedAt { get; set; }
    public string[] Providers { get; set; } = System.Array.Empty<string>();
}

public class ApiErrorResponse
{
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
}

public class MessageResponse
{
    public int Id { get; set; }
    public string ExternalId { get; set; } = string.Empty;
    public string UserPrompt { get; set; } = string.Empty;
    public string AiResponse { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string MessageType { get; set; } = string.Empty;
    public Dictionary<string, string> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class SetupTemplateResponse
{
    public bool Success { get; set; }
    public string Template { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? EntityType { get; set; }
    public string? Identifier { get; set; }
}

public class FileAnalysisResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? FileType { get; set; }
    public string? Analysis { get; set; }
    public string? Provider { get; set; }
}

public class GenerateCompleteCopyRequest
{
    public CampaignDto Campaign { get; set; } = new();
    public ExpertDto Expert { get; set; } = new();
    public ProductDto Product { get; set; } = new();
    public PublicDto Public { get; set; } = new();
    public string CopyRequest { get; set; } = string.Empty;
    public string? Provider { get; set; }
}

public class CampaignDto
{
    public string Name { get; set; } = string.Empty;
    public CampaingTypeEnum Type { get; set; }
    public CampaingConscienceLevelEnum ConscienceLevel { get; set; }
    public CampaingSophisticationLevelEnum SophisticationLevel { get; set; }
    public string Ideia { get; set; } = string.Empty;
    public string Emotion { get; set; } = string.Empty;
    public string Belief { get; set; } = string.Empty;
}

public class ExpertDto
{
    public string Name { get; set; } = string.Empty;
    public string AreaOfExpertise { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Moment { get; set; } = string.Empty;
    public string Dolor { get; set; } = string.Empty;
    public string Credibility { get; set; } = string.Empty;
    public string Recognition { get; set; } = string.Empty;
    public string TrackRecord { get; set; } = string.Empty;
    public string HowProductWorks { get; set; } = string.Empty;
    public string VoicePersonality { get; set; } = string.Empty;
    public string EssentialValues { get; set; } = string.Empty;
    public string Enterprise { get; set; } = string.Empty;
}

public class ProductDto
{
    public string Name { get; set; } = string.Empty;
    public string Benefits { get; set; } = string.Empty;
    public string SocialProof { get; set; } = string.Empty;
    public string Metodology { get; set; } = string.Empty;
    public string Guarantee { get; set; } = string.Empty;
    public ProductDeliverablesEnum Deliverables { get; set; }
    public ProductCustomerJourneyEnum CustomerJourney { get; set; }
}

public class PublicDto
{
    public string Name { get; set; } = string.Empty;
    public string Local { get; set; } = string.Empty;
    public string FamilySituation { get; set; } = string.Empty;
    public string Personality { get; set; } = string.Empty;
    public string Hobbies { get; set; } = string.Empty;
    public string Lifestyle { get; set; } = string.Empty;
    public string PersonalValue { get; set; } = string.Empty;
    public string Roof { get; set; } = string.Empty;
    public string NextLevel { get; set; } = string.Empty;
    public string DropOfWater { get; set; } = string.Empty;
    public string Beliefs { get; set; } = string.Empty;
    public string SelfVisions { get; set; } = string.Empty;
    public string PossibleObjections { get; set; } = string.Empty;
    public string OwnCommunication { get; set; } = string.Empty;
    public PublicGenderEnum PublicGender { get; set; }
    public PublicEducationLevelEnum PublicEducationLevel { get; set; }
}

public class GenerateCompleteCopyResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string GeneratedCopy { get; set; } = string.Empty;
    public string CopyRequest { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
}

public class GenerateCompleteCopyByIdRequest
{
    public string CampaignId { get; set; } = string.Empty;
    public string ExpertId { get; set; } = string.Empty;
    public string ProductId { get; set; } = string.Empty;
    public string PublicId { get; set; } = string.Empty;
    public string CopyRequest { get; set; } = string.Empty;
    public string? Provider { get; set; } = string.Empty;
}

using Domain.Enums.Products;

namespace Application.Contracts.DTOs;

public class CreateProductDto
{
    public required string Name { get; set; }
    public required string Benefits { get; set; }
    public required string SocialProof { get; set; }
    public required string Metodology { get; set; }
    public required string Guarantee { get; set; }
    public required ProductDeliverablesEnum Deliverables  { get; set; }
    public required ProductCustomerJourneyEnum CustomerJourney { get; set; }
}

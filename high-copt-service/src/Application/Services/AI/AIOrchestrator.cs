using Microsoft.Extensions.Logging;
using Domain.Interfaces;

namespace Application.Services.AI;

public class AiOrchestrator(
    IAiServiceFactory aiServiceFactory,
    ILogger<AiOrchestrator> logger,
    string defaultProvider = "chatgpt")
    : IAiOrchestrator
{
    private readonly IAiServiceFactory _aiServiceFactory = aiServiceFactory ?? throw new ArgumentNullException(nameof(aiServiceFactory));
    private readonly ILogger<AiOrchestrator> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly string _fallbackProvider = defaultProvider == "chatgpt" ? "gemini" : "chatgpt";

    public async Task<string> GenerateContentAsync(string prompt, string? preferredProvider = null)
    {
        if (string.IsNullOrWhiteSpace(prompt))
            throw new ArgumentException("Prompt cannot be empty.", nameof(prompt));

        var provider = preferredProvider ?? defaultProvider;
        
        try
        {
            _logger.LogInformation("Starting content generation with provider: {Provider}", provider);
            
            var aiService = _aiServiceFactory.CreateService(provider);
            var result = await aiService.GenerateAsync(prompt);
            
            _logger.LogInformation("Content generated successfully using provider: {Provider}", provider);
            
            return result;
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("communication failure"))
        {
            _logger.LogWarning(ex, "Provider {Provider} unavailable. Trying fallback to {FallbackProvider}", 
                provider, _fallbackProvider);
            
            return await TryFallbackProvider(prompt, provider);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "Provider {Provider} failed. Trying fallback to {FallbackProvider}", 
                provider, _fallbackProvider);
            
            return await TryFallbackProvider(prompt, provider);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogWarning(ex, "Provider {Provider} timeout. Trying fallback to {FallbackProvider}", 
                provider, _fallbackProvider);
            
            return await TryFallbackProvider(prompt, provider);
        }
        catch (NotSupportedException ex)
        {
            _logger.LogWarning(ex, "Provider not supported: {Provider}. Trying with default provider.", provider);
            
            if (provider != defaultProvider)
            {
                return await GenerateContentAsync(prompt, defaultProvider);
            }
            
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Unexpected error in provider {Provider}. Trying fallback to {FallbackProvider}", 
                provider, _fallbackProvider);
            
            return await TryFallbackProvider(prompt, provider);
        }
    }

    private async Task<string> TryFallbackProvider(string prompt, string originalProvider)
    {
        if (originalProvider == _fallbackProvider)
        {
            _logger.LogError("Fallback not available - original provider is already the fallback: {Provider}", originalProvider);
            throw new InvalidOperationException($"All AI providers are unavailable. Provider attempted: {originalProvider}");
        }

        try
        {
            _logger.LogInformation("Executing fallback - trying provider: {FallbackProvider}", _fallbackProvider);
            
            var fallbackService = _aiServiceFactory.CreateService(_fallbackProvider);
            var result = await fallbackService.GenerateAsync(prompt);
            
            _logger.LogInformation("Fallback successful! Content generated using provider: {FallbackProvider}", _fallbackProvider);
            
            return result;
        }
        catch (Exception fallbackEx)
        {
            _logger.LogError(fallbackEx, "Fallback also failed for provider: {FallbackProvider}", _fallbackProvider);
            throw new InvalidOperationException($"All AI providers are unavailable. Providers attempted: {originalProvider}, {_fallbackProvider}", fallbackEx);
        }
    }
}

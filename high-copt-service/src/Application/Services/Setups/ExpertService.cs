using Domain.Entities;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;

namespace Application.Services.Setups;

public class ExpertService(IExpertRepository expertRepository, ITemplateService templateService, IIdGenerationService idGenerationService) : IExpertService
{
    public async Task<Expert> GetByIdAsync(int id)
    {
        return await expertRepository.GetByIdAsync(id);
    }

    public async Task<Expert> GetByExternalIdAsync(string externalId)
    {
        return await expertRepository.GetByExternalIdAsync(externalId);
    }

    public async Task<Expert> GetByIdOrExternalIdAsync(string identifier)
    {
        if (int.TryParse(identifier, out int id))
        {
            return await expertRepository.GetByIdAsync(id);
        }
        return await expertRepository.GetByExternalIdAsync(identifier);
    }

    public async Task<string> GenerateSetupTemplateAsync(string identifier, string? expertType = null)
    {
        var expert = await GetByIdOrExternalIdAsync(identifier);
        return await templateService.ProcessExpertTemplateAsync(expert, expertType);
    }

    public async Task<IEnumerable<Expert>> GetAllAsync()
    {
        return await expertRepository.GetAllAsync();
    }

    public async Task<Expert> CreateExpertAsync(Expert expert)
    {
        if (string.IsNullOrEmpty(expert.ExternalId))
        {
            expert.ExternalId = idGenerationService.GenerateExternalId();
        }
        
        if (expert.Id == 0)
        {
            expert.Id = await idGenerationService.GenerateUniqueIdAsync(() => expertRepository.GetAllAsync());
        }

        await expertRepository.AddAsync(expert);
        return expert;
    }

    public async Task UpdateAsync(Expert expert)
    {
        await expertRepository.UpdateAsync(expert);
        await expertRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        await expertRepository.DeleteAsync(id);
        await expertRepository.SaveChangesAsync();
    }
}
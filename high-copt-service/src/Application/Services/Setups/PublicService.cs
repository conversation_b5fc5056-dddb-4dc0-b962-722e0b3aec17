using Domain.Entities;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;

namespace Application.Services.Setups;

public class PublicService(IPublicRepository publicRepository, ITemplateService templateService, IIdGenerationService idGenerationService) : IPublicService
{
    public async Task<Public> GetByIdAsync(int id)
    {
        return await publicRepository.GetByIdAsync(id);
    }

    public async Task<Public> GetByExternalIdAsync(string externalId)
    {
        return await publicRepository.GetByExternalIdAsync(externalId);
    }

    public async Task<Public> GetByIdOrExternalIdAsync(string identifier)
    {
        if (int.TryParse(identifier, out int id))
        {
            return await publicRepository.GetByIdAsync(id);
        }
        return await publicRepository.GetByExternalIdAsync(identifier);
    }

    public async Task<string> GenerateSetupTemplateAsync(string identifier)
    {
        var publicEntity = await GetByIdOrExternalIdAsync(identifier);
        return await templateService.ProcessPublicTemplateAsync(publicEntity);
    }

    public async Task<IEnumerable<Public>> GetAllAsync()
    {
        return await publicRepository.GetAllAsync();
    }

    public async Task<Public> CreatePublicAsync(Public publicEntity)
    {
        ArgumentNullException.ThrowIfNull(publicEntity);
        
        if (string.IsNullOrEmpty(publicEntity.ExternalId))
        {
            publicEntity.ExternalId = idGenerationService.GenerateExternalId();
        }
        
        if (publicEntity.Id == 0)
        {
            publicEntity.Id = await idGenerationService.GenerateUniqueIdAsync(() => publicRepository.GetAllAsync());
        }

        await publicRepository.AddAsync(publicEntity);
        await publicRepository.SaveChangesAsync();
        return publicEntity;
    }

    public async Task UpdateAsync(Public publicEntity)
    {
        await publicRepository.UpdateAsync(publicEntity);
        await publicRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        await publicRepository.DeleteAsync(id);
        await publicRepository.SaveChangesAsync();
    }
}
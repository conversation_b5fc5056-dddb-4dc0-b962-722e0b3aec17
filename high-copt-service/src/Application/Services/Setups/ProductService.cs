using Domain.Entities;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;

namespace Application.Services.Setups;

public class ProductService(IProductRepository productRepository, ITemplateService templateService, IIdGenerationService idGenerationService) : IProductService
{
    public async Task<Product> GetByIdAsync(int id)
    {
        return await productRepository.GetByIdAsync(id);
    }

    public async Task<Product> GetByExternalIdAsync(string externalId)
    {
        return await productRepository.GetByExternalIdAsync(externalId);
    }

    public async Task<Product> GetByIdOrExternalIdAsync(string identifier)
    {
        if (int.TryParse(identifier, out int id))
        {
            return await productRepository.GetByIdAsync(id);
        }
        return await productRepository.GetByExternalIdAsync(identifier);
    }

    public async Task<string> GenerateSetupTemplateAsync(string identifier)
    {
        var product = await GetByIdOrExternalIdAsync(identifier);
        return await templateService.ProcessProductTemplateAsync(product);
    }

    public async Task<IEnumerable<Product>> GetAllAsync()
    {
        return await productRepository.GetAllAsync();
    }

    public async Task<Product> CreateProductAsync(Product product)
    {
        if (string.IsNullOrEmpty(product.ExternalId))
        {
            product.ExternalId = idGenerationService.GenerateExternalId();
        }
        
        if (product.Id == 0)
        {
            product.Id = await idGenerationService.GenerateUniqueIdAsync(() => productRepository.GetAllAsync());
        }

        await productRepository.AddAsync(product);
        return product;
    }

    public async Task UpdateAsync(Product product)
    {
        await productRepository.UpdateAsync(product);
        await productRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        await productRepository.DeleteAsync(id);
        await productRepository.SaveChangesAsync();
    }
}
using Domain.Entities;
using Domain.Enums.Messages;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace Application.Services;

public class MessageService : IMessageService
{
    private readonly IMessageRepository _messageRepository;
    private readonly ILogger<MessageService> _logger;

    public MessageService(IMessageRepository messageRepository, ILogger<MessageService> logger)
    {
        _messageRepository = messageRepository;
        _logger = logger;
    }

    public async Task<Message> SaveMessageAsync(string userPrompt, string aiResponse, string provider, MessageTypeEnum messageType, Dictionary<string, string>? metadata = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(userPrompt))
            {
                throw new ArgumentException("User prompt cannot be empty", nameof(userPrompt));
            }

            if (string.IsNullOrWhiteSpace(aiResponse))
            {
                throw new ArgumentException("AI response cannot be empty", nameof(aiResponse));
            }

            if (string.IsNullOrWhiteSpace(provider))
            {
                throw new ArgumentException("Provider cannot be empty", nameof(provider));
            }

            var message = new Message
            {
                UserPrompt = userPrompt,
                AiResponse = aiResponse,
                Provider = provider,
                MessageType = messageType.ToString(),
                Metadata = metadata ?? new Dictionary<string, string>()
            };

            var savedMessage = await _messageRepository.CreateAsync(message);
            
            _logger.LogInformation("Message saved successfully. Type: {MessageType}, Provider: {Provider}", 
                messageType, provider);

            return savedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving message");
            throw;
        }
    }

    public async Task<Message?> GetMessageByIdAsync(int id)
    {
        try
        {
            if (id <= 0)
            {
                throw new ArgumentException("ID must be greater than 0", nameof(id));
            }

            return await _messageRepository.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message by ID: {MessageId}", id);
            throw;
        }
    }

    public async Task<Message?> GetMessageByExternalIdAsync(string externalId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(externalId))
            {
                throw new ArgumentException("External ID cannot be empty", nameof(externalId));
            }

            return await _messageRepository.GetByExternalIdAsync(externalId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message by external ID: {ExternalId}", externalId);
            throw;
        }
    }


    public async Task<IEnumerable<Message>> GetRecentMessagesAsync(int count = 50)
    {
        try
        {
            if (count <= 0)
            {
                throw new ArgumentException("Count must be greater than 0", nameof(count));
            }

            return await _messageRepository.GetRecentMessagesAsync(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent messages");
            throw;
        }
    }

    public async Task<Message> UpdateMessageAsync(Message message)
    {
        try
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            if (message.Id <= 0)
            {
                throw new ArgumentException("Message ID must be greater than 0", nameof(message));
            }

            return await _messageRepository.UpdateAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating message");
            throw;
        }
    }

    public async Task DeleteMessageAsync(int id)
    {
        try
        {
            if (id <= 0)
            {
                throw new ArgumentException("ID must be greater than 0", nameof(id));
            }

            await _messageRepository.DeleteAsync(id);
            _logger.LogInformation("Message deleted successfully: {MessageId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message: {MessageId}", id);
            throw;
        }
    }
}

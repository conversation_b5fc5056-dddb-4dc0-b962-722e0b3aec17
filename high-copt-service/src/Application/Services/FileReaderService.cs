using System.Text;
using System.Data;
using CsvHelper;
using CsvHelper.Configuration;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ExcelDataReader;
using UglyToad.PdfPig;
using UglyToad.PdfPig.Content;
using Domain.Interfaces;
using System.Globalization;

namespace Application.Services;

public class FileReaderService : IFileReaderService
{
    private readonly Dictionary<string, Func<string, Task<string>>> _fileReaders;
    private readonly Dictionary<string, Func<Stream, Task<string>>> _streamReaders;

    public FileReaderService()
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        _fileReaders = new Dictionary<string, Func<string, Task<string>>>(StringComparer.OrdinalIgnoreCase)
        {
            { ".txt", ReadTextFileAsync },
            { ".csv", ReadCsvFileAsync },
            { ".pdf", ReadPdfFileAsync },
            { ".docx", ReadDocxFileAsync },
            { ".xlsx", ReadExcelFileAsync },
            { ".xls", ReadExcelFileAsync },
            { ".json", ReadTextFileAsync },
            { ".xml", ReadTextFileAsync },
            { ".html", ReadTextFileAsync },
            { ".htm", ReadTextFileAsync },
            { ".md", ReadTextFileAsync },
            { ".log", ReadTextFileAsync }
        };

        _streamReaders = new Dictionary<string, Func<Stream, Task<string>>>(StringComparer.OrdinalIgnoreCase)
        {
            { ".txt", ReadTextStreamAsync },
            { ".csv", ReadCsvStreamAsync },
            { ".pdf", ReadPdfStreamAsync },
            { ".docx", ReadDocxStreamAsync },
            { ".xlsx", ReadExcelStreamAsync },
            { ".xls", ReadExcelStreamAsync },
            { ".json", ReadTextStreamAsync },
            { ".xml", ReadTextStreamAsync },
            { ".html", ReadTextStreamAsync },
            { ".htm", ReadTextStreamAsync },
            { ".md", ReadTextStreamAsync },
            { ".log", ReadTextStreamAsync }
        };
    }

    public async Task<string> ReadFileAsync(string filePath)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"File not found: {filePath}");

        var extension = Path.GetExtension(filePath);
        
        if (!_fileReaders.TryGetValue(extension, out var reader))
            throw new NotSupportedException($"File type '{extension}' is not supported");

        try
        {
            return await reader(filePath);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error reading file '{filePath}': {ex.Message}", ex);
        }
    }

    public async Task<string> ReadFileAsync(Stream fileStream, string fileName)
    {
        var extension = Path.GetExtension(fileName);
        
        if (!_streamReaders.TryGetValue(extension, out var reader))
            throw new NotSupportedException($"File type '{extension}' is not supported");

        try
        {
            return await reader(fileStream);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error reading file stream '{fileName}': {ex.Message}", ex);
        }
    }

    public bool IsFileTypeSupported(string fileName)
    {
        var extension = Path.GetExtension(fileName);
        return _fileReaders.ContainsKey(extension);
    }

    public IEnumerable<string> GetSupportedExtensions()
    {
        return _fileReaders.Keys.ToList();
    }

   

    private async Task<string> ReadTextFileAsync(string filePath)
    {
        return await File.ReadAllTextAsync(filePath, Encoding.UTF8);
    }

    private async Task<string> ReadCsvFileAsync(string filePath)
    {
        using var reader = new StreamReader(filePath);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
            MissingFieldFound = null
        });

        var records = new List<Dictionary<string, object>>();
        await csv.ReadAsync();
        csv.ReadHeader();
        var headers = csv.HeaderRecord;

        if (headers == null) return string.Empty;

        while (await csv.ReadAsync())
        {
            var record = new Dictionary<string, object>();
            for (int i = 0; i < headers.Length; i++)
            {
                record[headers[i] ?? $"Column{i}"] = csv.GetField(i) ?? string.Empty;
            }
            records.Add(record);
        }

        var sb = new StringBuilder();
        sb.AppendLine(string.Join(", ", headers.Select(h => h ?? string.Empty)));
        foreach (var record in records)
        {
            sb.AppendLine(string.Join(", ", record.Values.Select(v => v.ToString() ?? string.Empty)));
        }

        return sb.ToString();
    }

    private async Task<string> ReadPdfFileAsync(string filePath)
    {
        return await Task.Run(() =>
        {
            using var document = PdfDocument.Open(filePath);
            var text = new StringBuilder();

            foreach (Page page in document.GetPages())
            {
                text.AppendLine(page.Text);
            }

            return text.ToString();
        });
    }

    private async Task<string> ReadDocxFileAsync(string filePath)
    {
        return await Task.Run(() =>
        {
            using var doc = WordprocessingDocument.Open(filePath, false);
            var body = doc.MainDocumentPart?.Document?.Body;
            
            if (body == null)
                return string.Empty;

            var text = new StringBuilder();
            foreach (var paragraph in body.Elements<Paragraph>())
            {
                text.AppendLine(paragraph.InnerText);
            }

            return text.ToString();
        });
    }

    private async Task<string> ReadExcelFileAsync(string filePath)
    {
        return await Task.Run(() =>
        {
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read);
            using var reader = ExcelReaderFactory.CreateReader(stream);
            var result = reader.AsDataSet();

            var sb = new StringBuilder();
            foreach (DataTable table in result.Tables)
            {
                sb.AppendLine($"Sheet: {table.TableName}");
                
                for (int row = 0; row < table.Rows.Count; row++)
                {
                    var values = new List<string>();
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        values.Add(table.Rows[row][col]?.ToString() ?? string.Empty);
                    }
                    sb.AppendLine(string.Join(", ", values));
                }
                sb.AppendLine();
            }

            return sb.ToString();
        });
    }

   

   

    private async Task<string> ReadTextStreamAsync(Stream stream)
    {
        using var reader = new StreamReader(stream, Encoding.UTF8);
        return await reader.ReadToEndAsync();
    }

    private async Task<string> ReadCsvStreamAsync(Stream stream)
    {
        using var reader = new StreamReader(stream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
            MissingFieldFound = null
        });

        var records = new List<Dictionary<string, object>>();
        await csv.ReadAsync();
        csv.ReadHeader();
        var headers = csv.HeaderRecord;

        if (headers == null) return string.Empty;

        while (await csv.ReadAsync())
        {
            var record = new Dictionary<string, object>();
            for (int i = 0; i < headers.Length; i++)
            {
                record[headers[i] ?? $"Column{i}"] = csv.GetField(i) ?? string.Empty;
            }
            records.Add(record);
        }

        var sb = new StringBuilder();
        sb.AppendLine(string.Join(", ", headers.Select(h => h ?? string.Empty)));
        foreach (var record in records)
        {
            sb.AppendLine(string.Join(", ", record.Values.Select(v => v.ToString() ?? string.Empty)));
        }

        return sb.ToString();
    }

    private async Task<string> ReadPdfStreamAsync(Stream stream)
    {
        return await Task.Run(() =>
        {
            using var document = PdfDocument.Open(stream);
            var text = new StringBuilder();

            foreach (Page page in document.GetPages())
            {
                text.AppendLine(page.Text);
            }

            return text.ToString();
        });
    }

    private async Task<string> ReadDocxStreamAsync(Stream stream)
    {
        return await Task.Run(() =>
        {
            using var doc = WordprocessingDocument.Open(stream, false);
            var body = doc.MainDocumentPart?.Document?.Body;
            
            if (body == null)
                return string.Empty;

            var text = new StringBuilder();
            foreach (var paragraph in body.Elements<Paragraph>())
            {
                text.AppendLine(paragraph.InnerText);
            }

            return text.ToString();
        });
    }

    private async Task<string> ReadExcelStreamAsync(Stream stream)
    {
        return await Task.Run(() =>
        {
            using var reader = ExcelReaderFactory.CreateReader(stream);
            var result = reader.AsDataSet();

            var sb = new StringBuilder();
            foreach (DataTable table in result.Tables)
            {
                sb.AppendLine($"Sheet: {table.TableName}");
                
                for (int row = 0; row < table.Rows.Count; row++)
                {
                    var values = new List<string>();
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        values.Add(table.Rows[row][col]?.ToString() ?? string.Empty);
                    }
                    sb.AppendLine(string.Join(", ", values));
                }
                sb.AppendLine();
            }

            return sb.ToString();
        });
    }

    
}

using Domain.Entities;
using Domain.Interfaces.Services;
using System.Text.RegularExpressions;

namespace Application.Services;

public class TemplateService : ITemplateService
{
    private readonly string _templatesPath;

    public TemplateService()
    {
        _templatesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
    }

    public async Task<string> ProcessExpertTemplateAsync(Expert expert, string? expertType = null)
    {
        var templatePath = Path.Combine(_templatesPath, "expert-setup.sbn");
        var template = await File.ReadAllTextAsync(templatePath);
        
        var processedTemplate = template
            .Replace("{{ expert_type }}", expertType ?? "Expert")
            .Replace("{{ expert.name }}", expert.Name)
            .Replace("{{ expert.area_of_expertise }}", expert.AreaOfExpertise)
            .Replace("{{ expert.biography }}", expert.Biography)
            .Replace("{{ expert.moment }}", expert.Moment)
            .<PERSON>lace("{{ expert.dolor }}", expert.Dolor)
            .<PERSON>lace("{{ expert.credibility }}", expert.Credibility)
            .<PERSON>lace("{{ expert.recognition }}", expert.Recognition)
            .Replace("{{ expert.track_record }}", expert.TrackRecord)
            .Replace("{{ expert.how_product_works }}", expert.HowProductWorks)
            .Replace("{{ expert.voice_personality }}", expert.VoicePersonality)
            .Replace("{{ expert.essential_values }}", expert.EssentialValues)
            .Replace("{{ expert.enterprise }}", expert.Enterprise);

        return ProcessConditionalBlocks(processedTemplate, expertType);
    }

    public async Task<string> ProcessCampaignTemplateAsync(Campaign campaign, Expert? expert = null, Product? product = null, Public? publicEntity = null)
    {
        var templatePath = Path.Combine(_templatesPath, "campaign-setup.sbn");
        var template = await File.ReadAllTextAsync(templatePath);
        
        var processedTemplate = template
            .Replace("{{ campaign.type }}", campaign.Type.ToString())
            .Replace("{{ campaign.conscience_level }}", campaign.ConscienceLevel.ToString())
            .Replace("{{ campaign.sophistication_level }}", campaign.SophisticationLevel.ToString())
            .Replace("{{ campaign.ideia }}", campaign.Ideia)
            .Replace("{{ campaign.emotion }}", campaign.Emotion)
            .Replace("{{ campaign.belief }}", campaign.Belief);

        // Replace expert data if provided
        if (expert != null)
        {
            processedTemplate = processedTemplate
                .Replace("{{ expert.name }}", expert.Name)
                .Replace("{{ expert.area_of_expertise }}", expert.AreaOfExpertise)
                .Replace("{{ expert.biography }}", expert.Biography)
                .Replace("{{ expert.moment }}", expert.Moment)
                .Replace("{{ expert.dolor }}", expert.Dolor)
                .Replace("{{ expert.credibility }}", expert.Credibility)
                .Replace("{{ expert.recognition }}", expert.Recognition)
                .Replace("{{ expert.track_record }}", expert.TrackRecord)
                .Replace("{{ expert.how_product_works }}", expert.HowProductWorks)
                .Replace("{{ expert.voice_personality }}", expert.VoicePersonality)
                .Replace("{{ expert.essential_values }}", expert.EssentialValues)
                .Replace("{{ expert.enterprise }}", expert.Enterprise);
        }

        // Replace product data if provided
        if (product != null)
        {
            processedTemplate = processedTemplate
                .Replace("{{ product.name }}", product.Name)
                .Replace("{{ product.benefits }}", product.Benefits)
                .Replace("{{ product.social_proof }}", product.SocialProof)
                .Replace("{{ product.metodology }}", product.Metodology)
                .Replace("{{ product.guarantee }}", product.Guarantee)
                .Replace("{{ product.deliverables }}", product.Deliverables.ToString())
                .Replace("{{ product.customer_journey }}", product.CustomerJourney.ToString());
        }

        // Replace public data if provided
        if (publicEntity != null)
        {
            processedTemplate = processedTemplate
                .Replace("{{ public.name }}", publicEntity.Name)
                .Replace("{{ public.local }}", publicEntity.Local)
                .Replace("{{ public.family_situation }}", publicEntity.FamilySituation)
                .Replace("{{ public.personality }}", publicEntity.Personality)
                .Replace("{{ public.hobbies }}", publicEntity.Hobbies)
                .Replace("{{ public.lifestyle }}", publicEntity.Lifestyle)
                .Replace("{{ public.personal_value }}", publicEntity.PersonalValue)
                .Replace("{{ public.roof }}", publicEntity.Roof)
                .Replace("{{ public.next_level }}", publicEntity.NextLevel)
                .Replace("{{ public.drop_of_water }}", publicEntity.DropOfWater)
                .Replace("{{ public.beliefs }}", publicEntity.Beliefs)
                .Replace("{{ public.self_visions }}", publicEntity.SelfVisions)
                .Replace("{{ public.possible_objections }}", publicEntity.PossibleObjections)
                .Replace("{{ public.own_communication }}", publicEntity.OwnCommunication);
        }

        return ProcessConditionalBlocks(processedTemplate, campaign.Type.ToString());
    }

    public async Task<string> ProcessProductTemplateAsync(Product product)
    {
        var templatePath = Path.Combine(_templatesPath, "product-setup.sbn");
        var template = await File.ReadAllTextAsync(templatePath);
        
        var processedTemplate = template
            .Replace("{{ product.name }}", product.Name)
            .Replace("{{ product.benefits }}", product.Benefits)
            .Replace("{{ product.social_proof }}", product.SocialProof)
            .Replace("{{ product.metodology }}", product.Metodology)
            .Replace("{{ product.guarantee }}", product.Guarantee)
            .Replace("{{ product.deliverables }}", product.Deliverables.ToString())
            .Replace("{{ product.customer_journey }}", product.CustomerJourney.ToString());

        return ProcessConditionalBlocks(processedTemplate, product.Deliverables.ToString());
    }

    public async Task<string> ProcessPublicTemplateAsync(Public publicEntity)
    {
        var templatePath = Path.Combine(_templatesPath, "public-setup.sbn");
        var template = await File.ReadAllTextAsync(templatePath);
        
        var processedTemplate = template
            .Replace("{{ public.name }}", publicEntity.Name)
            .Replace("{{ public.local }}", publicEntity.Local)
            .Replace("{{ public.family_situation }}", publicEntity.FamilySituation)
            .Replace("{{ public.personality }}", publicEntity.Personality)
            .Replace("{{ public.hobbies }}", publicEntity.Hobbies)
            .Replace("{{ public.lifestyle }}", publicEntity.Lifestyle)
            .Replace("{{ public.personal_value }}", publicEntity.PersonalValue)
            .Replace("{{ public.roof }}", publicEntity.Roof)
            .Replace("{{ public.next_level }}", publicEntity.NextLevel)
            .Replace("{{ public.drop_of_water }}", publicEntity.DropOfWater)
            .Replace("{{ public.beliefs }}", publicEntity.Beliefs)
            .Replace("{{ public.self_visions }}", publicEntity.SelfVisions)
            .Replace("{{ public.possible_objections }}", publicEntity.PossibleObjections)
            .Replace("{{ public.own_communication }}", publicEntity.OwnCommunication);

        return processedTemplate;
    }

    private string ProcessConditionalBlocks(string template, string? contextValue)
    {
        // Process {{ if }} blocks
        var ifPattern = @"\{\{\s*if\s+([^}]+)\s*\}\}(.*?)\{\{\s*else\s*if\s+([^}]+)\s*\}\}(.*?)\{\{\s*else\s*\}\}(.*?)\{\{\s*end\s*\}\}";
        var matches = Regex.Matches(template, ifPattern, RegexOptions.Singleline);
        
        foreach (Match match in matches)
        {
            var condition1 = match.Groups[1].Value.Trim();
            var content1 = match.Groups[2].Value;
            var condition2 = match.Groups[3].Value.Trim();
            var content2 = match.Groups[4].Value;
            var elseContent = match.Groups[5].Value;
            
            string replacement = "";
            
            if (EvaluateCondition(condition1, contextValue))
            {
                replacement = content1;
            }
            else if (EvaluateCondition(condition2, contextValue))
            {
                replacement = content2;
            }
            else
            {
                replacement = elseContent;
            }
            
            template = template.Replace(match.Value, replacement);
        }

        // Process simple {{ if }} blocks without else if
        var simpleIfPattern = @"\{\{\s*if\s+([^}]+)\s*\}\}(.*?)\{\{\s*else\s*\}\}(.*?)\{\{\s*end\s*\}\}";
        var simpleMatches = Regex.Matches(template, simpleIfPattern, RegexOptions.Singleline);
        
        foreach (Match match in simpleMatches)
        {
            var condition = match.Groups[1].Value.Trim();
            var ifContent = match.Groups[2].Value;
            var elseContent = match.Groups[3].Value;
            
            string replacement = EvaluateCondition(condition, contextValue) ? ifContent : elseContent;
            template = template.Replace(match.Value, replacement);
        }

        return template;
    }

    private bool EvaluateCondition(string condition, string? contextValue)
    {
        // Simple condition evaluation for template processing
        if (condition.Contains("=="))
        {
            var parts = condition.Split("==").Select(p => p.Trim().Trim('"')).ToArray();
            if (parts.Length == 2)
            {
                return parts[1].Equals(contextValue, StringComparison.OrdinalIgnoreCase);
            }
        }
        
        return false;
    }
}

using Application.Services.AI;
using Application.Services;
using Application.Services.Setups;
using Domain.Interfaces;
using Domain.Interfaces.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Application;

public static class DependencyInjection
{
    public static void AddApplicationServices(this IServiceCollection services)
    {
        services.AddHttpClient<ChatGptService>();
        
        services.AddTransient<ChatGptService>();
        services.AddTransient<GeminiService>();
        services.AddSingleton<IAiServiceFactory, AiServiceFactory>();
        services.AddScoped<IAiOrchestrator, AiOrchestrator>();
        
        services.AddScoped<IFileReaderService, FileReaderService>();
        services.AddScoped<ITemplateService, TemplateService>();
        services.AddScoped<IContentService, ContentService>();
        services.AddScoped<IIdGenerationService, IdGenerationService>();
        services.AddScoped<IPromptGenerationService, PromptGenerationService>();
        services.AddScoped<ICompleteSetupService, CompleteSetupService>();
        services.AddScoped<IMessageService, MessageService>();
        
        services.AddScoped<IExpertService, ExpertService>();
        services.AddScoped<ICampaignService, CampaignService>();
        services.AddScoped<IProductService, ProductService>();
        services.AddScoped<IPublicService, PublicService>();
    }
}

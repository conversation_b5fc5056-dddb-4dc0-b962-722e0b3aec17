using Domain.Entities;
using Scriban;

namespace Application.Prompts.Setups;

/// <summary>
/// Static class for generating public/audience setup prompts using Scriban templates
/// </summary>
public static class PublicSetup
{
    private static readonly Template _publicTemplate;
    
    static PublicSetup()
    {
        var assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
        var assemblyDir = Path.GetDirectoryName(assemblyPath);
        var templatePath = Path.Combine(assemblyDir!, "Templates", "public-setup.sbn");
        
        if (!File.Exists(templatePath))
        {
            var currentDir = Directory.GetCurrentDirectory();
            templatePath = Path.Combine(currentDir, "src", "Application", "Templates", "public-setup.sbn");
        }
        
        var templateContent = File.ReadAllText(templatePath);
        _publicTemplate = Template.Parse(templateContent);
    }
    
    public static string PublicSetupPrompt(Public publicTarget)
    {
        var model = new
        {
            @public = new
            {
                name = publicTarget.Name,
                local = publicTarget.Local,
                family_situation = publicTarget.FamilySituation,
                personality = publicTarget.Personality,
                hobbies = publicTarget.Hobbies,
                lifestyle = publicTarget.Lifestyle,
                personal_value = publicTarget.PersonalValue,
                roof = publicTarget.Roof,
                next_level = publicTarget.NextLevel,
                drop_of_water = publicTarget.DropOfWater,
                beliefs = publicTarget.Beliefs,
                self_visions = publicTarget.SelfVisions,
                possible_objections = publicTarget.PossibleObjections,
                own_communication = publicTarget.OwnCommunication
            }
        };
        
        return _publicTemplate.Render(model);
    }
}

using Domain.Entities;
using <PERSON><PERSON>ban;

namespace Application.Prompts.Setups;

public static class ExpertSetup
{
    private static Template GetTemplate()
    {
        var assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
        var assemblyDir = Path.GetDirectoryName(assemblyPath);
        var templatePath = Path.Combine(assemblyDir!, "Templates", "expert-setup.sbn");
        
        if (!File.Exists(templatePath))
        {
            var currentDir = Directory.GetCurrentDirectory();
            templatePath = Path.Combine(currentDir, "src", "Application", "Templates", "expert-setup.sbn");
        }
        
        if (!File.Exists(templatePath))
        {
            throw new FileNotFoundException($"Template expert-setup.sbn não encontrado. Procurado em: {templatePath}");
        }
        
        var templateContent = File.ReadAllText(templatePath);
        return Template.Parse(templateContent);
    }
    
    public static string ExpertSetupPrompt(Expert expert)
    {
        var template = GetTemplate();
        var model = new
        {
            expert_type = "Expert",
            expert = new
            {
                name = expert.Name,
                area_of_expertise = expert.AreaOfExpertise,
                biography = expert.Biography,
                moment = expert.Moment,
                dolor = expert.Dolor,
                credibility = expert.Credibility,
                recognition = expert.Recognition,
                track_record = expert.TrackRecord,
                how_product_works = expert.HowProductWorks,
                voice_personality = expert.VoicePersonality,
                essential_values = expert.EssentialValues,
                enterprise = expert.Enterprise
            }
        };
        
        return template.Render(model);
    }
    
    public static string CompanySetupPrompt(Expert expert)
    {
        var template = GetTemplate();
        var model = new
        {
            expert_type = "Company",
            expert = new
            {
                name = expert.Name,
                area_of_expertise = expert.AreaOfExpertise,
                biography = expert.Biography,
                moment = expert.Moment,
                dolor = expert.Dolor,
                credibility = expert.Credibility,
                recognition = expert.Recognition,
                track_record = expert.TrackRecord,
                how_product_works = expert.HowProductWorks,
                voice_personality = expert.VoicePersonality,
                essential_values = expert.EssentialValues,
                enterprise = expert.Enterprise
            }
        };
        
        return template.Render(model);
    }
    
    public static string GenerateSetupPrompt(Expert expert, string expertType)
    {
        var template = GetTemplate();
        var model = new
        {
            expert_type = expertType,
            expert = new
            {
                name = expert.Name,
                area_of_expertise = expert.AreaOfExpertise,
                biography = expert.Biography,
                moment = expert.Moment,
                dolor = expert.Dolor,
                credibility = expert.Credibility,
                recognition = expert.Recognition,
                track_record = expert.TrackRecord,
                how_product_works = expert.HowProductWorks,
                voice_personality = expert.VoicePersonality,
                essential_values = expert.EssentialValues,
                enterprise = expert.Enterprise
            }
        };
        
        return template.Render(model);
    }
}

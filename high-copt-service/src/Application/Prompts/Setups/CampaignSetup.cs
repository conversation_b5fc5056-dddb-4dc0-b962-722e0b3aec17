using Domain.Entities;
using Sc<PERSON>ban;

namespace Application.Prompts.Setups;

public static class CampaignSetup
{
    private static readonly Template _campaignTemplate;
    
    static CampaignSetup()
    {
        var assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
        var assemblyDir = Path.GetDirectoryName(assemblyPath);
        var templatePath = Path.Combine(assemblyDir!, "Templates", "campaign-setup.sbn");
        
        if (!File.Exists(templatePath))
        {
            var currentDir = Directory.GetCurrentDirectory();
            templatePath = Path.Combine(currentDir, "src", "Application", "Templates", "campaign-setup.sbn");
        }
        
        var templateContent = File.ReadAllText(templatePath);
        _campaignTemplate = Template.Parse(templateContent);
    }
    
    public static string CampaignSetupPrompt(Campaign campaign, Product product, Expert expert, Public publicTarget)
    {
        var model = new
        {
            campaign = new
            {
                type = campaign.Type.ToString(),
                conscience_level = campaign.ConscienceLevel.ToString(),
                sophistication_level = campaign.SophisticationLevel.ToString(),
                ideia = campaign.Ideia,
                emotion = campaign.Emotion,
                belief = campaign.Belief
            },
            product = new
            {
                name = product.Name,
                benefits = product.Benefits,
                social_proof = product.SocialProof,
                metodology = product.Metodology,
                guarantee = product.Guarantee,
                deliverables = product.Deliverables.ToString(),
                customer_journey = product.CustomerJourney.ToString()
            },
            expert = new
            {
                name = expert.Name,
                area_of_expertise = expert.AreaOfExpertise,
                biography = expert.Biography,
                moment = expert.Moment,
                dolor = expert.Dolor,
                credibility = expert.Credibility,
                recognition = expert.Recognition,
                track_record = expert.TrackRecord,
                how_product_works = expert.HowProductWorks,
                voice_personality = expert.VoicePersonality,
                essential_values = expert.EssentialValues,
                enterprise = expert.Enterprise
            },
            @public = new
            {
                name = publicTarget.Name,
                local = publicTarget.Local,
                family_situation = publicTarget.FamilySituation,
                personality = publicTarget.Personality,
                hobbies = publicTarget.Hobbies,
                lifestyle = publicTarget.Lifestyle,
                personal_value = publicTarget.PersonalValue,
                roof = publicTarget.Roof,
                next_level = publicTarget.NextLevel,
                drop_of_water = publicTarget.DropOfWater,
                beliefs = publicTarget.Beliefs,
                self_visions = publicTarget.SelfVisions,
                possible_objections = publicTarget.PossibleObjections,
                own_communication = publicTarget.OwnCommunication
            }
        };
        
        return _campaignTemplate.Render(model);
    }
}

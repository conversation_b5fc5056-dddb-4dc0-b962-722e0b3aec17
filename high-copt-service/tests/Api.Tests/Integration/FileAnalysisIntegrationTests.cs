using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;
using System.Text;
using Xunit;
using FluentAssertions;
using System.Net;

namespace Api.Tests.Integration;

public class FileAnalysisIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public FileAnalysisIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task AnalyzeFile_WithTextFile_ShouldReturnSuccessfulAnalysis()
    {
        var content = "Este é um arquivo de teste com dados importantes sobre vendas.";
        var fileName = "vendas.txt";
        
        using var formContent = new MultipartFormDataContent();
        var fileContent = new ByteArrayContent(Encoding.UTF8.GetBytes(content));
        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
        formContent.Add(fileContent, "file", fileName);
        formContent.Add(new StringContent("chatgpt"), "provider");

        var response = await _client.PostAsync("/api/content/analyze-file", formContent);

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().Contain("success");
        responseContent.Should().Contain(fileName);
    }

    [Fact]
    public async Task AnalyzeFile_WithCsvData_ShouldReturnStructuredAnalysis()
    {
        var csvContent = "Nome,Idade,Cidade\nJoão,25,São Paulo\nMaria,30,Rio de Janeiro";
        var fileName = "dados.csv";
        
        using var formContent = new MultipartFormDataContent();
        var fileContent = new ByteArrayContent(Encoding.UTF8.GetBytes(csvContent));
        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/csv");
        formContent.Add(fileContent, "file", fileName);

        var response = await _client.PostAsync("/api/content/analyze-file", formContent);

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().Contain("success");
        responseContent.Should().Contain(".csv");
    }

    [Fact]
    public async Task AnalyzeFile_WithUnsupportedFileType_ShouldReturnBadRequest()
    {
        var content = "binary data";
        var fileName = "image.png";
        
        using var formContent = new MultipartFormDataContent();
        var fileContent = new ByteArrayContent(Encoding.UTF8.GetBytes(content));
        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/png");
        formContent.Add(fileContent, "file", fileName);

        var response = await _client.PostAsync("/api/content/analyze-file", formContent);

        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().Contain("not supported");
    }

    [Fact]
    public async Task AnalyzeFile_WithoutFile_ShouldReturnBadRequest()
    {
        using var formContent = new MultipartFormDataContent();

        var response = await _client.PostAsync("/api/content/analyze-file", formContent);

        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var responseContent = await response.Content.ReadAsStringAsync();
        // ASP.NET Core retorna erro de validação de formulário quando não há arquivo
        responseContent.Should().Contain("Failed to read the request form");
    }

    [Fact]
    public async Task GetProviders_ShouldReturnAvailableProviders()
    {
        var response = await _client.GetAsync("/api/content/providers");

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().Contain("chatgpt");
        responseContent.Should().Contain("gemini");
    }

    [Fact]
    public async Task CheckHealth_ShouldReturnHealthStatus()
    {
        var response = await _client.GetAsync("/api/content/health");

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().Contain("ChatGPT");
        responseContent.Should().Contain("Gemini");
    }
}

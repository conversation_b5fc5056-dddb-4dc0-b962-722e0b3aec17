using Application.Services.AI;
using Application.Services;
using Domain.Interfaces;
using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Moq;
using Xunit;
using FluentAssertions;
using Api.Controllers;
using Application.Contracts.DTOs;
using Domain.Entities;
using Domain.Enums.Campaing;
using Domain.Enums.Products;
using Domain.Enums.Public;
using System.Text;

namespace Api.Tests.Controllers;

public class ContentControllerTests
{
    private readonly Mock<IContentService> _mockContentService;
    private readonly Mock<ICompleteSetupService> _mockCompleteSetupService;
    private readonly Mock<ICampaignService> _mockCampaignService;
    private readonly Mock<IExpertService> _mockExpertService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IPublicService> _mockPublicService;
    private readonly Mock<ILogger<ContentController>> _mockLogger;
    private readonly ContentController _controller;

    public ContentControllerTests()
    {
        _mockContentService = new Mock<IContentService>();
        _mockCompleteSetupService = new Mock<ICompleteSetupService>();
        _mockCampaignService = new Mock<ICampaignService>();
        _mockExpertService = new Mock<IExpertService>();
        _mockProductService = new Mock<IProductService>();
        _mockPublicService = new Mock<IPublicService>();
        _mockLogger = new Mock<ILogger<ContentController>>();
        
        _controller = new ContentController(
            _mockContentService.Object,
            _mockCompleteSetupService.Object,
            _mockCampaignService.Object,
            _mockExpertService.Object,
            _mockProductService.Object,
            _mockPublicService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateContent_WithValidRequest_ShouldReturnOk()
    {
        var request = new GenerateContentRequest { Prompt = "Test prompt", PreferredProvider = "chatgpt" };
        var expectedResponse = "AI generated content";
        
        _mockContentService.Setup(x => x.GenerateContentAsync(request.Prompt, request.PreferredProvider))
            .ReturnsAsync(expectedResponse);

        var result = await _controller.GenerateContent(request);

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<GenerateContentResponse>().Subject;
        response.Success.Should().BeTrue();
        response.Content.Should().Be(expectedResponse);
        response.Provider.Should().Be(request.PreferredProvider);
    }

    [Fact]
    public async Task GenerateContent_WithEmptyPrompt_ShouldReturnBadRequest()
    {
        var request = new GenerateContentRequest { Prompt = "" };

        var result = await _controller.GenerateContent(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateContentResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Be("Prompt cannot be empty");
    }

    [Fact]
    public async Task GenerateContent_WithInvalidProvider_ShouldReturnBadRequest()
    {
        var request = new GenerateContentRequest { Prompt = "Test prompt", PreferredProvider = "invalid" };
        
        _mockContentService.Setup(x => x.GenerateContentAsync(request.Prompt, request.PreferredProvider))
            .ThrowsAsync(new ArgumentException("Invalid provider"));

        var result = await _controller.GenerateContent(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateContentResponse>().Subject;
        response.Success.Should().BeFalse();
    }

    [Fact]
    public async Task GenerateContent_WithServiceError_ShouldReturnInternalServerError()
    {
        var request = new GenerateContentRequest { Prompt = "Test prompt" };
        
        _mockContentService.Setup(x => x.GenerateContentAsync(request.Prompt, request.PreferredProvider))
            .ThrowsAsync(new Exception("Service error"));

        var result = await _controller.GenerateContent(request);

        var errorResult = result.Should().BeOfType<ObjectResult>().Subject;
        errorResult.StatusCode.Should().Be(500);
        var response = errorResult.Value.Should().BeOfType<GenerateContentResponse>().Subject;
        response.Success.Should().BeFalse();
    }

    [Fact]
    public async Task GenerateCompleteCopy_WithValidRequest_ShouldReturnOk()
    {
        var request = new GenerateCompleteCopyRequest
        {
            Campaign = new CampaignDto 
            { 
                Name = "Test Campaign",
                Type = CampaingTypeEnum.Launch,
                ConscienceLevel = CampaingConscienceLevelEnum.TotallyUnconscious,
                SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent,
                Ideia = "Test Idea",
                Emotion = "Test Emotion",
                Belief = "Test Belief"
            },
            Expert = new ExpertDto { Name = "Test Expert", AreaOfExpertise = "Marketing" },
            Product = new ProductDto 
            { 
                Name = "Test Product", 
                Benefits = "Great benefits",
                SocialProof = "Customer testimonials",
                Metodology = "Our methodology",
                Guarantee = "Money back guarantee",
                Deliverables = ProductDeliverablesEnum.SessionMentoring,
                CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
            },
            Public = new PublicDto 
            { 
                Name = "Test Public",
                Local = "Test location",
                FamilySituation = "Test family",
                Personality = "Test personality",
                Hobbies = "Test hobbies",
                Lifestyle = "Test lifestyle",
                PersonalValue = "Test values",
                Roof = "Test roof",
                NextLevel = "Test next level",
                DropOfWater = "Test drop",
                Beliefs = "Test beliefs",
                SelfVisions = "Test visions",
                PossibleObjections = "Test objections",
                OwnCommunication = "Test communication",
                PublicGender = PublicGenderEnum.Men,
                PublicEducationLevel = PublicEducationLevelEnum.CompleteHighSchool
            },
            CopyRequest = "Generate sales copy",
            Provider = "chatgpt"
        };
        var expectedCopy = "Generated copy content";

        _mockCompleteSetupService.Setup(x => x.GenerateCompleteCopyAsync(
            It.IsAny<Campaign>(), It.IsAny<Expert>(), It.IsAny<Product>(), It.IsAny<Public>(), 
            request.CopyRequest, request.Provider))
            .ReturnsAsync(expectedCopy);

        var result = await _controller.GenerateCompleteCopy(request);

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeTrue();
        response.GeneratedCopy.Should().Be(expectedCopy);
        response.CopyRequest.Should().Be(request.CopyRequest);
    }

    [Fact]
    public async Task GenerateCompleteCopy_WithEmptyCopyRequest_ShouldReturnBadRequest()
    {
        var request = new GenerateCompleteCopyRequest
        {
            Campaign = new CampaignDto { Name = "Test" },
            Expert = new ExpertDto { Name = "Test" },
            Product = new ProductDto { Name = "Test" },
            Public = new PublicDto { Name = "Test" },
            CopyRequest = ""
        };

        var result = await _controller.GenerateCompleteCopy(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Be("Copy request cannot be empty");
    }

    [Fact]
    public async Task GenerateCompleteCopy_WithInvalidEnumValue_ShouldReturnBadRequest()
    {
        var request = new GenerateCompleteCopyRequest
        {
            Campaign = new CampaignDto { Name = "Test" },
            Expert = new ExpertDto { Name = "Test" },
            Product = new ProductDto { Name = "Test" },
            Public = new PublicDto { Name = "Test" },
            CopyRequest = "Test request"
        };

        _mockCompleteSetupService.Setup(x => x.GenerateCompleteCopyAsync(
            It.IsAny<Campaign>(), It.IsAny<Expert>(), It.IsAny<Product>(), It.IsAny<Public>(), 
            request.CopyRequest, request.Provider))
            .ThrowsAsync(new ArgumentException("Invalid enum value"));

        var result = await _controller.GenerateCompleteCopy(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Contain("Invalid campaign type");
    }

    [Fact]
    public async Task GenerateCompleteCopyById_WithValidRequest_ShouldReturnOk()
    {
        var request = new GenerateCompleteCopyByIdRequest
        {
            CampaignId = "camp-1",
            ExpertId = "exp-1",
            ProductId = "prod-1",
            PublicId = "pub-1",
            CopyRequest = "Generate copy",
            Provider = "chatgpt"
        };

        var campaign = new Campaign 
        { 
            Id = 1, 
            Name = "Test Campaign",
            Ideia = "Test idea",
            Emotion = "Test emotion",
            Belief = "Test belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.TotallyUnconscious,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };
        var expert = new Expert 
        { 
            Id = 1, 
            Name = "Test Expert",
            AreaOfExpertise = "Marketing",
            Biography = "Expert bio",
            Moment = "Current moment",
            Dolor = "Pain point",
            Credibility = "High credibility",
            Recognition = "Awards",
            TrackRecord = "Track record",
            HowProductWorks = "Product explanation",
            VoicePersonality = "Professional",
            EssentialValues = "Core values",
            Enterprise = "Company name"
        };
        var product = new Product 
        { 
            Id = 1, 
            Name = "Test Product",
            Benefits = "Great benefits",
            SocialProof = "Customer testimonials",
            Metodology = "Our methodology",
            Guarantee = "Money back guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };
        var publicTarget = new Public 
        { 
            Id = 1, 
            Name = "Test Public",
            Local = "Test location",
            FamilySituation = "Test family",
            Personality = "Test personality",
            Hobbies = "Test hobbies",
            Lifestyle = "Test lifestyle",
            PersonalValue = "Test values",
            Roof = "Test roof",
            NextLevel = "Test next level",
            DropOfWater = "Test drop",
            Beliefs = "Test beliefs",
            SelfVisions = "Test visions",
            PossibleObjections = "Test objections",
            OwnCommunication = "Test communication",
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool,
            Gender = PublicGenderEnum.Men
        };
        var expectedCopy = "Generated copy";

        _mockCampaignService.Setup(x => x.GetByIdOrExternalIdAsync(request.CampaignId))
            .ReturnsAsync(campaign);
        _mockExpertService.Setup(x => x.GetByIdOrExternalIdAsync(request.ExpertId))
            .ReturnsAsync(expert);
        _mockProductService.Setup(x => x.GetByIdOrExternalIdAsync(request.ProductId))
            .ReturnsAsync(product);
        _mockPublicService.Setup(x => x.GetByIdOrExternalIdAsync(request.PublicId))
            .ReturnsAsync(publicTarget);
        _mockCompleteSetupService.Setup(x => x.GenerateCompleteCopyAsync(
            campaign, expert, product, publicTarget, request.CopyRequest, request.Provider))
            .ReturnsAsync(expectedCopy);

        var result = await _controller.GenerateCompleteCopyById(request);

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeTrue();
        response.GeneratedCopy.Should().Be(expectedCopy);
    }

    [Fact]
    public async Task GenerateCompleteCopyById_WithEmptyCopyRequest_ShouldReturnBadRequest()
    {
        var request = new GenerateCompleteCopyByIdRequest
        {
            CampaignId = "camp-1",
            ExpertId = "exp-1",
            ProductId = "prod-1",
            PublicId = "pub-1",
            CopyRequest = ""
        };

        var result = await _controller.GenerateCompleteCopyById(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Be("Copy request cannot be empty");
    }

    [Fact]
    public async Task GenerateCompleteCopyById_WithMissingIds_ShouldReturnBadRequest()
    {
        var request = new GenerateCompleteCopyByIdRequest
        {
            CampaignId = "",
            ExpertId = "exp-1",
            ProductId = "prod-1",
            PublicId = "pub-1",
            CopyRequest = "Test request"
        };

        var result = await _controller.GenerateCompleteCopyById(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Be("All setup IDs (Campaign, Expert, Product, Public) are required");
    }

    [Fact]
    public async Task GenerateCompleteCopyById_WithEntityNotFound_ShouldReturnNotFound()
    {
        var request = new GenerateCompleteCopyByIdRequest
        {
            CampaignId = "missing",
            ExpertId = "exp-1",
            ProductId = "prod-1",
            PublicId = "pub-1",
            CopyRequest = "Test request"
        };

        _mockCampaignService.Setup(x => x.GetByIdOrExternalIdAsync(request.CampaignId))
            .ThrowsAsync(new KeyNotFoundException("Campaign not found"));

        var result = await _controller.GenerateCompleteCopyById(request);

        var notFoundResult = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var response = notFoundResult.Value.Should().BeOfType<GenerateCompleteCopyResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Contain("Campaign not found");
    }

    [Fact]
    public void GetProviders_ShouldReturnProvidersList()
    {
        var result = _controller.GetProviders();

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var providers = okResult.Value.Should().BeAssignableTo<object[]>().Subject;
        providers.Should().HaveCount(2);
    }

    [Fact]
    public void GetHealth_ShouldReturnHealthStatus()
    {
        var result = _controller.GetHealth();

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        okResult.Value.Should().NotBeNull();
    }
}

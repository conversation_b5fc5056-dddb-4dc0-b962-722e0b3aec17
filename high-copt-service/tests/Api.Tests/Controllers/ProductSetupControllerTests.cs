using Api.Controllers;
using Application.Contracts.DTOs;
using Domain.Entities;
using Domain.Enums.Products;
using Domain.Interfaces.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Api.Tests.Controllers;

public class ProductSetupControllerTests
{
    private readonly Mock<IProductService> _mockService;
    private readonly Mock<ILogger<ProductSetupController>> _mockLogger;
    private readonly ProductSetupController _controller;

    public ProductSetupControllerTests()
    {
        _mockService = new Mock<IProductService>();
        _mockLogger = new Mock<ILogger<ProductSetupController>>();
        _controller = new ProductSetupController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task CreateProduct_WithValidDto_ShouldReturnCreated()
    {
        var createDto = new CreateProductDto
        {
            Name = "Test Product",
            Benefits = "Great benefits",
            SocialProof = "Customer testimonials",
            Metodology = "Our methodology",
            Guarantee = "Money back guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };

        var createdProduct = new Product
        {
            Id = 1,
            ExternalId = "prod-123",
            Name = createDto.Name,
            Benefits = createDto.Benefits,
            SocialProof = createDto.SocialProof,
            Metodology = createDto.Metodology,
            Guarantee = createDto.Guarantee,
            Deliverables = createDto.Deliverables,
            CustomerJourney = createDto.CustomerJourney
        };

        _mockService.Setup(s => s.CreateProductAsync(It.IsAny<Product>()))
            .ReturnsAsync(createdProduct);

        var result = await _controller.CreateProduct(createDto);

        var createdResult = result.Should().BeOfType<CreatedAtActionResult>().Subject;
        createdResult.ActionName.Should().Be(nameof(_controller.GetProduct));
        createdResult.Value.Should().Be(createdProduct);
    }

    [Fact]
    public async Task CreateProduct_WithServiceError_ShouldReturn500()
    {
        var createDto = new CreateProductDto 
        { 
            Name = "Test Product",
            Benefits = "Test benefits",
            SocialProof = "Test social proof",
            Metodology = "Test methodology",
            Guarantee = "Test guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };

        _mockService.Setup(s => s.CreateProductAsync(It.IsAny<Product>()))
            .ThrowsAsync(new Exception("Database error"));

        var result = await _controller.CreateProduct(createDto);

        var statusResult = result.Should().BeOfType<ObjectResult>().Subject;
        statusResult.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnOk_WhenSuccess()
    {
        var id = "prod-1";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ReturnsAsync("TEMPLATE");

        var result = await _controller.GenerateSetupTemplate(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        var payload = ok.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeTrue();
        payload.EntityType.Should().Be("Product");
        payload.Identifier.Should().Be(id);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new KeyNotFoundException("not found"));

        var result = await _controller.GenerateSetupTemplate(id);

        var nf = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var payload = nf.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
        payload.Message.Should().Be("not found");
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GenerateSetupTemplate(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GetProduct_ShouldReturnOk_WhenFound()
    {
        var id = "prod-1";
        var entity = new Product 
        { 
            Id = 1, 
            Name = "P1",
            Benefits = "Benefits",
            SocialProof = "Proof",
            Metodology = "Method",
            Guarantee = "Guarantee",
            Deliverables = Domain.Enums.Products.ProductDeliverablesEnum.Others,
            CustomerJourney = Domain.Enums.Products.ProductCustomerJourneyEnum.OneTwoMonths
        };
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id)).ReturnsAsync(entity);

        var result = await _controller.GetProduct(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        ok.Value.Should().Be(entity);
    }

    [Fact]
    public async Task GetProduct_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new KeyNotFoundException());

        var result = await _controller.GetProduct(id);

        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetProduct_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GetProduct(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }
}



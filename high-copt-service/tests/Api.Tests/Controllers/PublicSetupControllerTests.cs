using Api.Controllers;
using Application.Contracts.DTOs;
using Domain.Entities;
using Domain.Enums.Public;
using Domain.Interfaces.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Api.Tests.Controllers;

public class PublicSetupControllerTests
{
    private readonly Mock<IPublicService> _mockService;
    private readonly Mock<ILogger<PublicSetupController>> _mockLogger;
    private readonly PublicSetupController _controller;

    public PublicSetupControllerTests()
    {
        _mockService = new Mock<IPublicService>();
        _mockLogger = new Mock<ILogger<PublicSetupController>>();
        _controller = new PublicSetupController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task CreatePublic_WithValidDto_ShouldReturnCreated()
    {
        var createDto = new CreatePublicDto
        {
            Name = "Test Public",
            Local = "São Paulo",
            FamilySituation = "Married",
            Personality = "Extrovert",
            Hobbies = "Reading, Sports",
            Lifestyle = "Active",
            PersonalValue = "Family",
            Roof = "Upper middle class",
            NextLevel = "Financial freedom",
            DropOfWater = "Debt",
            Beliefs = "Hard work pays off",
            SelfVisions = "Successful entrepreneur",
            PossibleObjections = "Too expensive",
            OwnCommunication = "Direct",
            Gender = PublicGenderEnum.Men,
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool
        };

        var createdPublic = new Public
        {
            Id = 1,
            ExternalId = "pub-123",
            Name = createDto.Name,
            Local = createDto.Local,
            FamilySituation = createDto.FamilySituation,
            Personality = createDto.Personality,
            Hobbies = createDto.Hobbies,
            Lifestyle = createDto.Lifestyle,
            PersonalValue = createDto.PersonalValue,
            Roof = createDto.Roof,
            NextLevel = createDto.NextLevel,
            DropOfWater = createDto.DropOfWater,
            Beliefs = createDto.Beliefs,
            SelfVisions = createDto.SelfVisions,
            PossibleObjections = createDto.PossibleObjections,
            OwnCommunication = createDto.OwnCommunication,
            Gender = createDto.Gender,
            EducationLevel = createDto.EducationLevel
        };

        _mockService.Setup(s => s.CreatePublicAsync(It.IsAny<Public>()))
            .ReturnsAsync(createdPublic);

        var result = await _controller.CreatePublic(createDto);

        var createdResult = result.Should().BeOfType<CreatedAtActionResult>().Subject;
        createdResult.ActionName.Should().Be(nameof(_controller.GetPublic));
        createdResult.Value.Should().Be(createdPublic);
    }

    [Fact]
    public async Task CreatePublic_WithServiceError_ShouldReturn500()
    {
        var createDto = new CreatePublicDto 
        { 
            Name = "Test Public",
            Local = "Test local",
            FamilySituation = "Test family",
            Personality = "Test personality",
            Hobbies = "Test hobbies",
            Lifestyle = "Test lifestyle",
            PersonalValue = "Test values",
            Roof = "Test roof",
            NextLevel = "Test next level",
            DropOfWater = "Test drop",
            Beliefs = "Test beliefs",
            SelfVisions = "Test visions",
            PossibleObjections = "Test objections",
            OwnCommunication = "Test communication",
            Gender = PublicGenderEnum.Men,
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool
        };

        _mockService.Setup(s => s.CreatePublicAsync(It.IsAny<Public>()))
            .ThrowsAsync(new Exception("Database error"));

        var result = await _controller.CreatePublic(createDto);

        var statusResult = result.Should().BeOfType<ObjectResult>().Subject;
        statusResult.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnOk_WhenSuccess()
    {
        var id = "pub-1";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ReturnsAsync("TEMPLATE");

        var result = await _controller.GenerateSetupTemplate(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        var payload = ok.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeTrue();
        payload.EntityType.Should().Be("Public");
        payload.Identifier.Should().Be(id);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new KeyNotFoundException("not found"));

        var result = await _controller.GenerateSetupTemplate(id);

        var nf = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var payload = nf.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
        payload.Message.Should().Be("not found");
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GenerateSetupTemplate(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GetPublic_ShouldReturnOk_WhenFound()
    {
        var id = "pub-1";
        var entity = new Public 
        { 
            Id = 1, 
            Name = "Audience",
            Local = "City",
            FamilySituation = "Single",
            Personality = "Calm",
            Hobbies = "Reading",
            Lifestyle = "Active",
            PersonalValue = "Honesty",
            Roof = "Roof",
            NextLevel = "Next",
            DropOfWater = "Drop",
            Beliefs = "Beliefs",
            SelfVisions = "Vision",
            PossibleObjections = "Objections",
            OwnCommunication = "Tone",
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool,
            Gender = PublicGenderEnum.Men
        };
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id)).ReturnsAsync(entity);

        var result = await _controller.GetPublic(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        ok.Value.Should().Be(entity);
    }

    [Fact]
    public async Task GetPublic_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new KeyNotFoundException());

        var result = await _controller.GetPublic(id);

        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetPublic_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GetPublic(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }
}



using Moq;
using Xunit;
using FluentAssertions;
using Domain.Entities;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;
using Application.Services.Setups;

namespace Application.Tests.Services.Setups;

public class ExpertServiceTests
{
    private readonly Mock<IExpertRepository> _mockExpertRepository;
    private readonly Mock<ITemplateService> _mockTemplateService;
    private readonly Mock<IIdGenerationService> _mockIdGenerationService;
    private readonly ExpertService _expertService;

    public ExpertServiceTests()
    {
        _mockExpertRepository = new Mock<IExpertRepository>();
        _mockTemplateService = new Mock<ITemplateService>();
        _mockIdGenerationService = new Mock<IIdGenerationService>();
        _expertService = new ExpertService(_mockExpertRepository.Object, _mockTemplateService.Object, _mockIdGenerationService.Object);
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnExpert()
    {
        var expertId = 1;
        var expectedExpert = new Expert 
        { 
            Id = expertId, 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };

        _mockExpertRepository.Setup(x => x.GetByIdAsync(expertId))
            .ReturnsAsync(expectedExpert);

        var result = await _expertService.GetByIdAsync(expertId);

        result.Should().Be(expectedExpert);
    }

    [Fact]
    public async Task GetByExternalIdAsync_WithValidExternalId_ShouldReturnExpert()
    {
        var externalId = "exp-123";
        var expectedExpert = new Expert 
        { 
            ExternalId = externalId, 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };

        _mockExpertRepository.Setup(x => x.GetByExternalIdAsync(externalId))
            .ReturnsAsync(expectedExpert);

        var result = await _expertService.GetByExternalIdAsync(externalId);

        result.Should().Be(expectedExpert);
    }

    [Fact]
    public async Task GenerateSetupTemplateAsync_WithValidId_ShouldReturnTemplate()
    {
        var expertId = "1";
        var expert = new Expert 
        { 
            Id = 1, 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };
        var expectedTemplate = "Generated template";

        _mockExpertRepository.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(expert);
        _mockTemplateService.Setup(x => x.ProcessExpertTemplateAsync(expert, null))
            .ReturnsAsync(expectedTemplate);

        var result = await _expertService.GenerateSetupTemplateAsync(expertId);

        result.Should().Be(expectedTemplate);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllExperts()
    {
        var experts = new List<Expert>
        {
            new Expert 
            { 
                Id = 1, 
                Name = "Expert 1",
                AreaOfExpertise = "Technology",
                Biography = "Biography 1",
                Moment = "Moment 1",
                Dolor = "Dolor 1",
                Credibility = "Credibility 1",
                Recognition = "Recognition 1",
                TrackRecord = "TrackRecord 1",
                HowProductWorks = "HowProductWorks 1",
                VoicePersonality = "VoicePersonality 1",
                EssentialValues = "EssentialValues 1",
                Enterprise = "Enterprise 1"
            },
            new Expert 
            { 
                Id = 2, 
                Name = "Expert 2",
                AreaOfExpertise = "Marketing",
                Biography = "Biography 2",
                Moment = "Moment 2",
                Dolor = "Dolor 2",
                Credibility = "Credibility 2",
                Recognition = "Recognition 2",
                TrackRecord = "TrackRecord 2",
                HowProductWorks = "HowProductWorks 2",
                VoicePersonality = "VoicePersonality 2",
                EssentialValues = "EssentialValues 2",
                Enterprise = "Enterprise 2"
            }
        };

        _mockExpertRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(experts);

        var result = await _expertService.GetAllAsync();

        result.Should().HaveCount(2);
        result.Should().BeEquivalentTo(experts);
    }
}

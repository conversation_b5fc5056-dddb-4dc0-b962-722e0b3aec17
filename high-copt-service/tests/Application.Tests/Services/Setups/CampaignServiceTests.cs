using Application.Services.Setups;
using Domain.Entities;
using Domain.Enums.Campaing;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;
using FluentAssertions;
using Moq;
using Xunit;

namespace Application.Tests.Services.Setups;

public class CampaignServiceTests
{
    private readonly Mock<ICampaignRepository> _mockCampaignRepository;
    private readonly Mock<ITemplateService> _mockTemplateService;
    private readonly Mock<IExpertRepository> _mockExpertRepository;
    private readonly Mock<IProductRepository> _mockProductRepository;
    private readonly Mock<IPublicRepository> _mockPublicRepository;
    private readonly Mock<IIdGenerationService> _mockIdGenerationService;
    private readonly CampaignService _campaignService;

    public CampaignServiceTests()
    {
        _mockCampaignRepository = new Mock<ICampaignRepository>();
        _mockTemplateService = new Mock<ITemplateService>();
        _mockExpertRepository = new Mock<IExpertRepository>();
        _mockProductRepository = new Mock<IProductRepository>();
        _mockPublicRepository = new Mock<IPublicRepository>();
        _mockIdGenerationService = new Mock<IIdGenerationService>();
        
        _campaignService = new CampaignService(
            _mockCampaignRepository.Object,
            _mockTemplateService.Object,
            _mockExpertRepository.Object,
            _mockProductRepository.Object,
            _mockPublicRepository.Object,
            _mockIdGenerationService.Object);
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnCampaign()
    {
        var campaignId = 1;
        var expectedCampaign = new Campaign 
        { 
            Id = campaignId, 
            Name = "Test Campaign",
            Ideia = "Test Idea",
            Emotion = "Test Emotion",
            Belief = "Test Belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };

        _mockCampaignRepository.Setup(x => x.GetByIdAsync(campaignId))
            .ReturnsAsync(expectedCampaign);

        var result = await _campaignService.GetByIdAsync(campaignId);

        result.Should().Be(expectedCampaign);
    }

    [Fact]
    public async Task GetByExternalIdAsync_WithValidExternalId_ShouldReturnCampaign()
    {
        var externalId = "camp-123";
        var expectedCampaign = new Campaign 
        { 
            ExternalId = externalId, 
            Name = "Test Campaign",
            Ideia = "Test Idea",
            Emotion = "Test Emotion",
            Belief = "Test Belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };

        _mockCampaignRepository.Setup(x => x.GetByExternalIdAsync(externalId))
            .ReturnsAsync(expectedCampaign);

        var result = await _campaignService.GetByExternalIdAsync(externalId);

        result.Should().Be(expectedCampaign);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllCampaigns()
    {
        var campaigns = new List<Campaign>
        {
            new() 
            { 
                Id = 1, 
                Name = "Campaign 1",
                Ideia = "Idea 1",
                Emotion = "Emotion 1",
                Belief = "Belief 1",
                Type = CampaingTypeEnum.Launch,
                ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
                SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
            },
            new() 
            { 
                Id = 2, 
                Name = "Campaign 2",
                Ideia = "Idea 2",
                Emotion = "Emotion 2",
                Belief = "Belief 2",
                Type = CampaingTypeEnum.WebinarOurEvent,
                ConscienceLevel = CampaingConscienceLevelEnum.TotallyUnconscious,
                SophisticationLevel = CampaingSophisticationLevelEnum.AmpliationPromisse
            }
        };

        _mockCampaignRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(campaigns);

        var result = await _campaignService.GetAllAsync();

        result.Should().BeEquivalentTo(campaigns);
    }
}

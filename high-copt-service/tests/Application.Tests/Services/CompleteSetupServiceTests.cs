using Application.Services;
using Domain.Entities;
using Domain.Enums.Campaing;
using Domain.Enums.Products;
using Domain.Enums.Public;
using Domain.Interfaces.Services;
using FluentAssertions;
using Moq;
using Xunit;

namespace Application.Tests.Services;

public class CompleteSetupServiceTests
{
    private readonly Mock<IContentService> _mockContentService;
    private readonly CompleteSetupService _completeSetupService;

    public CompleteSetupServiceTests()
    {
        _mockContentService = new Mock<IContentService>();
        _completeSetupService = new CompleteSetupService(_mockContentService.Object);
    }

    [Fact]
    public async Task GenerateCompleteCopyAsync_WithValidEntities_ShouldReturnGeneratedCopy()
    {
        var campaign = new Campaign 
        { 
            Name = "Test Campaign",
            Ideia = "Test Idea",
            Emotion = "Test Emotion",
            Belief = "Test Belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };
        var expert = new Expert 
        { 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };
        var product = new Product 
        { 
            Name = "Test Product",
            Benefits = "Product Benefits",
            SocialProof = "Social Proof",
            Metodology = "Methodology",
            Guarantee = "Guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };
        var publicTarget = new Public 
        { 
            Name = "Test Public",
            Local = "Test Location",
            FamilySituation = "Test Family",
            Personality = "Test Personality",
            Hobbies = "Test Hobbies",
            Lifestyle = "Test Lifestyle",
            PersonalValue = "Test Values",
            Roof = "Test Roof",
            NextLevel = "Test Next Level",
            DropOfWater = "Test Drop",
            Beliefs = "Test Beliefs",
            SelfVisions = "Test Visions",
            PossibleObjections = "Test Objections",
            OwnCommunication = "Test Communication",
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool,
            Gender = PublicGenderEnum.Men
        };
        var copyRequest = "Generate sales copy";
        var provider = "chatgpt";
        var expectedCopy = "Generated complete copy";

        _mockContentService.Setup(x => x.GenerateContentAsync(
            It.IsAny<string>(), 
            provider))
            .ReturnsAsync(expectedCopy);

        var result = await _completeSetupService.GenerateCompleteCopyAsync(
            campaign, expert, product, publicTarget, copyRequest, provider);

        result.Should().Be(expectedCopy);
        _mockContentService.Verify(x => x.GenerateContentAsync(
            It.IsAny<string>(), 
            provider), Times.Once);
    }

    [Fact]
    public async Task GenerateCompleteCopyAsync_WithNullCampaign_ShouldThrowArgumentNullException()
    {
        var expert = new Expert 
        { 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };
        var product = new Product 
        { 
            Name = "Test Product",
            Benefits = "Product Benefits",
            SocialProof = "Social Proof",
            Metodology = "Methodology",
            Guarantee = "Guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };
        var publicTarget = new Public 
        { 
            Name = "Test Public",
            Local = "Test Location",
            FamilySituation = "Test Family",
            Personality = "Test Personality",
            Hobbies = "Test Hobbies",
            Lifestyle = "Test Lifestyle",
            PersonalValue = "Test Values",
            Roof = "Test Roof",
            NextLevel = "Test Next Level",
            DropOfWater = "Test Drop",
            Beliefs = "Test Beliefs",
            SelfVisions = "Test Visions",
            PossibleObjections = "Test Objections",
            OwnCommunication = "Test Communication",
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool,
            Gender = PublicGenderEnum.Men
        };
        var copyRequest = "Generate sales copy";

        var action = async () => await _completeSetupService.GenerateCompleteCopyAsync(
            null!, expert, product, publicTarget, copyRequest);

        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task GenerateCompleteCopyAsync_WithEmptyCopyRequest_ShouldThrowArgumentException()
    {
        var campaign = new Campaign 
        { 
            Name = "Test Campaign",
            Ideia = "Test Idea",
            Emotion = "Test Emotion",
            Belief = "Test Belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };
        var expert = new Expert 
        { 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };
        var product = new Product 
        { 
            Name = "Test Product",
            Benefits = "Product Benefits",
            SocialProof = "Social Proof",
            Metodology = "Methodology",
            Guarantee = "Guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };
        var publicTarget = new Public 
        { 
            Name = "Test Public",
            Local = "Test Location",
            FamilySituation = "Test Family",
            Personality = "Test Personality",
            Hobbies = "Test Hobbies",
            Lifestyle = "Test Lifestyle",
            PersonalValue = "Test Values",
            Roof = "Test Roof",
            NextLevel = "Test Next Level",
            DropOfWater = "Test Drop",
            Beliefs = "Test Beliefs",
            SelfVisions = "Test Visions",
            PossibleObjections = "Test Objections",
            OwnCommunication = "Test Communication",
            EducationLevel = PublicEducationLevelEnum.CompleteHighSchool,
            Gender = PublicGenderEnum.Men
        };

        var action = async () => await _completeSetupService.GenerateCompleteCopyAsync(
            campaign, expert, product, publicTarget, "");

        await action.Should().ThrowAsync<ArgumentException>();
    }
}

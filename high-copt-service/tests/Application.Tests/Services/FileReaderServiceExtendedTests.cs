using Application.Services;
using Domain.Interfaces;
using System.Text;
using Xunit;
using FluentAssertions;
using System.Text.Json;

namespace Application.Tests.Services;

public class FileReaderServiceExtendedTests
{
    private readonly IFileReaderService _fileReaderService;

    public FileReaderServiceExtendedTests()
    {
        _fileReaderService = new FileReaderService();
    }

    [Fact]
    public async Task ReadFileAsync_WithJsonFile_ShouldReturnFormattedContent()
    {
        var jsonData = new { name = "Test", value = 123, active = true };
        var jsonContent = JsonSerializer.Serialize(jsonData);
        var tempFile = Path.ChangeExtension(Path.GetTempFileName(), ".json");
        
        await File.WriteAllTextAsync(tempFile, jsonContent);

        try
        {
            var result = await _fileReaderService.ReadFileAsync(tempFile);
            result.Should().Contain("Test");
            result.Should().Contain("123");
        }
        finally
        {
            File.Delete(tempFile);
        }
    }

    [Fact]
    public async Task ReadFileAsync_WithCsvFile_ShouldReturnStructuredData()
    {
        var csvContent = "Nome,Idade,Cidade\nJoão,25,São Paulo\nMaria,30,Rio de Janeiro";
        var tempFile = Path.ChangeExtension(Path.GetTempFileName(), ".csv");
        
        await File.WriteAllTextAsync(tempFile, csvContent);

        try
        {
            var result = await _fileReaderService.ReadFileAsync(tempFile);
            result.Should().Contain("Nome, Idade, Cidade");
            result.Should().Contain("João, 25, São Paulo");
            result.Should().Contain("Maria, 30, Rio de Janeiro");
        }
        finally
        {
            File.Delete(tempFile);
        }
    }

    [Fact]
    public async Task ReadFileAsync_WithLargeFile_ShouldHandleEfficiently()
    {
        var largeContent = new StringBuilder();
        for (int i = 0; i < 10000; i++)
        {
            largeContent.AppendLine($"Line {i}: This is test data for performance testing");
        }
        
        var tempFile = Path.ChangeExtension(Path.GetTempFileName(), ".txt");
        await File.WriteAllTextAsync(tempFile, largeContent.ToString());

        try
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await _fileReaderService.ReadFileAsync(tempFile);
            stopwatch.Stop();
            
            result.Should().NotBeNullOrEmpty();
            result.Should().Contain("Line 0:");
            result.Should().Contain("Line 9999:");
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000);
        }
        finally
        {
            File.Delete(tempFile);
        }
    }

    [Fact]
    public async Task ReadFileAsync_WithSpecialCharacters_ShouldPreserveEncoding()
    {
        var specialContent = "Teste com acentos: ção, ã, é, ü, ñ, 中文, 🚀";
        var tempFile = Path.ChangeExtension(Path.GetTempFileName(), ".txt");
        
        await File.WriteAllTextAsync(tempFile, specialContent, Encoding.UTF8);

        try
        {
            var result = await _fileReaderService.ReadFileAsync(tempFile);
            result.Should().Be(specialContent);
        }
        finally
        {
            File.Delete(tempFile);
        }
    }

    [Fact]
    public async Task ReadFileAsync_WithEmptyFile_ShouldReturnEmptyString()
    {
        var tempFile = Path.ChangeExtension(Path.GetTempFileName(), ".txt");
        await File.WriteAllTextAsync(tempFile, "");

        try
        {
            var result = await _fileReaderService.ReadFileAsync(tempFile);
            result.Should().BeEmpty();
        }
        finally
        {
            File.Delete(tempFile);
        }
    }

    [Theory]
    [InlineData(".txt")]
    [InlineData(".csv")]
    [InlineData(".json")]
    [InlineData(".xml")]
    [InlineData(".html")]
    [InlineData(".md")]
    [InlineData(".log")]
    public void IsFileTypeSupported_WithTextBasedFiles_ShouldReturnTrue(string extension)
    {
        var fileName = $"test{extension}";
        _fileReaderService.IsFileTypeSupported(fileName).Should().BeTrue();
    }

    [Theory]
    [InlineData(".pdf")]
    [InlineData(".docx")]
    [InlineData(".xlsx")]
    [InlineData(".xls")]
    public void IsFileTypeSupported_WithDocumentFiles_ShouldReturnTrue(string extension)
    {
        var fileName = $"document{extension}";
        _fileReaderService.IsFileTypeSupported(fileName).Should().BeTrue();
    }

    [Theory]
    [InlineData(".exe")]
    [InlineData(".dll")]
    [InlineData(".bin")]
    [InlineData(".jpg")]
    [InlineData(".png")]
    [InlineData(".mp4")]
    [InlineData(".zip")]
    public void IsFileTypeSupported_WithUnsupportedFiles_ShouldReturnFalse(string extension)
    {
        var fileName = $"file{extension}";
        _fileReaderService.IsFileTypeSupported(fileName).Should().BeFalse();
    }

    [Fact]
    public void GetSupportedExtensions_ShouldReturnCompleteList()
    {
        var extensions = _fileReaderService.GetSupportedExtensions().ToList();
        
        extensions.Should().Contain(".txt");
        extensions.Should().Contain(".csv");
        extensions.Should().Contain(".pdf");
        extensions.Should().Contain(".docx");
        extensions.Should().Contain(".xlsx");
        extensions.Should().Contain(".json");
        extensions.Should().Contain(".xml");
        extensions.Should().Contain(".html");
        extensions.Should().Contain(".md");
        extensions.Should().Contain(".log");
        
        extensions.Should().HaveCountGreaterThan(10);
    }

    [Fact]
    public async Task ReadFileAsync_WithStream_MultipleCalls_ShouldWorkCorrectly()
    {
        var content = "Stream test content";
        var bytes = Encoding.UTF8.GetBytes(content);
        
        using var stream1 = new MemoryStream(bytes);
        using var stream2 = new MemoryStream(bytes);
        
        var result1 = await _fileReaderService.ReadFileAsync(stream1, "test1.txt");
        var result2 = await _fileReaderService.ReadFileAsync(stream2, "test2.txt");
        
        result1.Should().Be(content);
        result2.Should().Be(content);
    }
}

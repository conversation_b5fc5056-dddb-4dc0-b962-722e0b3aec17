using Application.Services;
using Application.Services.AI;
using Domain.Interfaces;
using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using System.Text;
using Domain.Entities;
using Domain.Enums.Campaing;
using Domain.Enums.Products;

namespace Application.Tests.Services;

public class ContentServiceTests
{
    private readonly Mock<IAiOrchestrator> _mockAiOrchestrator;
    private readonly Mock<IFileReaderService> _mockFileReaderService;
    private readonly Mock<IPromptGenerationService> _mockPromptGenerationService;
    private readonly Mock<ILogger<ContentService>> _mockLogger;
    private readonly ContentService _contentService;

    public ContentServiceTests()
    {
        _mockAiOrchestrator = new Mock<IAiOrchestrator>();
        _mockFileReaderService = new Mock<IFileReaderService>();
        _mockPromptGenerationService = new Mock<IPromptGenerationService>();
        _mockLogger = new Mock<ILogger<ContentService>>();
        
        _contentService = new ContentService(
            _mockAiOrchestrator.Object,
            _mockFileReaderService.Object,
            _mockPromptGenerationService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateContentAsync_WithValidPrompt_ShouldReturnContent()
    {
        var prompt = "Test prompt";
        var provider = "chatgpt";
        var expectedResponse = "Generated content";

        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(prompt, provider))
            .ReturnsAsync(expectedResponse);

        var result = await _contentService.GenerateContentAsync(prompt, provider);

        result.Should().Be(expectedResponse);
        _mockAiOrchestrator.Verify(x => x.GenerateContentAsync(prompt, provider), Times.Once);
    }

    [Fact]
    public async Task AnalyzeFileAsync_WithValidFile_ShouldReturnAnalysis()
    {
        var fileName = "test.txt";
        var fileContent = "Test file content";
        var expectedAnalysis = "File analysis result";
        var mockFile = CreateMockFile(fileName, fileContent);

        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(true);
        _mockFileReaderService.Setup(x => x.ReadFileAsync(It.IsAny<Stream>(), fileName))
            .ReturnsAsync(fileContent);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(It.IsAny<string>(), null))
            .ReturnsAsync(expectedAnalysis);

        var result = await _contentService.AnalyzeFileAsync(mockFile);

        result.Should().Be(expectedAnalysis);
    }

    [Fact]
    public async Task AnalyzeFileAsync_WithUnsupportedFileType_ShouldThrowNotSupportedException()
    {
        var fileName = "test.exe";
        var mockFile = CreateMockFile(fileName, "content");
        var supportedExtensions = new[] { ".txt", ".pdf", ".csv" };

        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(false);
        _mockFileReaderService.Setup(x => x.GetSupportedExtensions())
            .Returns(supportedExtensions);

        var action = async () => await _contentService.AnalyzeFileAsync(mockFile);

        await action.Should().ThrowAsync<NotSupportedException>()
            .WithMessage("File type not supported*");
    }

    [Fact]
    public async Task AnalyzeFileAsync_WithEmptyFile_ShouldThrowInvalidOperationException()
    {
        var fileName = "empty.txt";
        var mockFile = CreateMockFile(fileName, "");

        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(true);
        _mockFileReaderService.Setup(x => x.ReadFileAsync(It.IsAny<Stream>(), fileName))
            .ReturnsAsync("");

        var action = async () => await _contentService.AnalyzeFileAsync(mockFile);

        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Could not extract content from the file or file is empty.");
    }

    [Fact]
    public async Task AnalyzeSetupAsync_WithValidParameters_ShouldReturnAnalysis()
    {
        var templateType = "campaign";
        var entity = new Campaign 
        { 
            Name = "Test Campaign",
            Ideia = "Test Idea",
            Emotion = "Test Emotion",
            Belief = "Test Belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };
        var context = "Test context";
        var analysisPrompt = "Generated analysis prompt";
        var expectedResult = "Setup analysis result";

        _mockPromptGenerationService.Setup(x => x.GenerateAnalysisPromptAsync(templateType, entity, context))
            .ReturnsAsync(analysisPrompt);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(analysisPrompt, null))
            .ReturnsAsync(expectedResult);

        var result = await _contentService.AnalyzeSetupAsync(templateType, entity, context);

        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task GenerateImprovementSuggestionsAsync_WithValidParameters_ShouldReturnSuggestions()
    {
        var templateType = "expert";
        var entity = new Expert 
        { 
            Name = "Test Expert",
            AreaOfExpertise = "Technology",
            Biography = "Test Biography",
            Moment = "Current Moment",
            Dolor = "Pain Point",
            Credibility = "High Credibility",
            Recognition = "Industry Recognition",
            TrackRecord = "Proven Track Record",
            HowProductWorks = "Product Explanation",
            VoicePersonality = "Professional Voice",
            EssentialValues = "Core Values",
            Enterprise = "Test Enterprise"
        };
        var improvementPrompt = "Generated improvement prompt";
        var expectedSuggestions = "Improvement suggestions";

        _mockPromptGenerationService.Setup(x => x.GenerateImprovementPromptAsync(templateType, entity))
            .ReturnsAsync(improvementPrompt);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(improvementPrompt, null))
            .ReturnsAsync(expectedSuggestions);

        var result = await _contentService.GenerateImprovementSuggestionsAsync(templateType, entity);

        result.Should().Be(expectedSuggestions);
    }

    [Fact]
    public async Task ValidateSetupCompletenessAsync_WithValidParameters_ShouldReturnValidation()
    {
        var templateType = "product";
        var entity = new Product 
        { 
            Name = "Test Product",
            Benefits = "Product Benefits",
            SocialProof = "Social Proof",
            Metodology = "Methodology",
            Guarantee = "Guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };
        var validationPrompt = "Generated validation prompt";
        var expectedValidation = "Validation result";

        _mockPromptGenerationService.Setup(x => x.GenerateValidationPromptAsync(templateType, entity))
            .ReturnsAsync(validationPrompt);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(validationPrompt, null))
            .ReturnsAsync(expectedValidation);

        var result = await _contentService.ValidateSetupCompletenessAsync(templateType, entity);

        result.Should().Be(expectedValidation);
    }

    [Fact]
    public async Task GenerateContentFromTemplateAsync_WithCampaign_ShouldReturnContent()
    {
        var templateType = "campaign";
        var campaign = new Campaign 
        { 
            Name = "Test Campaign",
            Ideia = "Test Idea",
            Emotion = "Test Emotion",
            Belief = "Test Belief",
            Type = CampaingTypeEnum.Launch,
            ConscienceLevel = CampaingConscienceLevelEnum.TotallyUnconscious,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent
        };
        var contentRequest = "Generate email campaign";
        var analysisPrompt = "Generated analysis prompt";
        var expectedContent = "Generated content from template";

        _mockPromptGenerationService.Setup(x => x.GenerateAnalysisPromptAsync(templateType, campaign, null))
            .ReturnsAsync(analysisPrompt);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(It.IsAny<string>(), null))
            .ReturnsAsync(expectedContent);

        var result = await _contentService.GenerateContentFromTemplateAsync(templateType, campaign, contentRequest);

        result.Should().Be(expectedContent);
    }

    private static IFormFile CreateMockFile(string fileName, string content)
    {
        var bytes = Encoding.UTF8.GetBytes(content);
        var stream = new MemoryStream(bytes);
        
        var mockFile = new Mock<IFormFile>();
        mockFile.Setup(f => f.FileName).Returns(fileName);
        mockFile.Setup(f => f.Length).Returns(bytes.Length);
        mockFile.Setup(f => f.OpenReadStream()).Returns(stream);
        
        return mockFile.Object;
    }
}

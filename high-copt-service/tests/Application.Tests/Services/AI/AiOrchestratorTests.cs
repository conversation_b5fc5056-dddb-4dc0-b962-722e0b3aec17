using Application.Services.AI;
using Domain.Interfaces.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace Application.Tests.Services.AI;

public class AiOrchestratorTests
{
    private readonly Mock<IAiServiceFactory> _mockFactory;
    private readonly Mock<ILogger<AiOrchestrator>> _mockLogger;
    private readonly Mock<IGenerativeAiService> _mockChatGptService;
    private readonly Mock<IGenerativeAiService> _mockGeminiService;
    private readonly AiOrchestrator _orchestrator;

    public AiOrchestratorTests()
    {
        _mockFactory = new Mock<IAiServiceFactory>();
        _mockLogger = new Mock<ILogger<AiOrchestrator>>();
        _mockChatGptService = new Mock<IGenerativeAiService>();
        _mockGeminiService = new Mock<IGenerativeAiService>();
        
        _orchestrator = new AiOrchestrator(_mockFactory.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateContentAsync_WithChatGptProvider_ShouldReturnContent()
    {
        var prompt = "Test prompt";
        var expectedResponse = "AI generated response";
        
        _mockFactory.Setup(x => x.CreateService("chatgpt"))
            .Returns(_mockChatGptService.Object);
        _mockChatGptService.Setup(x => x.GenerateAsync(prompt))
            .ReturnsAsync(expectedResponse);

        var result = await _orchestrator.GenerateContentAsync(prompt, "chatgpt");

        result.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task GenerateContentAsync_WithGeminiProvider_ShouldReturnContent()
    {
        var prompt = "Test prompt";
        var expectedResponse = "Gemini generated response";
        
        _mockFactory.Setup(x => x.CreateService("gemini"))
            .Returns(_mockGeminiService.Object);
        _mockGeminiService.Setup(x => x.GenerateAsync(prompt))
            .ReturnsAsync(expectedResponse);

        var result = await _orchestrator.GenerateContentAsync(prompt, "gemini");

        result.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task GenerateContentAsync_WithNullProvider_ShouldUseChatGptAsDefault()
    {
        var prompt = "Test prompt";
        var expectedResponse = "Default ChatGPT response";
        
        _mockFactory.Setup(x => x.CreateService("chatgpt"))
            .Returns(_mockChatGptService.Object);
        _mockChatGptService.Setup(x => x.GenerateAsync(prompt))
            .ReturnsAsync(expectedResponse);

        var result = await _orchestrator.GenerateContentAsync(prompt, null);

        result.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task GenerateContentAsync_WithChatGptFailure_ShouldFallbackToGemini()
    {
        var prompt = "Test prompt";
        var expectedResponse = "Gemini fallback response";
        
        _mockFactory.Setup(x => x.CreateService("chatgpt"))
            .Returns(_mockChatGptService.Object);
        _mockFactory.Setup(x => x.CreateService("gemini"))
            .Returns(_mockGeminiService.Object);
        
        _mockChatGptService.Setup(x => x.GenerateAsync(prompt))
            .ThrowsAsync(new InvalidOperationException("ChatGPT unavailable"));
        _mockGeminiService.Setup(x => x.GenerateAsync(prompt))
            .ReturnsAsync(expectedResponse);

        var result = await _orchestrator.GenerateContentAsync(prompt, "chatgpt");

        result.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task GenerateContentAsync_WithAllServicesFailure_ShouldThrowException()
    {
        var prompt = "Test prompt";
        
        _mockFactory.Setup(x => x.CreateService("chatgpt"))
            .Returns(_mockChatGptService.Object);
        _mockFactory.Setup(x => x.CreateService("gemini"))
            .Returns(_mockGeminiService.Object);
        
        _mockChatGptService.Setup(x => x.GenerateAsync(prompt))
            .ThrowsAsync(new InvalidOperationException("ChatGPT unavailable"));
        _mockGeminiService.Setup(x => x.GenerateAsync(prompt))
            .ThrowsAsync(new InvalidOperationException("Gemini unavailable"));

        var act = async () => await _orchestrator.GenerateContentAsync(prompt, "chatgpt");

        await act.Should().ThrowAsync<InvalidOperationException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public async Task GenerateContentAsync_WithInvalidPrompt_ShouldThrowArgumentException(string prompt)
    {
        var act = async () => await _orchestrator.GenerateContentAsync(prompt, "chatgpt");

        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task GenerateContentAsync_WithNullPrompt_ShouldThrowArgumentException()
    {
        var act = async () => await _orchestrator.GenerateContentAsync(null!, "chatgpt");

        await act.Should().ThrowAsync<ArgumentException>();
    }
}

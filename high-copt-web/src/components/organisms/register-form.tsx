import { AuthFormLayout } from '../layouts';
import {
  BodyRegisterForm,
  type registerSchemaProps,
} from '../molecules/body-register-form';
import { FooterLoginAuth } from '../molecules/footer-login-form';
import { HeaderFormAuth } from '../molecules/header-login-form';
import { useAuth } from '@/contexts/auth-context';
import { useNavigate } from 'react-router-dom';

const RegisterForm = () => {
  const { register } = useAuth();
  const navigate = useNavigate();

  const onSubmit = async (data: registerSchemaProps) => {
    try {
      await register(data.name, data.email, data.password);
      navigate('/app/dashboard');
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <AuthFormLayout
      imgUrl="https://img.freepik.com/free-photo/business-people-meeting_53876-15178.jpg?semt=ais_hybrid&w=740&q=80"
      imagePosition="left"
    >
      <HeaderFormAuth />
      <BodyRegisterForm onSubmit={onSubmit} />
      <FooterLoginAuth />
    </AuthFormLayout>
  );
};

export default RegisterForm;

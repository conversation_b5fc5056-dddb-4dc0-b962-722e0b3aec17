import type { ReactNode } from 'react';
import { Button } from '../atoms';

interface ButtonWithLogoProps extends React.ComponentProps<'button'> {
  icon: ReactNode;
  text: string;
}

export const ButtonWithIcon = ({
  icon,
  text,
  ...rest
}: ButtonWithLogoProps) => (
  <Button
    variant="outline"
    className="border border-gray-200 shadow-sm mx-auto w-full sm:w-auto flex-1 sm:flex-none"
    {...rest}
  >
    <div className="flex items-center justify-center h-10 px-2">
      <span className="sr-only">{text}</span>
      {icon}
      <span className="ml-2.5 font-medium text-xs sm:text-sm truncate">{text}</span>
    </div>
  </Button>
);

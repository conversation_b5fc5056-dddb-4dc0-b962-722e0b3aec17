import { apiClient } from './api';
import type { ApiResponse } from '@/types';

export interface GenerateCompleteCopyRequest {
  campaignId: string;
  expertId: string;
  productId: string;
  publicId: string;
  copyRequest: string;
  provider?: 'chatgpt' | 'gemini';
}

export interface CompleteCopyResponse extends ApiResponse {
  generatedCopy: string;
  copyRequest: string;
  provider: string;
}

export class CopyService {
  private static readonly BASE_ENDPOINT = '/copy';

  static async generateCompleteCopy(
    data: GenerateCompleteCopyRequest
  ): Promise<CompleteCopyResponse> {
    const response = await apiClient.post<CompleteCopyResponse>(
      `${this.BASE_ENDPOINT}/generate-by-id`,
      data
    );
    return response.data;
  }
}

import { HeaderFormAuth } from '@/components/molecules/header-login-form';
import { Link } from 'react-router-dom';
import { FaRegSadCry } from 'react-icons/fa';
import { useEffect } from 'react';

const NotFoundPage = () => {

  useEffect(() => {
    document.title = 'High Capital | 404';
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-200 text-center p-4 sm:p-6">
      <HeaderFormAuth />
      <h1 className="text-4xl sm:text-6xl mt-8 font-bold text-[var(--hc-scooter)]">404</h1>
      <p className="mt-2 flex flex-col sm:flex-row items-center gap-2 sm:gap-4 text-base sm:text-lg text-gray-600">
        Oops! Page not found <FaRegSadCry className="mt-1" /> .
      </p>

      <Link
        to="/"
        className="mt-6 px-4 sm:px-6 py-3 sm:py-4 rounded-2xl text-white font-medium shadow-md hover:bg-[#30636f] text-sm sm:text-base"
        style={{ backgroundColor: 'var(--hc-scooter)' }}
      >
        Return to Home
      </Link>
    </div>
  );
};

export default NotFoundPage;

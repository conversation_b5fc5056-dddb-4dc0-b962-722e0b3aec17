name: Build and Deploy to GCS

on:
  push:
    branches: [ main ]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  BUCKET_NAME: ${{ secrets.GCS_BUCKET_NAME }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    # Configurar permissões para Workload Identity
    permissions:
      contents: read
      id-token: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Clean Cache
      run: npm cache clean --force

    - name: Delete Node Modules and Cache Files
      run: rm -rf node_modules package-lock.json pnpm-lock.yaml yarn.lock

    - name: Install Rollup Linux x64
      run: npm install @rollup/rollup-linux-x64-gnu

    - name: Install dependencies
      run: npm install

    - name: Run linter
      run: npm run lint

    - name: Build project
      run: npm run build
      env:
        CI: false

    - name: Authenticate to Google Cloud
      id: auth
      uses: google-github-actions/auth@v2
      with:
        workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
        service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Deploy to Google Cloud Storage
      run: |
        # Remove arquivos antigos do bucket (opcional)
        gsutil -m rm -r gs://${{ env.BUCKET_NAME }}/* || true

        # Upload dos arquivos buildados
        gsutil -m cp -r dist/* gs://${{ env.BUCKET_NAME }}/

        # Configurar cache headers para assets estáticos
                gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.BUCKET_NAME }}/${PR_FOLDER}/**/*.js
        gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.BUCKET_NAME }}/${PR_FOLDER}/**/*.css
        gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.BUCKET_NAME }}/${PR_FOLDER}/**/*.png

        # Configurar cache headers para HTML (sem cache)
        gsutil -m setmeta -h "Cache-Control:no-cache, no-store, must-revalidate" gs://${{ env.BUCKET_NAME }}/**/*.html

        # Configurar index.html como página principal
        gsutil web set -m index.html -e 404.html gs://${{ env.BUCKET_NAME }}

    - name: Make bucket publicly readable
      run: |
        gsutil iam ch allUsers:objectViewer gs://${{ env.BUCKET_NAME }}

    - name: Get website URL
      run: |
        echo "🚀 Website deployed successfully!"
        echo "📍 URL: https://storage.googleapis.com/${{ env.BUCKET_NAME }}/index.html"
        echo "🌐 Custom domain URL (if configured): https://${{ env.BUCKET_NAME }}"
        echo "✅ Deploy completed using Workload Identity (no keys stored!)"

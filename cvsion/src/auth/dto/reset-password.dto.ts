import { ApiProperty } from '@nestjs/swagger';
import { IsString, MinLength } from 'class-validator';

export class ResetPasswordDto {
  @ApiProperty({
    description: 'Token de recuperação de senha',
    example: 'abc123def456',
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'Nova senha (mínimo 8 caracteres)',
    example: 'NovaSenha@123',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  password: string;
}

import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional } from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Senha do usuário',
    example: 'MinhaSenh@123',
  })
  @IsString()
  password: string;

  @ApiProperty({
    description: 'Código 2FA (se habilitado)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  twoFactorCode?: string;
}

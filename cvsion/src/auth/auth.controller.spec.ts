import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './services/auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    register: jest.fn(),
    login: jest.fn(),
    forgotPassword: jest.fn(),
    resetPassword: jest.fn(),
    verifyEmail: jest.fn(),
    refreshToken: jest.fn(),
    enable2FA: jest.fn(),
    confirm2FA: jest.fn(),
    disable2FA: jest.fn(),
    handleOAuthLogin: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should register a new user', async () => {
      const registerDto: RegisterDto = {
        fullname: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        birth_date: '1990-01-01',
      };

      const expectedResult = {
        user: { id: '1', email: '<EMAIL>' },
        tokens: { access_token: 'token', refresh_token: 'refresh_token' },
      };

      mockAuthService.register.mockResolvedValue(expectedResult);

      const result = await controller.register(registerDto);

      expect(authService.register).toHaveBeenCalledWith(registerDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('login', () => {
    it('should login user', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const expectedResult = {
        user: { id: '1', email: '<EMAIL>' },
        tokens: { access_token: 'token', refresh_token: 'refresh_token' },
      };

      mockAuthService.login.mockResolvedValue(expectedResult);

      const result = await controller.login(loginDto);

      expect(authService.login).toHaveBeenCalledWith(loginDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('forgotPassword', () => {
    it('should send password reset email', async () => {
      const forgotPasswordDto = { email: '<EMAIL>' };
      const expectedResult = { message: 'Se o email existir, um link de recuperação será enviado' };

      mockAuthService.forgotPassword.mockResolvedValue(expectedResult);

      const result = await controller.forgotPassword(forgotPasswordDto);

      expect(authService.forgotPassword).toHaveBeenCalledWith(forgotPasswordDto.email);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email with token', async () => {
      const token = 'verification-token';
      const expectedResult = { message: 'Email verificado com sucesso' };

      mockAuthService.verifyEmail.mockResolvedValue(expectedResult);

      const result = await controller.verifyEmail(token);

      expect(authService.verifyEmail).toHaveBeenCalledWith(token);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getProfile', () => {
    it('should return user profile without sensitive data', async () => {
      const mockUser = {
        id: '1',
        fullname: 'Test User',
        email: '<EMAIL>',
        password_hash: 'hashedPassword',
        two_factor_secret: 'secret',
        created_at: new Date(),
      };

      const req = { user: mockUser } as any;
      const result = await controller.getProfile(req);

      expect(result).not.toHaveProperty('password_hash');
      expect(result).not.toHaveProperty('two_factor_secret');
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('email');
    });
  });
});

import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, AuthProvider } from '../entities/user.entity';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super({
      clientID: configService.get('oauth.google.clientId'),
      clientSecret: configService.get('oauth.google.clientSecret'),
      callbackURL: configService.get('oauth.google.callbackURL'),
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { id, name, emails, photos } = profile;
    const email = emails[0].value;

    try {
      // Verificar se o usuário já existe
      let user = await this.userRepository.findOne({
        where: [
          { google_id: id },
          { email: email },
        ],
      });

      if (user) {
        // Atualizar informações do Google se necessário
        if (!user.google_id) {
          user.google_id = id;
          user.auth_provider = AuthProvider.GOOGLE;
        }
        if (!user.avatar_url && photos && photos.length > 0) {
          user.avatar_url = photos[0].value;
        }
        user.last_login = new Date();
        user = await this.userRepository.save(user);
      } else {
        // Criar novo usuário
        user = this.userRepository.create({
          google_id: id,
          email: email,
          fullname: `${name.givenName} ${name.familyName}`,
          avatar_url: photos && photos.length > 0 ? photos[0].value : null,
          auth_provider: AuthProvider.GOOGLE,
          email_verified: true, // Google emails são considerados verificados
          is_active: true,
          last_login: new Date(),
        });
        user = await this.userRepository.save(user);
      }

      done(null, user);
    } catch (error) {
      done(error, null);
    }
  }
}

import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-apple';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, AuthProvider } from '../entities/user.entity';

@Injectable()
export class AppleStrategy extends PassportStrategy(Strategy, 'apple') {
  constructor(
    private configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super({
      clientID: configService.get('oauth.apple.clientId'),
      teamID: configService.get('oauth.apple.teamId'),
      keyID: configService.get('oauth.apple.keyId'),
      privateKeyString: configService.get('oauth.apple.privateKey'),
      callbackURL: configService.get('oauth.apple.callbackURL'),
      scope: ['name', 'email'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    idToken: any,
    profile: any,
    done: any,
  ): Promise<any> {
    const { id, email, name } = profile;

    try {
      // Verificar se o usuário já existe
      let user = await this.userRepository.findOne({
        where: [
          { apple_id: id },
          { email: email },
        ],
      });

      if (user) {
        // Atualizar informações da Apple se necessário
        if (!user.apple_id) {
          user.apple_id = id;
          user.auth_provider = AuthProvider.APPLE;
        }
        user.last_login = new Date();
        user = await this.userRepository.save(user);
      } else {
        // Criar novo usuário
        const fullname = name ? `${name.firstName || ''} ${name.lastName || ''}`.trim() : 'Usuário Apple';
        
        user = this.userRepository.create({
          apple_id: id,
          email: email,
          fullname: fullname || 'Usuário Apple',
          auth_provider: AuthProvider.APPLE,
          email_verified: true, // Apple emails são considerados verificados
          is_active: true,
          last_login: new Date(),
        });
        user = await this.userRepository.save(user);
      }

      done(null, user);
    } catch (error) {
      done(error, null);
    }
  }
}

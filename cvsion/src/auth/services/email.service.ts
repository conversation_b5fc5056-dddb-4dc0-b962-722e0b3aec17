import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransporter({
      host: this.configService.get('email.host'),
      port: this.configService.get('email.port'),
      secure: this.configService.get('email.secure'),
      auth: {
        user: this.configService.get('email.auth.user'),
        pass: this.configService.get('email.auth.pass'),
      },
    });
  }

  async sendVerificationEmail(email: string, token: string): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/auth/verify-email?token=${token}`;
    
    const mailOptions = {
      from: this.configService.get('email.from'),
      to: email,
      subject: 'Verificação de Email - Finance App',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Verificação de Email</h2>
          <p>Obrigado por se registrar no Finance App!</p>
          <p>Para completar seu registro, clique no link abaixo para verificar seu email:</p>
          <a href="${verificationUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Verificar Email
          </a>
          <p>Este link expira em 24 horas.</p>
          <p>Se você não se registrou no Finance App, ignore este email.</p>
        </div>
      `,
    };

    await this.transporter.sendMail(mailOptions);
  }

  async sendPasswordResetEmail(email: string, token: string): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/auth/reset-password?token=${token}`;
    
    const mailOptions = {
      from: this.configService.get('email.from'),
      to: email,
      subject: 'Recuperação de Senha - Finance App',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Recuperação de Senha</h2>
          <p>Você solicitou a recuperação de sua senha no Finance App.</p>
          <p>Clique no link abaixo para redefinir sua senha:</p>
          <a href="${resetUrl}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Redefinir Senha
          </a>
          <p>Este link expira em 1 hora.</p>
          <p>Se você não solicitou a recuperação de senha, ignore este email.</p>
        </div>
      `,
    };

    await this.transporter.sendMail(mailOptions);
  }

  async sendTwoFactorBackupCodes(email: string, backupCodes: string[]): Promise<void> {
    const mailOptions = {
      from: this.configService.get('email.from'),
      to: email,
      subject: 'Códigos de Backup 2FA - Finance App',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Códigos de Backup para Autenticação de Dois Fatores</h2>
          <p>Você habilitou a autenticação de dois fatores em sua conta.</p>
          <p>Guarde estes códigos de backup em um local seguro. Você pode usar qualquer um deles para acessar sua conta se perder acesso ao seu dispositivo de autenticação:</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            ${backupCodes.map(code => `<div style="font-family: monospace; font-size: 16px; margin: 5px 0;">${code}</div>`).join('')}
          </div>
          <p><strong>Importante:</strong></p>
          <ul>
            <li>Cada código pode ser usado apenas uma vez</li>
            <li>Mantenha estes códigos em local seguro</li>
            <li>Não compartilhe estes códigos com ninguém</li>
          </ul>
        </div>
      `,
    };

    await this.transporter.sendMail(mailOptions);
  }
}

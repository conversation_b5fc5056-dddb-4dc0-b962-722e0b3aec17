import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import * as bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";
import { User, AuthProvider } from "../entities/user.entity";
import { RegisterDto } from "../dto/register.dto";
import { LoginDto } from "../dto/login.dto";
import { EmailService } from "./email.service";
import { TwoFactorService } from "./two-factor.service";

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private emailService: EmailService,
    private twoFactorService: TwoFactorService
  ) {}

  async register(
    registerDto: RegisterDto
  ): Promise<{ user: Partial<User>; tokens: any }> {
    const { email, password, fullname, birth_date } = registerDto;

    // Verificar se o usuário já existe
    const existingUser = await this.userRepository.findOne({
      where: { email },
    });
    if (existingUser) {
      throw new ConflictException("Email já está em uso");
    }

    // Hash da senha
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);

    // Criar token de verificação de email
    const email_verification_token = uuidv4();
    const email_verification_expires = new Date();
    email_verification_expires.setHours(
      email_verification_expires.getHours() + 24
    );

    // Criar usuário
    const user = this.userRepository.create({
      fullname,
      email,
      password_hash,
      birth_date: birth_date ? new Date(birth_date) : null,
      email_verification_token,
      email_verification_expires,
      auth_provider: AuthProvider.LOCAL,
    });

    const savedUser = await this.userRepository.save(user);

    // Enviar email de verificação
    await this.emailService.sendVerificationEmail(
      email,
      email_verification_token
    );

    // Gerar tokens
    const tokens = await this.generateTokens(savedUser);

    // Remover dados sensíveis
    const { password_hash: _, two_factor_secret, ...userResponse } = savedUser;

    return {
      user: userResponse,
      tokens,
    };
  }

  async login(
    loginDto: LoginDto
  ): Promise<{ user: Partial<User>; tokens: any }> {
    const { email, password, twoFactorCode } = loginDto;

    // Buscar usuário
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new UnauthorizedException("Credenciais inválidas");
    }

    // Verificar senha (apenas para usuários locais)
    if (user.auth_provider === AuthProvider.LOCAL) {
      if (!user.password_hash) {
        throw new UnauthorizedException("Credenciais inválidas");
      }

      const isPasswordValid = await bcrypt.compare(
        password,
        user.password_hash
      );
      if (!isPasswordValid) {
        throw new UnauthorizedException("Credenciais inválidas");
      }
    }

    // Verificar 2FA se habilitado
    if (user.two_factor_enabled) {
      if (!twoFactorCode) {
        throw new UnauthorizedException("Código 2FA é obrigatório");
      }

      const isValidCode = await this.twoFactorService.verifyToken(
        user.two_factor_secret,
        twoFactorCode
      );

      if (!isValidCode) {
        throw new UnauthorizedException("Código 2FA inválido");
      }
    }

    // Atualizar último login
    user.last_login = new Date();
    await this.userRepository.save(user);

    // Gerar tokens
    const tokens = await this.generateTokens(user);

    // Remover dados sensíveis
    const { password_hash: _, two_factor_secret, ...userResponse } = user;

    return {
      user: userResponse,
      tokens,
    };
  }

  async generateTokens(
    user: User
  ): Promise<{ access_token: string; refresh_token: string }> {
    const payload = { sub: user.id, email: user.email };

    const [access_token, refresh_token] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get("jwt.secret"),
        expiresIn: this.configService.get("jwt.expiresIn"),
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get("jwt.refreshSecret"),
        expiresIn: this.configService.get("jwt.refreshExpiresIn"),
      }),
    ]);

    return {
      access_token,
      refresh_token,
    };
  }

  async validateUser(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user || !user.is_active) {
      throw new UnauthorizedException("Usuário não encontrado ou inativo");
    }
    return user;
  }

  async refreshToken(refreshToken: string): Promise<{ access_token: string }> {
    try {
      const payload = await this.jwtService.verifyAsync(refreshToken, {
        secret: this.configService.get("jwt.refreshSecret"),
      });

      const user = await this.validateUser(payload.sub);
      const newPayload = { sub: user.id, email: user.email };

      const access_token = await this.jwtService.signAsync(newPayload, {
        secret: this.configService.get("jwt.secret"),
        expiresIn: this.configService.get("jwt.expiresIn"),
      });

      return { access_token };
    } catch (error) {
      throw new UnauthorizedException("Token de refresh inválido");
    }
  }

  async forgotPassword(email: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      // Por segurança, não revelamos se o email existe ou não
      return {
        message: "Se o email existir, um link de recuperação será enviado",
      };
    }

    // Gerar token de recuperação
    const password_reset_token = uuidv4();
    const password_reset_expires = new Date();
    password_reset_expires.setHours(password_reset_expires.getHours() + 1); // 1 hora

    // Salvar token no usuário
    user.password_reset_token = password_reset_token;
    user.password_reset_expires = password_reset_expires;
    await this.userRepository.save(user);

    // Enviar email
    await this.emailService.sendPasswordResetEmail(email, password_reset_token);

    return {
      message: "Se o email existir, um link de recuperação será enviado",
    };
  }

  async resetPassword(
    token: string,
    newPassword: string
  ): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({
      where: {
        password_reset_token: token,
      },
    });

    if (
      !user ||
      !user.password_reset_expires ||
      user.password_reset_expires < new Date()
    ) {
      throw new BadRequestException("Token inválido ou expirado");
    }

    // Hash da nova senha
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(newPassword, saltRounds);

    // Atualizar senha e limpar tokens
    user.password_hash = password_hash;
    user.password_reset_token = null;
    user.password_reset_expires = null;
    await this.userRepository.save(user);

    return { message: "Senha redefinida com sucesso" };
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({
      where: {
        email_verification_token: token,
      },
    });

    if (
      !user ||
      !user.email_verification_expires ||
      user.email_verification_expires < new Date()
    ) {
      throw new BadRequestException("Token inválido ou expirado");
    }

    // Marcar email como verificado
    user.email_verified = true;
    user.email_verification_token = null;
    user.email_verification_expires = null;
    await this.userRepository.save(user);

    return { message: "Email verificado com sucesso" };
  }

  async enable2FA(userId: string): Promise<{ secret: string; qrCode: string }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException("Usuário não encontrado");
    }

    if (user.two_factor_enabled) {
      throw new BadRequestException("2FA já está habilitado");
    }

    // Gerar secret e QR code
    const { secret, qrCodeUrl } = this.twoFactorService.generateSecret(
      user.email
    );
    const qrCode = await this.twoFactorService.generateQRCode(qrCodeUrl);

    // Salvar secret temporariamente (será confirmado quando o usuário verificar)
    user.two_factor_secret = secret;
    await this.userRepository.save(user);

    return { secret, qrCode };
  }

  async confirm2FA(
    userId: string,
    code: string
  ): Promise<{ backupCodes: string[] }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException("Usuário não encontrado");
    }

    if (!user.two_factor_secret) {
      throw new BadRequestException("2FA não foi iniciado");
    }

    // Verificar código
    const isValidCode = this.twoFactorService.verifyToken(
      user.two_factor_secret,
      code
    );
    if (!isValidCode) {
      throw new BadRequestException("Código inválido");
    }

    // Gerar códigos de backup
    const backupCodes = this.twoFactorService.generateBackupCodes();

    // Habilitar 2FA
    user.two_factor_enabled = true;
    user.two_factor_backup_codes = backupCodes;
    await this.userRepository.save(user);

    // Enviar códigos de backup por email
    await this.emailService.sendTwoFactorBackupCodes(user.email, backupCodes);

    return { backupCodes };
  }

  async disable2FA(userId: string, code: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException("Usuário não encontrado");
    }

    if (!user.two_factor_enabled) {
      throw new BadRequestException("2FA não está habilitado");
    }

    // Verificar código (pode ser TOTP ou backup code)
    let isValidCode = false;

    if (user.two_factor_secret) {
      isValidCode = this.twoFactorService.verifyToken(
        user.two_factor_secret,
        code
      );
    }

    if (!isValidCode && user.two_factor_backup_codes.length > 0) {
      isValidCode = this.twoFactorService.verifyBackupCode(
        user.two_factor_backup_codes,
        code
      );
    }

    if (!isValidCode) {
      throw new BadRequestException("Código inválido");
    }

    // Desabilitar 2FA
    user.two_factor_enabled = false;
    user.two_factor_secret = null;
    user.two_factor_backup_codes = [];
    await this.userRepository.save(user);

    return { message: "2FA desabilitado com sucesso" };
  }

  async handleOAuthLogin(
    user: User
  ): Promise<{ user: Partial<User>; tokens: any }> {
    // Gerar tokens para usuário OAuth
    const tokens = await this.generateTokens(user);

    // Remover dados sensíveis
    const { password_hash: _, two_factor_secret, ...userResponse } = user;

    return {
      user: userResponse,
      tokens,
    };
  }
}

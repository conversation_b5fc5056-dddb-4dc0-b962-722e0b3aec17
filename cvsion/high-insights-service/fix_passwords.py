#!/usr/bin/env python3
"""
Script para verificar e corrigir os hashes de senha no banco de dados
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from database import engine
from sqlalchemy import text
from passlib.context import CryptContext

# Configurar context do bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def test_password_verification():
    """Testar verificação de senha"""
    # Senha que deveria funcionar
    plain_password = "admin123"
    
    # Hash atual do banco
    stored_hash = "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/8K5K5K2"
    
    print("🔍 Testando verificação de senha...")
    print(f"Senha: {plain_password}")
    print(f"Hash: {stored_hash}")
    
    try:
        # Testar verificação
        is_valid = pwd_context.verify(plain_password, stored_hash)
        print(f"✅ Verificação: {is_valid}")
        
        if not is_valid:
            # Gerar novo hash
            new_hash = pwd_context.hash(plain_password)
            print(f"🔧 Novo hash gerado: {new_hash}")
            
            # Testar novo hash
            new_is_valid = pwd_context.verify(plain_password, new_hash)
            print(f"✅ Novo hash válido: {new_is_valid}")
            
            return new_hash
        
        return stored_hash
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None

def update_passwords_in_db():
    """Atualizar senhas no banco de dados"""
    print("\n🔧 Atualizando senhas no banco...")
    
    # Gerar hash correto para admin123
    correct_hash = pwd_context.hash("admin123")
    print(f"Hash correto: {correct_hash}")
    
    try:
        with engine.connect() as conn:
            # Atualizar todos os admins com a senha correta
            result = conn.execute(text("""
                UPDATE admins 
                SET password_hash = :new_hash
                WHERE password_hash = '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/8K5K5K2'
            """), {"new_hash": correct_hash})
            
            conn.commit()
            
            print(f"✅ {result.rowcount} senhas atualizadas!")
            
            # Verificar resultado
            check_result = conn.execute(text("SELECT username, password_hash FROM admins LIMIT 3"))
            print("\n📋 Admins atualizados:")
            for row in check_result:
                print(f"   {row[0]}: {row[1][:30]}...")
                
    except Exception as e:
        print(f"❌ Erro ao atualizar banco: {e}")

def test_login():
    """Testar login após correção"""
    print("\n🧪 Testando login...")
    
    try:
        with engine.connect() as conn:
            # Buscar admin
            result = conn.execute(text("""
                SELECT username, password_hash FROM admins 
                WHERE username = 'admin_agents'
            """))
            
            row = result.first()
            if row:
                username, stored_hash = row
                print(f"Usuário: {username}")
                print(f"Hash: {stored_hash[:50]}...")
                
                # Testar senha
                is_valid = pwd_context.verify("admin123", stored_hash)
                print(f"✅ Login válido: {is_valid}")
                
                if is_valid:
                    print("🎉 Login funcionando corretamente!")
                else:
                    print("❌ Login ainda não está funcionando")
            else:
                print("❌ Usuário não encontrado")
                
    except Exception as e:
        print(f"❌ Erro no teste: {e}")

if __name__ == "__main__":
    print("🔒 Script de Correção de Senhas")
    print("=" * 50)
    
    # Testar verificação atual
    new_hash = test_password_verification()
    
    # Se não funcionou, atualizar no banco
    if new_hash:
        update_passwords_in_db()
        
        # Testar novamente
        test_login()
    
    print("\n✅ Script concluído!")
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, DECIMAL, Date, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class Company(Base):
    __tablename__ = "companies"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    admins = relationship("Admin", back_populates="company")
    team_members = relationship("TeamMember", back_populates="company")
    products = relationship("Product", back_populates="company")
    leads = relationship("Lead", back_populates="company")
    sales = relationship("Sale", back_populates="company")
    goals = relationship("Goal", back_populates="company")
    followups = relationship("Followup", back_populates="company")
    preorders = relationship("Preorder", back_populates="company")
    audit_logs = relationship("AuditLog", back_populates="company")

class Admin(Base):
    __tablename__ = "admins"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(100))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="admins")
    audit_logs = relationship("AuditLog", back_populates="admin")

class TeamMember(Base):
    __tablename__ = "team_members"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    name = Column(String(100), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    position = Column(String(100))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="team_members")
    followups = relationship("Followup", back_populates="team_member")

class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    name = Column(String(200), nullable=False)
    price = Column(DECIMAL(10, 2), nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="products")
    sales = relationship("Sale", back_populates="product")
    preorders = relationship("Preorder", back_populates="product")

class Lead(Base):
    __tablename__ = "leads"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    name = Column(String(100), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    social_media = Column(String(200))
    source = Column(String(100))
    status = Column(String(50), default="new")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="leads")
    sales = relationship("Sale", back_populates="lead")
    followups = relationship("Followup", back_populates="lead")
    preorders = relationship("Preorder", back_populates="lead")

class Sale(Base):
    __tablename__ = "sales"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    lead_id = Column(Integer, ForeignKey("leads.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    amount = Column(DECIMAL(10, 2), nullable=False)
    sale_date = Column(Date, nullable=False)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="sales")
    lead = relationship("Lead", back_populates="sales")
    product = relationship("Product", back_populates="sales")

class Goal(Base):
    __tablename__ = "goals"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    target_value = Column(DECIMAL(10, 2), nullable=False)
    current_value = Column(DECIMAL(10, 2), default=0)
    goal_type = Column(String(50), nullable=False)  # 'sales', 'leads', 'revenue'
    period_type = Column(String(20), nullable=False)  # 'monthly', 'quarterly', 'yearly'
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="goals")

class Followup(Base):
    __tablename__ = "followups"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    lead_id = Column(Integer, ForeignKey("leads.id"))
    team_member_id = Column(Integer, ForeignKey("team_members.id"))
    title = Column(String(200), nullable=False)
    description = Column(Text)
    scheduled_date = Column(DateTime, nullable=False)
    status = Column(String(20), default="pending")  # 'pending', 'complete', 'declined'
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="followups")
    lead = relationship("Lead", back_populates="followups")
    team_member = relationship("TeamMember", back_populates="followups")

class Preorder(Base):
    __tablename__ = "preorders"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    lead_id = Column(Integer, ForeignKey("leads.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    amount = Column(DECIMAL(10, 2), nullable=False)
    status = Column(String(50), default="pending")  # 'pending', 'confirmed', 'cancelled'
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="preorders")
    lead = relationship("Lead", back_populates="preorders")
    product = relationship("Product", back_populates="preorders")

class AuditLog(Base):
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    admin_id = Column(Integer, ForeignKey("admins.id"))
    action = Column(String(50), nullable=False)  # 'create', 'update', 'delete'
    table_name = Column(String(50), nullable=False)
    record_id = Column(Integer)
    old_values = Column(JSON)
    new_values = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    
    # Relacionamentos
    company = relationship("Company", back_populates="audit_logs")
    admin = relationship("Admin", back_populates="audit_logs")
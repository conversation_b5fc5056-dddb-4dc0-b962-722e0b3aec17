#!/usr/bin/env python3
"""
Script para testar a autenticação
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Admin, Company
from auth import verify_password

# Configuração do banco
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://dev:devpass@localhost:5432/multitenancy_hub")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_auth():
    """Testar autenticação"""
    
    db = SessionLocal()
    
    try:
        # Buscar um admin
        admin = db.query(Admin).first()
        if not admin:
            print("❌ Nenhum admin encontrado no banco")
            return
        
        print(f"✅ Admin encontrado: {admin.username}")
        print(f"   Company ID: {admin.company_id}")
        print(f"   Email: {admin.email}")
        print(f"   Password Hash: {admin.password_hash}")
        
        # Testar verificação de senha
        test_password = "admin123"
        is_valid = verify_password(test_password, admin.password_hash)
        
        print(f"   Testando senha '{test_password}': {'✅ Válida' if is_valid else '❌ Inválida'}")
        
        # Testar com senha errada
        wrong_password = "wrong123"
        is_invalid = verify_password(wrong_password, admin.password_hash)
        
        print(f"   Testando senha '{wrong_password}': {'❌ Válida (ERRO!)' if is_invalid else '✅ Inválida (correto)'}")
        
        # Listar todos os admins
        print("\n📋 Todos os admins:")
        admins = db.query(Admin).all()
        for admin in admins:
            company = db.query(Company).filter(Company.id == admin.company_id).first()
            print(f"   • {admin.username} ({company.name if company else 'N/A'})")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    test_auth()

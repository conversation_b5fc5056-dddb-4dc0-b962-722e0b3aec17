from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import Admin, Preorder
from schemas import PreorderCreate, PreorderUpdate, PreorderResponse
from auth import get_current_admin

router = APIRouter()

@router.get("/", response_model=List[PreorderResponse])
async def get_preorders(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    preorders = db.query(Preorder).filter(Preorder.company_id == current_admin.company_id).all()
    return preorders

@router.post("/", response_model=PreorderResponse)
async def create_preorder(
    preorder_data: PreorderCreate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    preorder = Preorder(
        company_id=current_admin.company_id,
        **preorder_data.dict()
    )
    db.add(preorder)
    db.commit()
    db.refresh(preorder)
    return preorder

@router.get("/{preorder_id}", response_model=PreorderResponse)
async def get_preorder(
    preorder_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    preorder = db.query(Preorder).filter(
        Preorder.id == preorder_id,
        Preorder.company_id == current_admin.company_id
    ).first()
    
    if not preorder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pré-venda não encontrada"
        )
    
    return preorder

@router.put("/{preorder_id}", response_model=PreorderResponse)
async def update_preorder(
    preorder_id: int,
    preorder_data: PreorderUpdate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    preorder = db.query(Preorder).filter(
        Preorder.id == preorder_id,
        Preorder.company_id == current_admin.company_id
    ).first()
    
    if not preorder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pré-venda não encontrada"
        )
    
    update_data = preorder_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(preorder, field, value)
    
    db.commit()
    db.refresh(preorder)
    return preorder

@router.delete("/{preorder_id}")
async def delete_preorder(
    preorder_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    preorder = db.query(Preorder).filter(
        Preorder.id == preorder_id,
        Preorder.company_id == current_admin.company_id
    ).first()
    
    if not preorder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pré-venda não encontrada"
        )
    
    db.delete(preorder)
    db.commit()
    return {"message": "Pré-venda deletada com sucesso"}
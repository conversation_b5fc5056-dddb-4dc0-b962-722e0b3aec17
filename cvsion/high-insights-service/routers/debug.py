"""
Router para endpoints de debug e verificação
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from database import get_db
from models import Admin, Company
from debug_utils import debug_table_info, debug_session_queries
import os

router = APIRouter()

@router.get("/debug/database")
async def debug_database(db: Session = Depends(get_db)):
    """
    Endpoint para verificar o estado das tabelas do banco
    """
    if os.getenv("DEBUG") != "True":
        raise HTTPException(status_code=404, detail="Debug mode not enabled")
    
    try:
        # Informações da conexão
        engine_info = {
            "database_url": str(db.get_bind().url),
            "engine": str(db.get_bind()),
        }
        
        # Contar registros em cada tabela
        companies_count = db.query(Company).count()
        admins_count = db.query(Admin).count()
        
        # Buscar algumas amostras
        companies_sample = db.query(Company).limit(5).all()
        admins_sample = db.query(Admin).limit(5).all()
        
        return {
            "status": "success",
            "database_info": engine_info,
            "tables": {
                "companies": {
                    "count": companies_count,
                    "sample": [
                        {
                            "id": c.id,
                            "name": c.name,
                            "created_at": c.created_at
                        } for c in companies_sample
                    ]
                },
                "admins": {
                    "count": admins_count,
                    "sample": [
                        {
                            "id": a.id,
                            "company_id": a.company_id,
                            "username": a.username,
                            "email": a.email
                        } for a in admins_sample
                    ]
                }
            }
        }
    
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "type": type(e).__name__
        }

@router.get("/debug/tables/{table_name}")
async def debug_specific_table(table_name: str, db: Session = Depends(get_db)):
    """
    Debug de uma tabela específica
    """
    if os.getenv("DEBUG") != "True":
        raise HTTPException(status_code=404, detail="Debug mode not enabled")
    
    try:
        # Query direta na tabela
        query = text(f"SELECT * FROM {table_name} LIMIT 10")
        result = db.execute(query).fetchall()
        
        count_query = text(f"SELECT COUNT(*) FROM {table_name}")
        count = db.execute(count_query).scalar()
        
        return {
            "status": "success",
            "table": table_name,
            "total_records": count,
            "sample_data": [dict(row._mapping) for row in result]
        }
    
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "type": type(e).__name__
        }

@router.post("/debug/test-auth")
async def test_authentication(
    username: str, 
    password: str, 
    db: Session = Depends(get_db)
):
    """
    Testar autenticação sem fazer login completo
    """
    if os.getenv("DEBUG") != "True":
        raise HTTPException(status_code=404, detail="Debug mode not enabled")
    
    from auth import authenticate_admin
    
    try:
        admin = authenticate_admin(db, username, password)
        
        if admin:
            company = db.query(Company).filter(Company.id == admin.company_id).first()
            return {
                "status": "success",
                "admin_found": True,
                "admin": {
                    "id": admin.id,
                    "username": admin.username,
                    "company_id": admin.company_id,
                    "email": admin.email
                },
                "company": {
                    "id": company.id,
                    "name": company.name
                } if company else None
            }
        else:
            return {
                "status": "failed",
                "admin_found": False,
                "message": "Credenciais inválidas ou usuário não encontrado"
            }
    
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "type": type(e).__name__
        }
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from database import get_db
from models import Admin, Lead, Sale, Preorder
from auth import get_current_admin
import pandas as pd
import io

router = APIRouter()

@router.get("/leads")
async def export_leads_csv(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    leads = db.query(Lead).filter(Lead.company_id == current_admin.company_id).all()
    
    if not leads:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Nenhum lead encontrado"
        )
    
    # Converter para DataFrame
    leads_data = []
    for lead in leads:
        leads_data.append({
            'ID': lead.id,
            'Nome': lead.name,
            'Email': lead.email or '',
            'Telefone': lead.phone or '',
            'Redes Sociais': lead.social_media or '',
            'Origem': lead.source or '',
            'Status': lead.status,
            'Data de Criação': lead.created_at.strftime('%d/%m/%Y %H:%M')
        })
    
    df = pd.DataFrame(leads_data)
    
    # Criar arquivo CSV em memória
    output = io.StringIO()
    df.to_csv(output, index=False, encoding='utf-8')
    output.seek(0)
    
    return StreamingResponse(
        io.BytesIO(output.getvalue().encode('utf-8')),
        media_type='text/csv',
        headers={'Content-Disposition': 'attachment; filename=leads.csv'}
    )

@router.get("/sales")
async def export_sales_csv(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    sales = db.query(Sale).filter(Sale.company_id == current_admin.company_id).all()
    
    if not sales:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Nenhuma venda encontrada"
        )
    
    # Converter para DataFrame
    sales_data = []
    for sale in sales:
        sales_data.append({
            'ID': sale.id,
            'Lead': sale.lead.name if sale.lead else '',
            'Produto': sale.product.name if sale.product else '',
            'Valor': float(sale.amount),
            'Data da Venda': sale.sale_date.strftime('%d/%m/%Y'),
            'Notas': sale.notes or '',
            'Data de Criação': sale.created_at.strftime('%d/%m/%Y %H:%M')
        })
    
    df = pd.DataFrame(sales_data)
    
    # Criar arquivo CSV em memória
    output = io.StringIO()
    df.to_csv(output, index=False, encoding='utf-8')
    output.seek(0)
    
    return StreamingResponse(
        io.BytesIO(output.getvalue().encode('utf-8')),
        media_type='text/csv',
        headers={'Content-Disposition': 'attachment; filename=vendas.csv'}
    )

@router.get("/preorders")
async def export_preorders_csv(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    preorders = db.query(Preorder).filter(Preorder.company_id == current_admin.company_id).all()
    
    if not preorders:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Nenhuma pré-venda encontrada"
        )
    
    # Converter para DataFrame
    preorders_data = []
    for preorder in preorders:
        preorders_data.append({
            'ID': preorder.id,
            'Lead': preorder.lead.name if preorder.lead else '',
            'Produto': preorder.product.name if preorder.product else '',
            'Valor': float(preorder.amount),
            'Status': preorder.status,
            'Notas': preorder.notes or '',
            'Data de Criação': preorder.created_at.strftime('%d/%m/%Y %H:%M')
        })
    
    df = pd.DataFrame(preorders_data)
    
    # Criar arquivo CSV em memória
    output = io.StringIO()
    df.to_csv(output, index=False, encoding='utf-8')
    output.seek(0)
    
    return StreamingResponse(
        io.BytesIO(output.getvalue().encode('utf-8')),
        media_type='text/csv',
        headers={'Content-Disposition': 'attachment; filename=pre-vendas.csv'}
    )
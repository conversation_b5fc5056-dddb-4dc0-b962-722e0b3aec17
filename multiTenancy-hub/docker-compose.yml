version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: multitenancy_hub
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: devpass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  adminer:
    image: adminer
    ports:
      - "8080:8080"
    networks:
      - app-network
    depends_on:
      - postgres

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: **************************************/multitenancy_hub
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
    volumes:
      - ./backend:/app
    networks:
      - app-network
    depends_on:
      - postgres

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - app-network
    depends_on:
      - backend

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
'use client'

import { useEffect, useState } from 'react'
import Layout from '@/components/Layout'
import api from '@/lib/api'
import { Plus, Edit, Trash2, Download } from 'lucide-react'
import toast from 'react-hot-toast'

interface Preorder {
  id: number
  lead_id?: number
  product_id?: number
  amount: number
  status: string
  notes?: string
  created_at: string
  updated_at: string
  lead?: { name: string }
  product?: { name: string }
}

export default function PreordersPage() {
  const [preorders, setPreorders] = useState<Preorder[]>([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingPreorder, setEditingPreorder] = useState<Preorder | null>(null)
  const [formData, setFormData] = useState({
    lead_id: '',
    product_id: '',
    amount: '',
    status: 'pending',
    notes: ''
  })

  useEffect(() => {
    fetchPreorders()
  }, [])

  const fetchPreorders = async () => {
    try {
      const response = await api.get('/preorders')
      setPreorders(response.data)
    } catch (error) {
      toast.error('Erro ao carregar pré-vendas')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const preorderData = {
        ...formData,
        lead_id: formData.lead_id ? parseInt(formData.lead_id) : null,
        product_id: formData.product_id ? parseInt(formData.product_id) : null,
        amount: parseFloat(formData.amount)
      }
      
      if (editingPreorder) {
        await api.put(`/preorders/${editingPreorder.id}`, preorderData)
        toast.success('Pré-venda atualizada com sucesso!')
      } else {
        await api.post('/preorders', preorderData)
        toast.success('Pré-venda criada com sucesso!')
      }
      setShowModal(false)
      setEditingPreorder(null)
      setFormData({
        lead_id: '',
        product_id: '',
        amount: '',
        status: 'pending',
        notes: ''
      })
      fetchPreorders()
    } catch (error) {
      toast.error('Erro ao salvar pré-venda')
    }
  }

  const handleEdit = (preorder: Preorder) => {
    setEditingPreorder(preorder)
    setFormData({
      lead_id: preorder.lead_id?.toString() || '',
      product_id: preorder.product_id?.toString() || '',
      amount: preorder.amount.toString(),
      status: preorder.status,
      notes: preorder.notes || ''
    })
    setShowModal(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja deletar esta pré-venda?')) {
      try {
        await api.delete(`/preorders/${id}`)
        toast.success('Pré-venda deletada com sucesso!')
        fetchPreorders()
      } catch (error) {
        toast.error('Erro ao deletar pré-venda')
      }
    }
  }

  const handleExport = async () => {
    try {
      const response = await api.get('/export/preorders', {
        responseType: 'blob'
      })
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'pre-vendas.csv')
      document.body.appendChild(link)
      link.click()
      link.remove()
      
      toast.success('Pré-vendas exportadas com sucesso!')
    } catch (error) {
      toast.error('Erro ao exportar pré-vendas')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pré-vendas</h1>
            <p className="mt-1 text-sm text-gray-500">
              Gerencie suas pré-vendas e clientes em processo
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleExport}
              className="btn-secondary flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar CSV
            </button>
            <button
              onClick={() => {
                setEditingPreorder(null)
                setFormData({
                  lead_id: '',
                  product_id: '',
                  amount: '',
                  status: 'pending',
                  notes: ''
                })
                setShowModal(true)
              }}
              className="btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Pré-venda
            </button>
          </div>
        </div>

        {/* Tabela de Pré-vendas */}
        <div className="card">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="table-header">Lead</th>
                  <th className="table-header">Produto</th>
                  <th className="table-header">Valor</th>
                  <th className="table-header">Status</th>
                  <th className="table-header">Data de Criação</th>
                  <th className="table-header">Ações</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {preorders.map((preorder) => (
                  <tr key={preorder.id}>
                    <td className="table-cell">{preorder.lead?.name || '-'}</td>
                    <td className="table-cell">{preorder.product?.name || '-'}</td>
                    <td className="table-cell font-medium">
                      R$ {preorder.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </td>
                    <td className="table-cell">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(preorder.status)}`}>
                        {preorder.status}
                      </span>
                    </td>
                    <td className="table-cell">
                      {new Date(preorder.created_at).toLocaleDateString('pt-BR')}
                    </td>
                    <td className="table-cell">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(preorder)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(preorder.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modal de Criação/Edição */}
        {showModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingPreorder ? 'Editar Pré-venda' : 'Nova Pré-venda'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Lead ID</label>
                    <input
                      type="number"
                      className="input-field"
                      value={formData.lead_id}
                      onChange={(e) => setFormData({ ...formData, lead_id: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Produto ID</label>
                    <input
                      type="number"
                      className="input-field"
                      value={formData.product_id}
                      onChange={(e) => setFormData({ ...formData, product_id: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Valor *</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      required
                      className="input-field"
                      value={formData.amount}
                      onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <select
                      className="input-field"
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    >
                      <option value="pending">Pendente</option>
                      <option value="confirmed">Confirmado</option>
                      <option value="cancelled">Cancelado</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notas</label>
                    <textarea
                      className="input-field"
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    />
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="btn-secondary"
                    >
                      Cancelar
                    </button>
                    <button type="submit" className="btn-primary">
                      {editingPreorder ? 'Atualizar' : 'Criar'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
'use client'

import { useEffect, useState } from 'react'
import Layout from '@/components/Layout'
import api from '@/lib/api'
import { Plus, Edit, Trash2 } from 'lucide-react'
import toast from 'react-hot-toast'

interface Followup {
  id: number
  lead_id?: number
  team_member_id?: number
  title: string
  description?: string
  scheduled_date: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
  lead?: { name: string }
  team_member?: { name: string }
}

export default function FollowupsPage() {
  const [followups, setFollowups] = useState<Followup[]>([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingFollowup, setEditingFollowup] = useState<Followup | null>(null)
  const [formData, setFormData] = useState({
    lead_id: '',
    team_member_id: '',
    title: '',
    description: '',
    scheduled_date: '',
    status: 'pending',
    notes: ''
  })

  useEffect(() => {
    fetchFollowups()
  }, [])

  const fetchFollowups = async () => {
    try {
      const response = await api.get('/followups')
      setFollowups(response.data)
    } catch (error) {
      toast.error('Erro ao carregar follow-ups')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const followupData = {
        ...formData,
        lead_id: formData.lead_id ? parseInt(formData.lead_id) : null,
        team_member_id: formData.team_member_id ? parseInt(formData.team_member_id) : null,
        scheduled_date: new Date(formData.scheduled_date).toISOString()
      }
      
      if (editingFollowup) {
        await api.put(`/followups/${editingFollowup.id}`, followupData)
        toast.success('Follow-up atualizado com sucesso!')
      } else {
        await api.post('/followups', followupData)
        toast.success('Follow-up criado com sucesso!')
      }
      setShowModal(false)
      setEditingFollowup(null)
      setFormData({
        lead_id: '',
        team_member_id: '',
        title: '',
        description: '',
        scheduled_date: '',
        status: 'pending',
        notes: ''
      })
      fetchFollowups()
    } catch (error) {
      toast.error('Erro ao salvar follow-up')
    }
  }

  const handleEdit = (followup: Followup) => {
    setEditingFollowup(followup)
    setFormData({
      lead_id: followup.lead_id?.toString() || '',
      team_member_id: followup.team_member_id?.toString() || '',
      title: followup.title,
      description: followup.description || '',
      scheduled_date: new Date(followup.scheduled_date).toISOString().slice(0, 16),
      status: followup.status,
      notes: followup.notes || ''
    })
    setShowModal(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja deletar este follow-up?')) {
      try {
        await api.delete(`/followups/${id}`)
        toast.success('Follow-up deletado com sucesso!')
        fetchFollowups()
      } catch (error) {
        toast.error('Erro ao deletar follow-up')
      }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'complete': return 'bg-green-100 text-green-800'
      case 'declined': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Follow-ups</h1>
            <p className="mt-1 text-sm text-gray-500">
              Gerencie seus agendamentos e follow-ups
            </p>
          </div>
          <button
            onClick={() => {
              setEditingFollowup(null)
              setFormData({
                lead_id: '',
                team_member_id: '',
                title: '',
                description: '',
                scheduled_date: '',
                status: 'pending',
                notes: ''
              })
              setShowModal(true)
            }}
            className="btn-primary flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Follow-up
          </button>
        </div>

        {/* Tabela de Follow-ups */}
        <div className="card">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="table-header">Título</th>
                  <th className="table-header">Lead</th>
                  <th className="table-header">Responsável</th>
                  <th className="table-header">Data/Hora</th>
                  <th className="table-header">Status</th>
                  <th className="table-header">Ações</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {followups.map((followup) => (
                  <tr key={followup.id}>
                    <td className="table-cell font-medium">{followup.title}</td>
                    <td className="table-cell">{followup.lead?.name || '-'}</td>
                    <td className="table-cell">{followup.team_member?.name || '-'}</td>
                    <td className="table-cell">
                      {new Date(followup.scheduled_date).toLocaleString('pt-BR')}
                    </td>
                    <td className="table-cell">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(followup.status)}`}>
                        {followup.status}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(followup)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(followup.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modal de Criação/Edição */}
        {showModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingFollowup ? 'Editar Follow-up' : 'Novo Follow-up'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Título *</label>
                    <input
                      type="text"
                      required
                      className="input-field"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Descrição</label>
                    <textarea
                      className="input-field"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Lead ID</label>
                    <input
                      type="number"
                      className="input-field"
                      value={formData.lead_id}
                      onChange={(e) => setFormData({ ...formData, lead_id: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Responsável ID</label>
                    <input
                      type="number"
                      className="input-field"
                      value={formData.team_member_id}
                      onChange={(e) => setFormData({ ...formData, team_member_id: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Data/Hora *</label>
                    <input
                      type="datetime-local"
                      required
                      className="input-field"
                      value={formData.scheduled_date}
                      onChange={(e) => setFormData({ ...formData, scheduled_date: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <select
                      className="input-field"
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    >
                      <option value="pending">Pendente</option>
                      <option value="complete">Concluído</option>
                      <option value="declined">Recusado</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notas</label>
                    <textarea
                      className="input-field"
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    />
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="btn-secondary"
                    >
                      Cancelar
                    </button>
                    <button type="submit" className="btn-primary">
                      {editingFollowup ? 'Atualizar' : 'Criar'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
{"extends": ["next/core-web-vitals", "eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "no-debugger": "error", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off", "react/display-name": "off"}, "env": {"browser": true, "es2021": true, "node": true}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "detect"}}}
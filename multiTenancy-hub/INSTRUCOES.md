# 🚀 Instruções de Execução - Multi-Tenancy Hub

## 📋 Pré-requisitos

- Docker e Docker Compose instalados
- Git instalado

## 🚀 Como Executar

### 1. Clone o repositório
```bash
git clone <repository-url>
cd multiTenancy-hub
```

### 2. Execute com Docker Compose
```bash
docker compose up --build
```

### 3. Aguarde a inicialização
- O banco de dados será criado automaticamente
- Os dados iniciais serão inseridos
- O backend e frontend serão iniciados

### 4. Acesse as aplicações
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Adminer (Banco)**: http://localhost:8080
  - Servidor: postgres
  - Usuário: dev
  - Senha: devpass
  - Banco: multitenancy_hub

## 🔐 Credenciais de <PERSON> as empresas têm a mesma senha padrão: **admin123**

### Usuários Administradores:
- `admin_webnar` - Empresa: high_webnar
- `admin_rec` - Empresa: high_rec
- `admin_copy` - Empresa: high_copy
- `admin_capital` - Empresa: high_capital
- `admin_finance` - Empresa: high_finance
- `admin_stage` - Empresa: high_stage
- `admin_pulse` - Empresa: high_pulse
- `admin_agents` - Empresa: high_agents

## 📊 Dados Iniciais

O sistema já vem com dados de exemplo:
- 8 empresas configuradas
- 8 administradores (um para cada empresa)
- 16 membros da equipe
- 16 produtos
- 16 leads
- 16 vendas
- 9 metas
- 16 follow-ups
- 16 pré-vendas

## 🛠️ Comandos Úteis

### Parar os containers
```bash
docker compose down
```

### Ver logs
```bash
docker compose logs -f
```

### Reconstruir containers
```bash
docker compose up --build --force-recreate
```

### Acessar o banco de dados
```bash
docker compose exec postgres psql -U dev -d multitenancy_hub
```

### Executar seed manualmente
```bash
docker compose exec backend python seed_database.py
```

## 🔧 Desenvolvimento

### Backend (FastAPI)
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend (Next.js)
```bash
cd frontend
npm install
npm run dev
```

## 📱 Funcionalidades

- ✅ Autenticação JWT com isolamento por empresa
- ✅ Dashboard com KPIs e gráficos
- ✅ CRUD completo para todos os módulos
- ✅ Exportação CSV
- ✅ Sistema de metas com progresso
- ✅ Follow-ups e agenda
- ✅ Pré-vendas
- ✅ Audit log

## 🚨 Solução de Problemas

### Erro de conexão com banco
- Verifique se o PostgreSQL está rodando
- Aguarde alguns segundos para o banco inicializar

### Erro de permissão
- Execute: `chmod +x scripts/init.sh`

### Porta já em uso
- Pare outros serviços nas portas 3000, 8000, 5432, 8080
- Ou altere as portas no docker-compose.yml

## 📞 Suporte

Para suporte técnico, entre em contato com a equipe de desenvolvimento.
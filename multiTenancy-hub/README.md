# Multi-Tenancy Hub

Dashboard multi-tenant para empresas, desenvolvido com Next.js, FastAPI e PostgreSQL.

## 🚀 Funcionalidades

- **Autenticação JWT** com isolamento por empresa
- **Dashboard com KPIs** e gráficos interativos
- **Gestão de Leads** com CRUD completo
- **Catálogo de Produtos** com preços
- **Gestão de Equipe** com informações de contato
- **Controle de Vendas** com histórico
- **Metas e Objetivos** com acompanhamento de progresso
- **Follow-ups e Agenda** para reuniões
- **Pré-vendas** para clientes em processo
- **Exportação CSV** para leads, vendas e pré-vendas
- **Audit Log** para rastreamento de alterações

## 🏢 Empresas Configuradas

- **high_webnar** - admin_webnar / admin123
- **high_rec** - admin_rec / admin123
- **high_copy** - admin_copy / admin123
- **high_capital** - admin_capital / admin123
- **high_finance** - admin_finance / admin123
- **high_stage** - admin_stage / admin123
- **high_pulse** - admin_pulse / admin123
- **high_agents** - admin_agents / admin123

## 🛠️ Stack Tecnológica

### Backend
- **FastAPI** - Framework web Python
- **PostgreSQL** - Banco de dados
- **SQLAlchemy** - ORM
- **JWT** - Autenticação
- **bcrypt** - Hash de senhas
- **Pandas** - Exportação CSV

### Frontend
- **Next.js 14** - Framework React
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Estilização
- **Recharts** - Gráficos
- **Axios** - Cliente HTTP
- **React Hook Form** - Formulários
- **React Hot Toast** - Notificações

### Infraestrutura
- **Docker Compose** - Orquestração
- **Adminer** - Interface do banco

## 📋 Pré-requisitos

- Docker e Docker Compose
- Git

## 🚀 Instalação e Execução

1. **Clone o repositório**
```bash
git clone <repository-url>
cd multiTenancy-hub
```

2. **Execute com Docker Compose**
```bash
docker compose up --build
```

3. **Acesse as aplicações**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Adminer (Banco)**: http://localhost:8080
  - Servidor: postgres
  - Usuário: dev
  - Senha: devpass
  - Banco: multitenancy_hub

## 🔐 Credenciais de Acesso

Todas as empresas têm a mesma senha padrão: **admin123**

### Usuários Administradores:
- `admin_webnar` - Empresa: high_webnar
- `admin_rec` - Empresa: high_rec
- `admin_copy` - Empresa: high_copy
- `admin_capital` - Empresa: high_capital
- `admin_finance` - Empresa: high_finance
- `admin_stage` - Empresa: high_stage
- `admin_pulse` - Empresa: high_pulse
- `admin_agents` - Empresa: high_agents

## 📊 Estrutura do Banco de Dados

### Tabelas Principais:
- **companies** - Empresas do sistema
- **admins** - Administradores por empresa
- **team_members** - Membros da equipe
- **products** - Catálogo de produtos
- **leads** - Leads e prospects
- **sales** - Vendas realizadas
- **goals** - Metas e objetivos
- **followups** - Agendamentos e follow-ups
- **preorders** - Pré-vendas
- **audit_logs** - Log de auditoria

## 🔧 Desenvolvimento

### Backend (FastAPI)
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend (Next.js)
```bash
cd frontend
npm install
npm run dev
```

## 📡 Endpoints da API

### Autenticação
- `POST /auth/login` - Login de usuário

### Dashboard
- `GET /dashboard/kpis` - KPIs do dashboard

### CRUD Endpoints
- `GET|POST /leads` - Gestão de leads
- `GET|POST /products` - Gestão de produtos
- `GET|POST /team` - Gestão de equipe
- `GET|POST /sales` - Gestão de vendas
- `GET|POST /goals` - Gestão de metas
- `GET|POST /followups` - Gestão de follow-ups
- `GET|POST /preorders` - Gestão de pré-vendas

### Exportação
- `GET /export/leads` - Exportar leads em CSV
- `GET /export/sales` - Exportar vendas em CSV
- `GET /export/preorders` - Exportar pré-vendas em CSV

## 🔒 Segurança

- **JWT** com expiração de 30 minutos
- **bcrypt** para hash de senhas
- **Isolamento por empresa** - cada admin só acessa dados da sua empresa
- **Validação de dados** com Pydantic
- **CORS** configurado para desenvolvimento

## 📱 Interface

- **Design responsivo** com Tailwind CSS
- **Componentes reutilizáveis** para formulários e tabelas
- **Gráficos interativos** com Recharts
- **Notificações** com React Hot Toast
- **Navegação intuitiva** com sidebar fixa

## 🚀 Deploy

Para produção, configure:
- Variáveis de ambiente seguras
- HTTPS
- Banco de dados externo
- Domínio personalizado
- Backup automático

## 📝 Licença

Este projeto é para uso interno da empresa.

## 🤝 Contribuição

Para contribuir com o projeto:
1. Faça um fork
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📞 Suporte

Para suporte técnico, entre em contato com a equipe de desenvolvimento.
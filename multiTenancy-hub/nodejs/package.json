{"name": "multitenancy-hub-backend-nodejs", "version": "1.0.0", "description": "Node.js Express backend API para sistema multi-tenant de dashboard empresarial", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "seed": "node src/scripts/seedDatabase.js", "test-auth": "node src/scripts/testAuth.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "keywords": ["nodejs", "express", "multi-tenant", "dashboard", "api", "postgresql", "jwt"], "author": "MultiTenancy Hub <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "sequelize": "^6.35.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "express-validator": "^7.0.1", "winston": "^3.11.0", "csv-parser": "^3.0.0", "json2csv": "^6.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.1.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/multitenancy-hub/backend-nodejs.git"}, "bugs": {"url": "https://github.com/multitenancy-hub/backend-nodejs/issues"}, "homepage": "https://github.com/multitenancy-hub/backend-nodejs#readme"}
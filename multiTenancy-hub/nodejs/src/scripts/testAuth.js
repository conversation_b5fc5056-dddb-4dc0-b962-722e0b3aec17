#!/usr/bin/env node

/**
 * Script para testar a autenticação
 */

require('dotenv').config();
const { sequelize } = require('../config/database');
const { authenticateAdmin, verifyPassword } = require('../utils/auth');
const { Admin, Company } = require('../models');

async function testAuth() {
  try {
    console.log('🔐 Testando sistema de autenticação...\n');

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Conexão com banco de dados estabelecida');

    // Get all admins
    const admins = await Admin.findAll({
      include: [{
        model: Company,
        as: 'company'
      }]
    });

    if (admins.length === 0) {
      console.log('❌ Nenhum administrador encontrado no banco de dados');
      console.log('💡 Execute o script de seed primeiro: npm run seed');
      return;
    }

    console.log(`\n📋 Encontrados ${admins.length} administradores:`);
    admins.forEach(admin => {
      console.log(`   • ${admin.username} (${admin.company.name})`);
    });

    // Test authentication for each admin
    console.log('\n🧪 Testando autenticação...');
    
    for (const admin of admins) {
      console.log(`\n--- Testando ${admin.username} ---`);
      
      // Test with correct password
      console.log('✅ Testando senha correta (admin123)...');
      const authResult = await authenticateAdmin(admin.username, 'admin123');
      
      if (authResult) {
        console.log(`   ✅ Autenticação bem-sucedida para ${admin.username}`);
        console.log(`   📊 Empresa: ${authResult.company.name}`);
        console.log(`   📧 Email: ${authResult.email || 'Não informado'}`);
      } else {
        console.log(`   ❌ Falha na autenticação para ${admin.username}`);
      }

      // Test with wrong password
      console.log('❌ Testando senha incorreta...');
      const wrongAuthResult = await authenticateAdmin(admin.username, 'senhaerrada');
      
      if (wrongAuthResult) {
        console.log(`   ❌ ERRO: Autenticação deveria ter falhado para ${admin.username}`);
      } else {
        console.log(`   ✅ Autenticação corretamente rejeitada para senha incorreta`);
      }
    }

    // Test password verification directly
    console.log('\n🔍 Testando verificação de senha diretamente...');
    const firstAdmin = admins[0];
    
    console.log('✅ Testando senha correta...');
    const correctPassword = await verifyPassword('admin123', firstAdmin.password_hash);
    console.log(`   Resultado: ${correctPassword ? '✅ Válida' : '❌ Inválida'}`);
    
    console.log('❌ Testando senha incorreta...');
    const wrongPassword = await verifyPassword('senhaerrada', firstAdmin.password_hash);
    console.log(`   Resultado: ${wrongPassword ? '❌ ERRO: Deveria ser inválida' : '✅ Corretamente inválida'}`);

    // Test non-existent user
    console.log('\n👻 Testando usuário inexistente...');
    const nonExistentAuth = await authenticateAdmin('usuario_inexistente', 'qualquersenha');
    console.log(`   Resultado: ${nonExistentAuth ? '❌ ERRO: Deveria falhar' : '✅ Corretamente rejeitado'}`);

    console.log('\n🎉 Testes de autenticação concluídos!');
    console.log('\n📝 Resumo:');
    console.log(`   • ${admins.length} administradores testados`);
    console.log('   • Autenticação com senha correta: ✅');
    console.log('   • Rejeição de senha incorreta: ✅');
    console.log('   • Rejeição de usuário inexistente: ✅');
    console.log('   • Verificação direta de senha: ✅');

  } catch (error) {
    console.error('❌ Erro durante os testes:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run test if called directly
if (require.main === module) {
  testAuth();
}

module.exports = { testAuth };

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Company = sequelize.define('Company', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'companies',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });

  Company.associate = (models) => {
    Company.hasMany(models.Admin, {
      foreignKey: 'company_id',
      as: 'admins',
    });
    Company.hasMany(models.TeamMember, {
      foreignKey: 'company_id',
      as: 'teamMembers',
    });
    Company.hasMany(models.Product, {
      foreignKey: 'company_id',
      as: 'products',
    });
    Company.hasMany(models.Lead, {
      foreignKey: 'company_id',
      as: 'leads',
    });
    Company.hasMany(models.Sale, {
      foreignKey: 'company_id',
      as: 'sales',
    });
    Company.hasMany(models.Goal, {
      foreignKey: 'company_id',
      as: 'goals',
    });
    Company.hasMany(models.Followup, {
      foreignKey: 'company_id',
      as: 'followups',
    });
    Company.hasMany(models.Preorder, {
      foreignKey: 'company_id',
      as: 'preorders',
    });
    Company.hasMany(models.AuditLog, {
      foreignKey: 'company_id',
      as: 'auditLogs',
    });
  };

  return Company;
};

const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://postgres:evolution@localhost:5432/dashboardhc';

const sequelize = new Sequelize(DATABASE_URL, {
  dialect: 'postgres',
  logging: process.env.NODE_ENV === 'development' ? (msg) => logger.debug(msg) : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  timezone: '+00:00', // UTC
});

module.exports = { sequelize };

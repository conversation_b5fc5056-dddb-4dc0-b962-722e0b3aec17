const { verifyToken, getAdminById } = require('../utils/auth');

/**
 * Middleware to authenticate requests using JWT
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Token de acesso requerido',
        code: 'MISSING_TOKEN'
      });
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Get admin details
    const admin = await getAdminById(decoded.sub);
    if (!admin) {
      return res.status(401).json({
        error: 'Usuário não encontrado',
        code: 'USER_NOT_FOUND'
      });
    }

    // Add admin info to request object
    req.admin = admin;
    req.companyId = admin.company_id;
    
    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    
    if (error.message === 'Token inválido') {
      return res.status(401).json({
        error: 'Token inválido',
        code: 'INVALID_TOKEN'
      });
    }

    return res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
}

/**
 * Middleware to extract admin info from token (optional authentication)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = verifyToken(token);
      const admin = await getAdminById(decoded.sub);
      
      if (admin) {
        req.admin = admin;
        req.companyId = admin.company_id;
      }
    }
    
    next();
  } catch (error) {
    // For optional auth, we don't return errors, just continue without auth
    next();
  }
}

module.exports = {
  authenticateToken,
  optionalAuth,
};

const Joi = require('joi');

const createFollowupSchema = Joi.object({
  lead_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID do lead deve ser um número',
      'number.integer': 'ID do lead deve ser um número inteiro',
      'number.positive': 'ID do lead deve ser um número positivo',
      'any.required': 'ID do lead é obrigatório'
    }),
  
  title: Joi.string()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': 'Título deve ter pelo menos 1 caractere',
      'string.max': 'Título deve ter no máximo 200 caracteres',
      'any.required': 'Título é obrigatório'
    }),
  
  description: Joi.string()
    .optional()
    .allow(null, '')
    .messages({
      'string.base': 'Descrição deve ser um texto'
    }),
  
  followup_date: Joi.date()
    .iso()
    .required()
    .messages({
      'date.base': 'Data do follow-up deve ser uma data válida',
      'date.iso': 'Data do follow-up deve estar no formato ISO (YYYY-MM-DD)',
      'any.required': 'Data do follow-up é obrigatória'
    }),
  
  status: Joi.string()
    .valid('pending', 'completed', 'cancelled')
    .default('pending')
    .optional()
    .messages({
      'any.only': 'Status deve ser um dos valores válidos: pending, completed, cancelled'
    }),
  
  notes: Joi.string()
    .optional()
    .allow(null, '')
    .messages({
      'string.base': 'Notas devem ser um texto'
    })
});

const updateFollowupSchema = Joi.object({
  lead_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.base': 'ID do lead deve ser um número',
      'number.integer': 'ID do lead deve ser um número inteiro',
      'number.positive': 'ID do lead deve ser um número positivo'
    }),
  
  title: Joi.string()
    .min(1)
    .max(200)
    .optional()
    .messages({
      'string.min': 'Título deve ter pelo menos 1 caractere',
      'string.max': 'Título deve ter no máximo 200 caracteres'
    }),
  
  description: Joi.string()
    .optional()
    .allow(null, '')
    .messages({
      'string.base': 'Descrição deve ser um texto'
    }),
  
  followup_date: Joi.date()
    .iso()
    .optional()
    .messages({
      'date.base': 'Data do follow-up deve ser uma data válida',
      'date.iso': 'Data do follow-up deve estar no formato ISO (YYYY-MM-DD)'
    }),
  
  status: Joi.string()
    .valid('pending', 'completed', 'cancelled')
    .optional()
    .messages({
      'any.only': 'Status deve ser um dos valores válidos: pending, completed, cancelled'
    }),
  
  notes: Joi.string()
    .optional()
    .allow(null, '')
    .messages({
      'string.base': 'Notas devem ser um texto'
    })
});

const followupIdSchema = Joi.object({
  followup_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID do follow-up deve ser um número',
      'number.integer': 'ID do follow-up deve ser um número inteiro',
      'number.positive': 'ID do follow-up deve ser um número positivo',
      'any.required': 'ID do follow-up é obrigatório'
    })
});

module.exports = {
  createFollowupSchema,
  updateFollowupSchema,
  followupIdSchema,
};

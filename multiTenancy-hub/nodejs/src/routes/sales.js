const express = require('express');
const { Sale, Lead, Product } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

router.get('/', async (req, res, next) => {
  try {
    const sales = await Sale.findAll({
      where: { company_id: req.companyId },
      include: [
        { model: Lead, as: 'lead', attributes: ['id', 'name'] },
        { model: Product, as: 'product', attributes: ['id', 'name'] }
      ],
      order: [['sale_date', 'DESC']]
    });
    res.json(sales);
  } catch (error) {
    next(error);
  }
});

router.post('/', async (req, res, next) => {
  try {
    const sale = await Sale.create({
      ...req.body,
      company_id: req.companyId
    });
    res.status(201).json(sale);
  } catch (error) {
    next(error);
  }
});

module.exports = router;

const express = require('express');
const { sequelize } = require('../config/database');
const { Sale, Lead, Goal, Followup, TeamMember } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * GET /dashboard/kpis
 * Get dashboard KPIs for the authenticated admin's company
 */
router.get('/kpis', async (req, res, next) => {
  try {
    const companyId = req.companyId;
    const currentDate = new Date();
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const currentYear = new Date(currentDate.getFullYear(), 0, 1);

    // Get total leads
    const totalLeads = await Lead.count({
      where: { company_id: companyId }
    });

    // Get leads this month
    const leadsThisMonth = await Lead.count({
      where: {
        company_id: companyId,
        created_at: {
          [Op.gte]: currentMonth
        }
      }
    });

    // Get total sales
    const totalSales = await Sale.count({
      where: { company_id: companyId }
    });

    // Get sales this month
    const salesThisMonth = await Sale.count({
      where: {
        company_id: companyId,
        sale_date: {
          [Op.gte]: currentMonth
        }
      }
    });

    // Get total revenue
    const totalRevenueResult = await Sale.findOne({
      where: { company_id: companyId },
      attributes: [
        [sequelize.fn('COALESCE', sequelize.fn('SUM', sequelize.col('amount')), 0), 'total']
      ],
      raw: true
    });
    const totalRevenue = parseFloat(totalRevenueResult?.total || 0);

    // Get revenue this month
    const revenueThisMonthResult = await Sale.findOne({
      where: {
        company_id: companyId,
        sale_date: {
          [Op.gte]: currentMonth
        }
      },
      attributes: [
        [sequelize.fn('COALESCE', sequelize.fn('SUM', sequelize.col('amount')), 0), 'total']
      ],
      raw: true
    });
    const revenueThisMonth = parseFloat(revenueThisMonthResult?.total || 0);

    // Get pending followups
    const pendingFollowups = await Followup.count({
      where: {
        company_id: companyId,
        status: 'pending',
        followup_date: {
          [Op.lte]: currentDate
        }
      }
    });

    // Get team members count
    const teamMembersCount = await TeamMember.count({
      where: { company_id: companyId }
    });

    // Get active goals
    const activeGoals = await Goal.count({
      where: {
        company_id: companyId,
        start_date: {
          [Op.lte]: currentDate
        },
        end_date: {
          [Op.gte]: currentDate
        }
      }
    });

    // Calculate conversion rate (sales / leads * 100)
    const conversionRate = totalLeads > 0 ? ((totalSales / totalLeads) * 100).toFixed(2) : 0;

    const kpis = {
      total_leads: totalLeads,
      leads_this_month: leadsThisMonth,
      total_sales: totalSales,
      sales_this_month: salesThisMonth,
      total_revenue: totalRevenue,
      revenue_this_month: revenueThisMonth,
      pending_followups: pendingFollowups,
      team_members_count: teamMembersCount,
      active_goals: activeGoals,
      conversion_rate: parseFloat(conversionRate),
      period: {
        current_month: currentMonth.toISOString().split('T')[0],
        current_year: currentYear.toISOString().split('T')[0]
      }
    };

    res.json(kpis);
  } catch (error) {
    logger.error(`Error fetching dashboard KPIs for company ${req.companyId}: ${error.message}`);
    next(error);
  }
});

/**
 * GET /dashboard/recent-activity
 * Get recent activity for the dashboard
 */
router.get('/recent-activity', async (req, res, next) => {
  try {
    const companyId = req.companyId;
    const limit = parseInt(req.query.limit) || 10;

    // Get recent leads
    const recentLeads = await Lead.findAll({
      where: { company_id: companyId },
      order: [['created_at', 'DESC']],
      limit: Math.min(limit, 5),
      attributes: ['id', 'name', 'status', 'created_at']
    });

    // Get recent sales
    const recentSales = await Sale.findAll({
      where: { company_id: companyId },
      order: [['created_at', 'DESC']],
      limit: Math.min(limit, 5),
      attributes: ['id', 'amount', 'sale_date', 'created_at'],
      include: [{
        model: Lead,
        as: 'lead',
        attributes: ['id', 'name']
      }]
    });

    // Get upcoming followups
    const upcomingFollowups = await Followup.findAll({
      where: {
        company_id: companyId,
        status: 'pending',
        followup_date: {
          [Op.gte]: new Date()
        }
      },
      order: [['followup_date', 'ASC']],
      limit: Math.min(limit, 5),
      attributes: ['id', 'title', 'followup_date', 'status'],
      include: [{
        model: Lead,
        as: 'lead',
        attributes: ['id', 'name']
      }]
    });

    res.json({
      recent_leads: recentLeads,
      recent_sales: recentSales,
      upcoming_followups: upcomingFollowups
    });
  } catch (error) {
    logger.error(`Error fetching recent activity for company ${req.companyId}: ${error.message}`);
    next(error);
  }
});

module.exports = router;

const express = require('express');
const { authenticateAdmin, createAccessToken } = require('../utils/auth');
const { validateBody } = require('../middleware/validation');
const { loginSchema } = require('../validators/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * POST /auth/login
 * Authenticate admin user and return JWT token
 */
router.post('/login', validateBody(loginSchema), async (req, res, next) => {
  try {
    const { username, password } = req.body;

    // Authenticate admin
    const admin = await authenticateAdmin(username, password);
    if (!admin) {
      return res.status(401).json({
        error: 'Credenciais inválidas',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Create JWT token
    const tokenPayload = {
      sub: admin.id.toString(),
      company_id: admin.company_id,
      username: admin.username
    };

    const accessToken = createAccessToken(tokenPayload);

    // Log successful login
    logger.info(`Admin ${admin.username} logged in successfully from ${req.ip}`);

    // Return response
    res.json({
      access_token: accessToken,
      token_type: 'Bearer',
      company: {
        id: admin.company.id,
        name: admin.company.name,
        created_at: admin.company.created_at,
        updated_at: admin.company.updated_at
      },
      admin: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        company_id: admin.company_id,
        created_at: admin.created_at
      }
    });

  } catch (error) {
    logger.error(`Login error for username ${req.body?.username}: ${error.message}`);
    next(error);
  }
});

/**
 * POST /auth/logout
 * Logout endpoint (client-side token removal)
 */
router.post('/logout', (req, res) => {
  // In a stateless JWT system, logout is handled client-side
  // by removing the token from storage
  res.json({
    message: 'Logout realizado com sucesso'
  });
});

/**
 * GET /auth/me
 * Get current authenticated admin info
 */
router.get('/me', require('../middleware/auth').authenticateToken, (req, res) => {
  res.json({
    admin: {
      id: req.admin.id,
      username: req.admin.username,
      email: req.admin.email,
      company_id: req.admin.company_id,
      created_at: req.admin.created_at
    },
    company: {
      id: req.admin.company.id,
      name: req.admin.company.name,
      created_at: req.admin.company.created_at,
      updated_at: req.admin.company.updated_at
    }
  });
});

module.exports = router;

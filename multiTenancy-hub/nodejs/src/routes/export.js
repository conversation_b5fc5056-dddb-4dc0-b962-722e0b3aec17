const express = require('express');
const { Lead, Sale, Preorder } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const XLSX = require('xlsx');

const router = express.Router();
router.use(authenticateToken);

router.get('/leads', async (req, res, next) => {
  try {
    const leads = await Lead.findAll({
      where: { company_id: req.companyId },
      order: [['created_at', 'DESC']]
    });

    const worksheet = XLSX.utils.json_to_sheet(leads.map(lead => ({
      ID: lead.id,
      Nome: lead.name,
      Email: lead.email,
      Telefone: lead.phone,
      'Rede Social': lead.social_media,
      Fonte: lead.source,
      Status: lead.status,
      'Data de Criação': lead.created_at
    })));

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Leads');

    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=leads.xlsx');
    res.send(buffer);
  } catch (error) {
    next(error);
  }
});

router.get('/sales', async (req, res, next) => {
  try {
    const sales = await Sale.findAll({
      where: { company_id: req.companyId },
      order: [['sale_date', 'DESC']]
    });

    const worksheet = XLSX.utils.json_to_sheet(sales.map(sale => ({
      ID: sale.id,
      'ID Lead': sale.lead_id,
      'ID Produto': sale.product_id,
      Valor: sale.amount,
      'Data da Venda': sale.sale_date,
      Observações: sale.notes,
      'Data de Criação': sale.created_at
    })));

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Vendas');

    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=vendas.xlsx');
    res.send(buffer);
  } catch (error) {
    next(error);
  }
});

module.exports = router;

const express = require('express');
const { Goal } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

router.get('/', async (req, res, next) => {
  try {
    const goals = await Goal.findAll({
      where: { company_id: req.companyId },
      order: [['created_at', 'DESC']]
    });
    res.json(goals);
  } catch (error) {
    next(error);
  }
});

router.post('/', async (req, res, next) => {
  try {
    const goal = await Goal.create({
      ...req.body,
      company_id: req.companyId
    });
    res.status(201).json(goal);
  } catch (error) {
    next(error);
  }
});

module.exports = router;

const express = require('express');
const { Followup, Lead } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validateBody, validateParams } = require('../middleware/validation');
const { createFollowupSchema, updateFollowupSchema, followupIdSchema } = require('../validators/followups');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * GET /followups
 * Get all followups for the authenticated admin's company
 */
router.get('/', async (req, res, next) => {
  try {
    const followups = await Followup.findAll({
      where: { company_id: req.companyId },
      include: [{
        model: Lead,
        as: 'lead',
        attributes: ['id', 'name', 'email', 'phone']
      }],
      order: [['followup_date', 'ASC']]
    });

    res.json(followups);
  } catch (error) {
    logger.error(`Error fetching followups for company ${req.companyId}: ${error.message}`);
    next(error);
  }
});

/**
 * POST /followups
 * Create a new followup
 */
router.post('/', validateBody(createFollowupSchema), async (req, res, next) => {
  try {
    // Verify that the lead belongs to the same company
    const lead = await Lead.findOne({
      where: {
        id: req.body.lead_id,
        company_id: req.companyId
      }
    });

    if (!lead) {
      return res.status(404).json({
        error: 'Lead não encontrado',
        code: 'LEAD_NOT_FOUND'
      });
    }

    const followupData = {
      ...req.body,
      company_id: req.companyId
    };

    const followup = await Followup.create(followupData);

    // Fetch the created followup with lead data
    const createdFollowup = await Followup.findByPk(followup.id, {
      include: [{
        model: Lead,
        as: 'lead',
        attributes: ['id', 'name', 'email', 'phone']
      }]
    });

    logger.info(`Followup created: ${followup.id} by admin ${req.admin.username}`);
    res.status(201).json(createdFollowup);
  } catch (error) {
    logger.error(`Error creating followup for company ${req.companyId}: ${error.message}`);
    next(error);
  }
});

/**
 * GET /followups/:followup_id
 * Get a specific followup by ID
 */
router.get('/:followup_id', validateParams(followupIdSchema), async (req, res, next) => {
  try {
    const { followup_id } = req.params;

    const followup = await Followup.findOne({
      where: {
        id: followup_id,
        company_id: req.companyId
      },
      include: [{
        model: Lead,
        as: 'lead',
        attributes: ['id', 'name', 'email', 'phone']
      }]
    });

    if (!followup) {
      return res.status(404).json({
        error: 'Follow-up não encontrado',
        code: 'FOLLOWUP_NOT_FOUND'
      });
    }

    res.json(followup);
  } catch (error) {
    logger.error(`Error fetching followup ${req.params.followup_id}: ${error.message}`);
    next(error);
  }
});

/**
 * PUT /followups/:followup_id
 * Update a specific followup
 */
router.put('/:followup_id', 
  validateParams(followupIdSchema), 
  validateBody(updateFollowupSchema), 
  async (req, res, next) => {
    try {
      const { followup_id } = req.params;

      const followup = await Followup.findOne({
        where: {
          id: followup_id,
          company_id: req.companyId
        }
      });

      if (!followup) {
        return res.status(404).json({
          error: 'Follow-up não encontrado',
          code: 'FOLLOWUP_NOT_FOUND'
        });
      }

      // If lead_id is being updated, verify it belongs to the same company
      if (req.body.lead_id && req.body.lead_id !== followup.lead_id) {
        const lead = await Lead.findOne({
          where: {
            id: req.body.lead_id,
            company_id: req.companyId
          }
        });

        if (!lead) {
          return res.status(404).json({
            error: 'Lead não encontrado',
            code: 'LEAD_NOT_FOUND'
          });
        }
      }

      // Update followup with provided data
      await followup.update(req.body);

      // Fetch updated followup with lead data
      const updatedFollowup = await Followup.findByPk(followup.id, {
        include: [{
          model: Lead,
          as: 'lead',
          attributes: ['id', 'name', 'email', 'phone']
        }]
      });

      logger.info(`Followup updated: ${followup.id} by admin ${req.admin.username}`);
      res.json(updatedFollowup);
    } catch (error) {
      logger.error(`Error updating followup ${req.params.followup_id}: ${error.message}`);
      next(error);
    }
  }
);

/**
 * DELETE /followups/:followup_id
 * Delete a specific followup
 */
router.delete('/:followup_id', validateParams(followupIdSchema), async (req, res, next) => {
  try {
    const { followup_id } = req.params;

    const followup = await Followup.findOne({
      where: {
        id: followup_id,
        company_id: req.companyId
      }
    });

    if (!followup) {
      return res.status(404).json({
        error: 'Follow-up não encontrado',
        code: 'FOLLOWUP_NOT_FOUND'
      });
    }

    await followup.destroy();

    logger.info(`Followup deleted: ${followup_id} by admin ${req.admin.username}`);
    res.json({
      message: 'Follow-up deletado com sucesso'
    });
  } catch (error) {
    logger.error(`Error deleting followup ${req.params.followup_id}: ${error.message}`);
    next(error);
  }
});

module.exports = router;

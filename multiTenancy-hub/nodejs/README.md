# Multi-Tenancy Hub - Node.js Backend

Backend API em Node.js com Express para sistema multi-tenant de dashboard empresarial.

## 🚀 Características

- **Framework**: Express.js
- **Banco de Dados**: PostgreSQL com Sequelize ORM
- **Autenticação**: JWT (JSON Web Tokens)
- **Validação**: Joi
- **Logging**: Winston
- **Containerização**: Docker & Docker Compose
- **Multi-tenancy**: Isolamento por empresa
- **Segurança**: Helmet, CORS, Rate Limiting

## 📋 Pré-requisitos

- Node.js 18+ 
- PostgreSQL 13+
- Docker & Docker Compose (opcional)

## 🛠️ Instalação

### Desenvolvimento Local

1. Clone o repositório:
```bash
git clone <repository-url>
cd nodejs
```

2. Instale as dependências:
```bash
npm install
```

3. Configure as variáveis de ambiente:
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

4. Configure o banco de dados PostgreSQL e execute as migrações:
```bash
# Certifique-se de que o PostgreSQL está rodando
npm run seed  # Popula o banco com dados iniciais
```

5. Inicie o servidor de desenvolvimento:
```bash
npm run dev
```

### Docker

1. Inicie com Docker Compose:
```bash
# Desenvolvimento
docker-compose -f docker-compose.dev.yml up -d

# Produção
docker-compose up -d
```

2. Execute o seed do banco de dados:
```bash
docker-compose exec api npm run seed
```

## 📚 API Endpoints

### Autenticação
- `POST /auth/login` - Login do administrador
- `POST /auth/logout` - Logout
- `GET /auth/me` - Informações do usuário atual

### Dashboard
- `GET /dashboard/kpis` - KPIs do dashboard
- `GET /dashboard/recent-activity` - Atividade recente

### Leads
- `GET /leads` - Listar leads
- `POST /leads` - Criar lead
- `GET /leads/:id` - Obter lead específico
- `PUT /leads/:id` - Atualizar lead
- `DELETE /leads/:id` - Deletar lead

### Follow-ups
- `GET /followups` - Listar follow-ups
- `POST /followups` - Criar follow-up
- `GET /followups/:id` - Obter follow-up específico
- `PUT /followups/:id` - Atualizar follow-up
- `DELETE /followups/:id` - Deletar follow-up

### Outros Endpoints
- `GET /products` - Produtos
- `GET /team` - Equipe
- `GET /sales` - Vendas
- `GET /goals` - Metas
- `GET /preorders` - Pré-vendas
- `GET /export/leads` - Exportar leads
- `GET /export/sales` - Exportar vendas

## 🧪 Testes

```bash
# Executar testes
npm test

# Executar testes em modo watch
npm run test:watch

# Testar autenticação
npm run test-auth
```

## 🔧 Scripts Disponíveis

- `npm start` - Inicia o servidor de produção
- `npm run dev` - Inicia o servidor de desenvolvimento com nodemon
- `npm run seed` - Popula o banco de dados com dados iniciais
- `npm run test-auth` - Testa o sistema de autenticação
- `npm run lint` - Executa o ESLint
- `npm run lint:fix` - Corrige problemas do ESLint automaticamente
- `npm run format` - Formata o código com Prettier

## 🏗️ Estrutura do Projeto

```
nodejs/
├── src/
│   ├── config/          # Configurações (banco de dados, etc.)
│   ├── middleware/      # Middlewares (auth, validation, error handling)
│   ├── models/          # Modelos do Sequelize
│   ├── routes/          # Rotas da API
│   ├── scripts/         # Scripts utilitários
│   ├── utils/           # Utilitários (auth, logger)
│   ├── validators/      # Schemas de validação Joi
│   └── server.js        # Arquivo principal do servidor
├── logs/                # Arquivos de log
├── docker-compose.yml   # Configuração Docker para produção
├── docker-compose.dev.yml # Configuração Docker para desenvolvimento
├── Dockerfile           # Dockerfile para produção
├── Dockerfile.dev       # Dockerfile para desenvolvimento
└── package.json         # Dependências e scripts
```

## 🔐 Credenciais Padrão

Após executar o seed do banco de dados:

- **admin_techcorp** (TechCorp Solutions) - senha: `admin123`
- **admin_marketing** (Digital Marketing Pro) - senha: `admin123`
- **admin_ecommerce** (E-commerce Plus) - senha: `admin123`

## 🌍 Variáveis de Ambiente

Consulte o arquivo `.env.example` para todas as variáveis de ambiente disponíveis.

## 📝 Logs

Os logs são salvos na pasta `logs/`:
- `error.log` - Apenas erros
- `combined.log` - Todos os logs

## 🚀 Deploy

### Docker
```bash
docker-compose up -d
```

### Manual
```bash
npm install --production
npm start
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🆘 Suporte

Para suporte, envie um <NAME_EMAIL> ou abra uma issue no GitHub.

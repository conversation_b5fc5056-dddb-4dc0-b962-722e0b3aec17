from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from database import get_db
from models import Admin, Sale, Lead, Goal, Followup, TeamMember
from schemas import DashboardKPIs
from auth import get_current_admin
from datetime import datetime, date

router = APIRouter()

@router.get("/kpis", response_model=DashboardKPIs)
async def get_dashboard_kpis(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    company_id = current_admin.company_id
    
    # Receita total
    total_revenue = db.query(func.sum(Sale.amount)).filter(
        Sale.company_id == company_id
    ).scalar() or 0
    
    # Total de leads
    total_leads = db.query(func.count(Lead.id)).filter(
        Lead.company_id == company_id
    ).scalar() or 0
    
    # Vendas do mês atual
    current_month = date.today().replace(day=1)
    monthly_sales = db.query(func.sum(Sale.amount)).filter(
        and_(
            Sale.company_id == company_id,
            Sale.sale_date >= current_month
        )
    ).scalar() or 0
    
    # Taxa de conversão (leads que viraram vendas)
    leads_with_sales = db.query(func.count(func.distinct(Sale.lead_id))).filter(
        and_(
            Sale.company_id == company_id,
            Sale.lead_id.isnot(None)
        )
    ).scalar() or 0
    
    conversion_rate = (leads_with_sales / total_leads * 100) if total_leads > 0 else 0
    
    # Metas atingidas
    total_goals = db.query(func.count(Goal.id)).filter(
        Goal.company_id == company_id
    ).scalar() or 0
    
    goals_achieved = db.query(func.count(Goal.id)).filter(
        and_(
            Goal.company_id == company_id,
            Goal.current_value >= Goal.target_value
        )
    ).scalar() or 0
    
    # Follow-ups pendentes
    pending_followups = db.query(func.count(Followup.id)).filter(
        and_(
            Followup.company_id == company_id,
            Followup.status == "pending"
        )
    ).scalar() or 0
    
    # Total de membros da equipe
    total_team_members = db.query(func.count(TeamMember.id)).filter(
        TeamMember.company_id == company_id
    ).scalar() or 0
    
    return DashboardKPIs(
        total_revenue=total_revenue,
        total_leads=total_leads,
        conversion_rate=round(conversion_rate, 2),
        monthly_sales=monthly_sales,
        goals_achieved=goals_achieved,
        total_goals=total_goals,
        pending_followups=pending_followups,
        total_team_members=total_team_members
    )
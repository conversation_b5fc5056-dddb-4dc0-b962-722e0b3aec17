import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MessageInfo {
  @ApiProperty({ description: 'ID único da mensagem' })
  id: string;

  @ApiProperty({ description: 'Remetente da mensagem' })
  from: string;

  @ApiProperty({ description: 'Destinatário da mensagem' })
  to: string;

  @ApiProperty({ description: 'Conteúdo da mensagem' })
  message: string;

  @ApiProperty({ description: 'Timestamp da mensagem' })
  timestamp: number;

  @ApiProperty({ description: 'Indica se a mensagem foi enviada por mim' })
  fromMe: boolean;

  @ApiProperty({ description: 'Tipo da mensagem' })
  messageType: string;
}

export class ChatHistory {
  @ApiProperty({ description: 'ID do chat' })
  id: string;

  @ApiPropertyOptional({ description: 'Nome do contato/grupo' })
  name?: string;

  @ApiProperty({ 
    description: 'Lista de mensagens do chat',
    type: [MessageInfo]
  })
  messages: MessageInfo[];
}

export class SendMessageResponse {
  @ApiProperty({ description: 'Indica se a operação foi bem-sucedida' })
  success: boolean;

  @ApiProperty({ description: 'Dados da mensagem enviada' })
  data: {
    success: boolean;
    messageId: string;
    to: string;
    message: string;
  };
}

export class ChatHistoryResponse {
  @ApiProperty({ description: 'Indica se a operação foi bem-sucedida' })
  success: boolean;

  @ApiProperty({ 
    description: 'Histórico do chat',
    type: ChatHistory
  })
  data: ChatHistory;
}

export class AllChatsResponse {
  @ApiProperty({ description: 'Indica se a operação foi bem-sucedida' })
  success: boolean;

  @ApiProperty({ 
    description: 'Lista de todos os chats',
    type: [ChatHistory]
  })
  data: ChatHistory[];
}

export class DisconnectResponse {
  @ApiProperty({ description: 'Indica se a operação foi bem-sucedida' })
  success: boolean;

  @ApiProperty({ description: 'Mensagem de confirmação' })
  message: string;
}

export class ErrorResponse {
  @ApiProperty({ description: 'Indica que houve erro' })
  success: boolean;

  @ApiProperty({ description: 'Mensagem de erro' })
  message: string;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class QRCodeResponse {
  @ApiProperty({ description: 'Indica se a operação foi bem-sucedida' })
  success: boolean;

  @ApiProperty({ description: 'Indica se há QR Code disponível' })
  available: boolean;

  @ApiPropertyOptional({ 
    description: 'QR Code como string (formato raw)',
    example: '2@/JXKmwF6AUaRzQisO4jxpmmBm4SNHoZlBbHcsccbdKf1Sr10OzxD4BuVk7V17Rg4+IbW7FQi4HxB55CRVxM/om8oOIuTAcmfb4o=,c+Rc/NVDEclmpOn2E17RxNg89LhICEBXAxoY/EMnkDo=,6fLoGzj//BCCsZfRcnaWQBp0h5AD8Ensm2MHTU7JZBg=,SaE4o+SrY8Jhea3hYmt4kvbnZnNOreqsJnNtWVXqa4M='
  })
  qrCode?: string;

  @ApiPropertyOptional({ 
    description: 'QR Code como imagem base64 (PNG)',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAALF0lEQVR4Xu2d...'
  })
  qrCodeImage?: string;

  @ApiPropertyOptional({ description: 'Mensagem explicativa' })
  message?: string;

  @ApiProperty({ description: 'Status da conexão atual' })
  connectionStatus: {
    connected: boolean;
    state: string;
  };
}

export class QRCodeImageOptions {
  @ApiPropertyOptional({ 
    description: 'Formato da imagem',
    enum: ['png', 'svg'],
    default: 'png'
  })
  format?: 'png' | 'svg' = 'png';

  @ApiPropertyOptional({ 
    description: 'Tamanho da imagem em pixels',
    default: 256,
    minimum: 100,
    maximum: 1000
  })
  size?: number = 256;

  @ApiPropertyOptional({ 
    description: 'Incluir dados base64 no response',
    default: true
  })
  includeImage?: boolean = true;
}

import { Boom } from '@hapi/boom';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import makeWASocket, {
  ConnectionState,
  DisconnectReason,
  fetchLatestBaileysVersion,
  useMultiFileAuthState,
  WAMessage,
  WASocket,
} from '@whiskeysockets/baileys';
import * as fs from 'fs';
import * as path from 'path';
import * as QRCode from 'qrcode';
import * as qrcode from 'qrcode-terminal';
import { MessageStoreService } from './message-store.service';

export interface SendMessageDto {
  to: string;
  message: string;
  type?: 'text' | 'image' | 'document';
}

export interface ChatHistory {
  id: string;
  name?: string;
  messages: MessageInfo[];
}

export interface MessageInfo {
  id: string;
  from: string;
  to: string;
  message: string;
  timestamp: number;
  fromMe: boolean;
  messageType: string;
}

@Injectable()
export class WhatsappService implements OnModuleInit {
  private readonly logger = new Logger(WhatsappService.name);
  private sock: WASocket;
  private isConnected = false;
  private qrCode: string | null = null;
  private connectionState: string = 'close';

  constructor(private readonly messageStore: MessageStoreService) {}

  async onModuleInit() {
    await this.initializeWhatsApp();
  }

  private async initializeWhatsApp() {
    try {
      const authDir = path.join(process.cwd(), 'auth_info_baileys');

      // Ensure auth directory exists
      if (!fs.existsSync(authDir)) {
        fs.mkdirSync(authDir, { recursive: true });
      }

      const { state, saveCreds } = await useMultiFileAuthState(authDir);
      const { version, isLatest } = await fetchLatestBaileysVersion();

      this.logger.log(`Using WA v${version.join('.')}, isLatest: ${isLatest}`);

      this.sock = makeWASocket({
        version,
        auth: state,
        printQRInTerminal: false,
        generateHighQualityLinkPreview: true,
      });

      this.sock.ev.on('connection.update', (update) => {
        this.handleConnectionUpdate(update);
      });

      this.sock.ev.on('creds.update', saveCreds);

      this.sock.ev.on('messages.upsert', (m) => {
        this.handleIncomingMessages(m);
      });
    } catch (error) {
      this.logger.error('Failed to initialize WhatsApp:', error);
    }
  }

  private handleConnectionUpdate(update: Partial<ConnectionState>) {
    const { connection, lastDisconnect, qr } = update;

    if (qr) {
      this.qrCode = qr;
      this.logger.log('QR Code generated, scan it to connect');
      qrcode.generate(qr, { small: true });
    }

    if (connection === 'close') {
      this.isConnected = false;
      const shouldReconnect =
        (lastDisconnect?.error as Boom)?.output?.statusCode !==
        DisconnectReason.loggedOut;

      this.logger.log('Connection closed due to:', lastDisconnect?.error);

      if (shouldReconnect) {
        this.logger.log('Reconnecting...');
        setTimeout(() => this.initializeWhatsApp(), 3000);
      }
    } else if (connection === 'open') {
      this.isConnected = true;
      this.qrCode = null;
      this.logger.log('WhatsApp connection opened successfully');
    }

    this.connectionState = connection || 'close';
  }

  private handleIncomingMessages(m: any) {
    const messages = m.messages;
    for (const message of messages) {
      if (message.message) {
        const messageInfo: MessageInfo = {
          id: message.key.id || 'unknown',
          from: message.key.remoteJid || 'unknown',
          to: message.key.remoteJid || 'unknown',
          message: this.extractMessageText(message),
          timestamp: message.messageTimestamp || Date.now(),
          fromMe: message.key.fromMe || false,
          messageType: Object.keys(message.message || {})[0] || 'unknown',
        };

        // Store the message
        this.messageStore.addMessage(message.key.remoteJid, messageInfo);

        this.logger.log('Message processed:', {
          from: message.key.remoteJid,
          fromMe: message.key.fromMe,
          message: messageInfo.message,
        });
      }
    }
  }

  async sendMessage(data: SendMessageDto): Promise<any> {
    if (!this.isConnected) {
      throw new Error('WhatsApp is not connected');
    }

    try {
      const jid = this.formatPhoneNumber(data.to);

      const result = await this.sock.sendMessage(jid, {
        text: data.message,
      });

      this.logger.log(`Message sent to ${jid}: ${data.message}`);
      return {
        success: true,
        messageId: result?.key?.id || 'unknown',
        to: jid,
        message: data.message,
      };
    } catch (error) {
      this.logger.error('Failed to send message:', error);
      throw new Error(`Failed to send message: ${error.message}`);
    }
  }

  async getChatHistory(
    chatId: string,
    limit: number = 50,
  ): Promise<ChatHistory> {
    if (!this.isConnected) {
      throw new Error('WhatsApp is not connected');
    }

    try {
      const jid = this.formatPhoneNumber(chatId);

      // Get messages from the message store
      const messageInfos = this.messageStore.getMessages(jid, limit);

      return {
        id: jid,
        name: this.getChatName(jid),
        messages: messageInfos,
      };
    } catch (error: any) {
      this.logger.error('Failed to get chat history:', error);
      throw new Error(`Failed to get chat history: ${error.message}`);
    }
  }

  async getAllChats(): Promise<ChatHistory[]> {
    if (!this.isConnected) {
      throw new Error('WhatsApp is not connected');
    }

    try {
      const chatIds = this.messageStore.getAllChats();
      const chats: ChatHistory[] = [];

      for (const chatId of chatIds) {
        const messages = this.messageStore.getMessages(chatId, 1); // Get last message
        chats.push({
          id: chatId,
          name: this.getChatName(chatId),
          messages: messages,
        });
      }

      return chats;
    } catch (error: any) {
      this.logger.error('Failed to get all chats:', error);
      throw new Error(`Failed to get all chats: ${error.message}`);
    }
  }

  private formatPhoneNumber(phone: string): string {
    // Remove all non-numeric characters
    let cleaned = phone.replace(/\D/g, '');

    // Add country code if not present (assuming Brazil +55)
    if (!cleaned.startsWith('55') && cleaned.length === 11) {
      cleaned = '55' + cleaned;
    }

    return cleaned + '@s.whatsapp.net';
  }

  private extractMessageText(message: WAMessage): string {
    if (message.message?.conversation) {
      return message.message.conversation;
    }
    if (message.message?.extendedTextMessage?.text) {
      return message.message.extendedTextMessage.text;
    }
    if (message.message?.imageMessage?.caption) {
      return message.message.imageMessage.caption;
    }
    if (message.message?.videoMessage?.caption) {
      return message.message.videoMessage.caption;
    }
    return '[Media or unsupported message]';
  }

  private getChatName(jid: string): string {
    try {
      // Try to get contact name or group name
      return jid.split('@')[0];
    } catch {
      return jid.split('@')[0];
    }
  }

  getConnectionStatus(): {
    connected: boolean;
    qrCode?: string;
    state: string;
  } {
    return {
      connected: this.isConnected,
      qrCode: this.qrCode || undefined,
      state: this.connectionState,
    };
  }

  async getQRCode(options?: {
    format?: 'png' | 'svg';
    size?: number;
    includeImage?: boolean;
  }) {
    const defaultOptions = {
      format: 'png' as const,
      size: 256,
      includeImage: true,
      ...options,
    };

    const response = {
      success: true,
      available: !!this.qrCode,
      qrCode: this.qrCode || undefined,
      qrCodeImage: undefined as string | undefined,
      message: '',
      connectionStatus: {
        connected: this.isConnected,
        state: this.connectionState,
      },
    };

    if (!this.qrCode) {
      response.message = this.isConnected
        ? 'WhatsApp já está conectado. QR Code não é necessário.'
        : 'QR Code não disponível. Aguarde a inicialização ou reconexão.';
      return response;
    }

    if (defaultOptions.includeImage) {
      try {
        if (defaultOptions.format === 'png') {
          const qrCodeDataURL = await QRCode.toDataURL(this.qrCode, {
            width: defaultOptions.size,
            margin: 2,
            color: {
              dark: '#000000',
              light: '#FFFFFF',
            },
          });
          response.qrCodeImage = qrCodeDataURL;
        } else if (defaultOptions.format === 'svg') {
          const qrCodeSVG = await QRCode.toString(this.qrCode, {
            type: 'svg',
            width: defaultOptions.size,
            margin: 2,
          });
          response.qrCodeImage = `data:image/svg+xml;base64,${Buffer.from(qrCodeSVG).toString('base64')}`;
        }
        response.message =
          'QR Code gerado com sucesso. Escaneie com seu WhatsApp.';
      } catch (error: any) {
        this.logger.error('Failed to generate QR Code image:', error);
        response.message = 'QR Code disponível, mas falha ao gerar imagem.';
      }
    } else {
      response.message = 'QR Code disponível (apenas string).';
    }

    return response;
  }

  async disconnect(): Promise<void> {
    if (this.sock) {
      await this.sock.logout();
      this.isConnected = false;
      this.logger.log('WhatsApp disconnected');
    }
  }
}

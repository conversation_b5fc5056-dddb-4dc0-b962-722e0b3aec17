import { Injectable } from '@nestjs/common';
import { MessageInfo } from './whatsapp.service';

@Injectable()
export class MessageStoreService {
  private messages: Map<string, MessageInfo[]> = new Map();

  addMessage(chatId: string, message: MessageInfo): void {
    if (!this.messages.has(chatId)) {
      this.messages.set(chatId, []);
    }
    
    const chatMessages = this.messages.get(chatId)!;
    chatMessages.push(message);
    
    // Keep only the last 1000 messages per chat to avoid memory issues
    if (chatMessages.length > 1000) {
      chatMessages.splice(0, chatMessages.length - 1000);
    }
  }

  getMessages(chatId: string, limit: number = 50): MessageInfo[] {
    const messages = this.messages.get(chatId) || [];
    return messages.slice(-limit).reverse(); // Get last N messages, most recent first
  }

  getAllChats(): string[] {
    return Array.from(this.messages.keys());
  }

  getChatCount(): number {
    return this.messages.size;
  }

  getMessageCount(chatId: string): number {
    return this.messages.get(chatId)?.length || 0;
  }

  clearChat(chatId: string): void {
    this.messages.delete(chatId);
  }

  clearAllChats(): void {
    this.messages.clear();
  }
}

import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configuração do Swagger
  const config = new DocumentBuilder()
    .setTitle('WhatsApp Integration API')
    .setDescription('API para integração com WhatsApp usando Baileys')
    .setVersion('1.0')
    .addTag('whatsapp', 'Endpoints relacionados ao WhatsApp')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(process.env.PORT ?? 3000);
  console.log(`🚀 Servidor rodando em: http://localhost:${process.env.PORT ?? 3000}`);
  console.log(`📚 Swagger disponível em: http://localhost:${process.env.PORT ?? 3000}/api`);
}
bootstrap();

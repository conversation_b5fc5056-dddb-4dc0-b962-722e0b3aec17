# 📚 G<PERSON><PERSON> do Swagger - WhatsApp Integration API

O Swagger UI está disponível para testar todos os endpoints da API de forma interativa.

## 🌐 Acessando o Swagger

Após iniciar o servidor, acesse:
```
http://localhost:3000/api
```

## 🚀 Como Usar o Swagger

### 1. **Verificar Status da Conexão**
- **Endpoint**: `GET /whatsapp/status`
- **Descrição**: Verifica se o WhatsApp está conectado
- **Como testar**:
  1. Clique em "GET /whatsapp/status"
  2. Clique em "Try it out"
  3. Clique em "Execute"

### 2. **Enviar Mensagem**
- **Endpoint**: `POST /whatsapp/send-message`
- **Descrição**: Envia uma mensagem de texto
- **Como testar**:
  1. Clique em "POST /whatsapp/send-message"
  2. Clique em "Try it out"
  3. Edite o JSON de exemplo:
     ```json
     {
       "to": "5511999999999",
       "message": "Ol<PERSON>! Esta é uma mensagem de teste.",
       "type": "text"
     }
     ```
  4. Clique em "Execute"

### 3. **Buscar Histórico de Conversa**
- **Endpoint**: `GET /whatsapp/chat/{chatId}/history`
- **Descrição**: Retorna o histórico de mensagens
- **Como testar**:
  1. Clique em "GET /whatsapp/chat/{chatId}/history"
  2. Clique em "Try it out"
  3. Preencha o `chatId` (ex: 5511999999999)
  4. Opcionalmente, defina o `limit` (ex: 20)
  5. Clique em "Execute"

### 4. **Listar Todas as Conversas**
- **Endpoint**: `GET /whatsapp/chats`
- **Descrição**: Lista todas as conversas ativas
- **Como testar**:
  1. Clique em "GET /whatsapp/chats"
  2. Clique em "Try it out"
  3. Clique em "Execute"

### 5. **Desconectar do WhatsApp**
- **Endpoint**: `POST /whatsapp/disconnect`
- **Descrição**: Desconecta a sessão atual
- **Como testar**:
  1. Clique em "POST /whatsapp/disconnect"
  2. Clique em "Try it out"
  3. Clique em "Execute"

## 📋 Pré-requisitos para Testes

### Antes de Testar:
1. **Conectar ao WhatsApp**:
   - Inicie o servidor: `npm run start:dev`
   - Escaneie o QR Code exibido no terminal
   - Aguarde a confirmação da conexão

2. **Verificar Conexão**:
   - Use o endpoint `GET /whatsapp/status`
   - Certifique-se de que `connected: true`

### Para Enviar Mensagens:
- Use números no formato internacional (ex: 5511999999999)
- Certifique-se de que o número existe no WhatsApp
- O WhatsApp deve estar conectado

## 🎯 Exemplos de Uso

### Exemplo 1: Fluxo Completo
1. **Verificar Status** → `GET /whatsapp/status`
2. **Enviar Mensagem** → `POST /whatsapp/send-message`
3. **Buscar Histórico** → `GET /whatsapp/chat/{chatId}/history`

### Exemplo 2: Monitoramento
1. **Listar Conversas** → `GET /whatsapp/chats`
2. **Verificar Status** → `GET /whatsapp/status`

## 🔧 Códigos de Resposta

### Sucesso (200)
```json
{
  "success": true,
  "data": { ... }
}
```

### Erro (400)
```json
{
  "success": false,
  "message": "Descrição do erro"
}
```

## 📱 Formatos de Número

### ✅ Correto:
- `5511999999999` (Brasil + DDD + número)
- `1234567890` (outros países)

### ❌ Incorreto:
- `+55 (11) 99999-9999`
- `11999999999` (sem código do país)
- `(11) 99999-9999`

## 🛠️ Recursos do Swagger

### Funcionalidades Disponíveis:
- **Try it out**: Testar endpoints diretamente
- **Schemas**: Ver estrutura dos dados
- **Examples**: Exemplos de requisições
- **Responses**: Tipos de resposta possíveis
- **Download**: Baixar especificação OpenAPI

### Navegação:
- **Tags**: Endpoints agrupados por funcionalidade
- **Models**: Estruturas de dados utilizadas
- **Authorize**: Autenticação (não necessária nesta API)

## 🚨 Dicas Importantes

1. **Conexão Obrigatória**: Sempre verifique se está conectado antes de enviar mensagens
2. **Rate Limiting**: Evite enviar muitas mensagens rapidamente
3. **Números Válidos**: Use apenas números que existem no WhatsApp
4. **Logs**: Monitore os logs do servidor para debug
5. **QR Code**: Reescaneie se a conexão cair

## 🔄 Troubleshooting

### Problema: "WhatsApp is not connected"
**Solução**: 
1. Verifique o terminal do servidor
2. Escaneie o QR Code se disponível
3. Aguarde a conexão ser estabelecida

### Problema: "Failed to send message"
**Solução**:
1. Verifique se o número está correto
2. Confirme que o WhatsApp está conectado
3. Teste com um número conhecido

### Problema: Swagger não carrega
**Solução**:
1. Verifique se o servidor está rodando
2. Acesse: http://localhost:3000/api
3. Limpe o cache do navegador

## 📖 Documentação Adicional

- **API Guide**: [WHATSAPP_API_GUIDE.md](./WHATSAPP_API_GUIDE.md)
- **README**: [README.md](./README.md)
- **Testes HTTP**: [test-api.http](./test-api.http)
- **Exemplo JS**: [example-usage.js](./example-usage.js)

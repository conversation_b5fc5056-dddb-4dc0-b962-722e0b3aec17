### WhatsApp API Test File
### Use this file with REST Client extension in VS Code or similar tools

### 1. Check connection status
GET http://localhost:3000/whatsapp/status
Content-Type: application/json

###

### 2. Get QR Code for connection
GET http://localhost:3000/whatsapp/qrcode
Content-Type: application/json

###

### 2.1. Get QR Code as SVG with custom size
GET http://localhost:3000/whatsapp/qrcode?format=svg&size=200
Content-Type: application/json

###

### 2.2. Get QR Code without image (string only)
GET http://localhost:3000/whatsapp/qrcode?includeImage=false
Content-Type: application/json

###

### 3. Send a text message
POST http://localhost:3000/whatsapp/send-message
Content-Type: application/json

{
  "to": "5511999999999",
  "message": "Olá! Esta é uma mensagem de teste da API.",
  "type": "text"
}

###

### 4. Get chat history
GET http://localhost:3000/whatsapp/chat/5511999999999/history?limit=20
Content-Type: application/json

###

### 5. Get all chats
GET http://localhost:3000/whatsapp/chats
Content-Type: application/json

###

### 6. Disconnect from WhatsApp
POST http://localhost:3000/whatsapp/disconnect
Content-Type: application/json

###

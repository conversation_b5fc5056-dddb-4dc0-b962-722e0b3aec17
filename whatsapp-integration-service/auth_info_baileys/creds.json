{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "8K77vOQw7nb9PrkE375Li/HdVizshzN4gjuc/c4Qi24="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "Dp8zo9gAJ/UZ6I1XqrZgxfj6L3OLr8troGiFRDX/VHI="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "IDsPqtXf7yVPOPTDOnggmdJbadIWBZL5W2lYKu5EqnM="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "qqghuW7wQ36fksG77O/O2XjXxGOihvd/akz0XtQ3bCk="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "EAj6YoQObLc+SMaDyHLrWT/opg5hEsPbaUzXXs3tHEw="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "QNSesDvFp+nuDWJb/UE5Rc59e0XJ01UdFgTcsaEYyQM="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "+Giw8uwDRbDfQw/jyo4CNgdOmhAjWTioKevx8JgEEUM="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "9k3c14lKGTRe/Mke6YyR9o7n8pz6128gqa6/avBfyC0="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "IDcSG+QNydW6UlWRnw256dTZe2GIFV50Rp8ZemjiBXI+Wii+BTo1UvIHNipBYguNeypw5Tbw9bYrWy4qAKSiAA=="}, "keyId": 1}, "registrationId": 91, "advSecretKey": "qvvEPfQug4YsnZQyHUwfUsuLe4kafy6fUWiCBe68Vt4=", "processedHistoryMessages": [], "nextPreKeyId": 31, "firstUnuploadedPreKeyId": 31, "accountSyncCounter": 0, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CNvw6rcBEPWH4sUGGAUgACgA", "accountSignatureKey": "2Rh1wDv7PtoncEiO+Nh9i9y/lqku0LOduobr7W5exz0=", "accountSignature": "+G8pXtfU+KhrnjL/tcRg/uq3gNeQzJDI40bHADE3AOAax3L+R1fPQwzRSP7IvQTiSkZ/Mb6SigH9tu7Ggn4cDg==", "deviceSignature": "HAPRVZq0SvEuWQMnKh9SllN8ZjrLAlkmbWTPPx9a43UnnwWq/y7CWjy8hNmX8bnRIktBDzXu9qEfLtNRnC1TCQ=="}, "me": {"id": "************:<EMAIL>", "lid": "***************:10@lid"}, "signalIdentities": [{"identifier": {"name": "************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BdkYdcA7+z7aJ3BIjvjYfYvcv5apLtCznbqG6+1uXsc9"}}], "platform": "iphone", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CBIICA=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "3R9Z39", "myAppStateKeyId": "AAAAALZo"}
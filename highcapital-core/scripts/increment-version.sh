#!/bin/bash

# Script para incrementar versão no Ubuntu/Linux
# Equivalente ao increment-version.ps1 para uso no GitHub Actions

set -e

# Default values
VERSION_TYPE="patch"
PROJECT_PATH="src/Core/Core.csproj"
DRY_RUN=false
CREATE_TAG=false

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --version-type)
            VERSION_TYPE="$2"
            shift 2
            ;;
        --project-path)
            PROJECT_PATH="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --create-tag)
            CREATE_TAG=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --version-type TYPE    Version increment type (patch|minor|major|auto)"
            echo "  --project-path PATH    Path to project file (default: src/Core/Core.csproj)"
            echo "  --dry-run             Show what would be done without making changes"
            echo "  --create-tag          Create Git tag after version increment"
            echo "  -h, --help            Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Validate version type
case $VERSION_TYPE in
    patch|minor|major|auto)
        ;;
    *)
        echo "Error: Invalid version type '$VERSION_TYPE'. Must be one of: patch, minor, major, auto"
        exit 1
        ;;
esac

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    case $color in
        red)    echo -e "\033[31m$message\033[0m" ;;
        green)  echo -e "\033[32m$message\033[0m" ;;
        yellow) echo -e "\033[33m$message\033[0m" ;;
        blue)   echo -e "\033[34m$message\033[0m" ;;
        cyan)   echo -e "\033[36m$message\033[0m" ;;
        *)      echo "$message" ;;
    esac
}

# Function to get current version
get_current_version() {
    if [ ! -f "$PROJECT_PATH" ]; then
        print_color red "Error: Project file not found: $PROJECT_PATH"
        exit 1
    fi
    
    local version=$(grep -oP '<Version>\K[^<]+' "$PROJECT_PATH")
    if [ -z "$version" ]; then
        print_color red "Error: Version property not found in project file"
        exit 1
    fi
    
    echo "$version"
}

# Function to determine auto version type
get_auto_version_type() {
    # Get recent commits (since no tags exist, analyze recent history)
    local commits=$(git log --oneline -20 --pretty=format:"%s" 2>/dev/null || echo "")

    if [ -z "$commits" ]; then
        print_color yellow "No commits found, defaulting to patch"
        echo "patch"
        return
    fi

    local commit_count=$(echo "$commits" | wc -l)
    print_color cyan "Analyzing $commit_count recent commits for version increment"

    # Show some commits for debugging
    echo "Recent commits:"
    echo "$commits" | head -5
    echo ""

    # Check for breaking changes (major)
    if echo "$commits" | grep -qiE '(BREAKING|breaking change|major|feat!|fix!)'; then
        print_color red "Breaking changes detected, incrementing major version"
        echo "major"
    # Check for features (minor)
    elif echo "$commits" | grep -qE '(feat:|feature:|add:|new:|implement:)'; then
        print_color green "New features detected, incrementing minor version"
        echo "minor"
    # Check for fixes (patch)
    elif echo "$commits" | grep -qiE '(fix|bug|resolve|patch|correct)'; then
        print_color blue "Bug fixes detected, incrementing patch version"
        echo "patch"
    else
        print_color yellow "No clear pattern detected, defaulting to patch"
        echo "patch"
    fi
}

# Function to calculate next version
get_next_version() {
    local current_version=$1
    local version_type=$2
    
    # Parse version (x.y.z or x.y.z-suffix)
    if [[ ! $current_version =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)(-.*)?$ ]]; then
        print_color red "Error: Invalid version format: $current_version. Expected format: x.y.z or x.y.z-suffix"
        exit 1
    fi
    
    local major=${BASH_REMATCH[1]}
    local minor=${BASH_REMATCH[2]}
    local patch=${BASH_REMATCH[3]}
    local suffix=${BASH_REMATCH[4]}
    
    case $version_type in
        "major")
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        "minor")
            minor=$((minor + 1))
            patch=0
            ;;
        "patch")
            patch=$((patch + 1))
            ;;
        "auto")
            local auto_type=$(get_auto_version_type)
            # Recursively call with the detected type
            case $auto_type in
                "major")
                    major=$((major + 1))
                    minor=0
                    patch=0
                    ;;
                "minor")
                    minor=$((minor + 1))
                    patch=0
                    ;;
                "patch")
                    patch=$((patch + 1))
                    ;;
            esac
            ;;
    esac
    
    local new_version="$major.$minor.$patch"
    if [ -n "$suffix" ]; then
        new_version="$new_version$suffix"
    fi
    
    echo "$new_version"
}

# Function to update project version
update_project_version() {
    local new_version=$1
    local old_version=$2
    
    if [ "$DRY_RUN" = true ]; then
        print_color yellow "DRY RUN: Would update version from $old_version to $new_version in $PROJECT_PATH"
    else
        sed -i "s/<Version>$old_version<\/Version>/<Version>$new_version<\/Version>/" "$PROJECT_PATH"
        print_color green "Updated version from $old_version to $new_version in $PROJECT_PATH"
    fi
}

# Function to create Git tag
create_git_tag() {
    local version=$1
    local tag_name="v$version"
    
    if [ "$DRY_RUN" = true ]; then
        print_color yellow "DRY RUN: Would create Git tag: $tag_name"
    else
        if git tag "$tag_name"; then
            print_color green "Created Git tag: $tag_name"
            print_color cyan "To push the tag, run: git push origin $tag_name"
        else
            print_color red "Failed to create Git tag: $tag_name"
        fi
    fi
}

# Main execution
main() {
    print_color cyan "Starting version increment process..."
    echo "Version type: $VERSION_TYPE"
    echo "Project path: $PROJECT_PATH"
    
    if [ "$DRY_RUN" = true ]; then
        print_color yellow "DRY RUN MODE - No changes will be made"
    fi
    
    # Get current version
    local current_version=$(get_current_version)
    echo "Current version: $current_version"
    
    # Calculate next version
    echo "Calling get_next_version with: $current_version, $VERSION_TYPE"
    local next_version=$(get_next_version "$current_version" "$VERSION_TYPE")
    print_color green "Next version: $next_version"
    
    # Update project file
    update_project_version "$next_version" "$current_version"
    
    # Create Git tag if requested
    if [ "$CREATE_TAG" = true ]; then
        create_git_tag "$next_version"
    fi
    
    print_color green "Version increment completed successfully!"
    
    # Output for CI/CD
    echo "NEW_VERSION=$next_version"
}

# Run main function
main "$@"

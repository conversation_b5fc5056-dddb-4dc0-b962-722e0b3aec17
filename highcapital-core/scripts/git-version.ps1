param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "src/Core/Core.csproj",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [string]$TagPrefix = "v",
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateTag,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("patch", "minor", "major")]
    [string]$DefaultIncrement = "patch"
)

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Get-LatestGitTag {
    param([string]$TagPrefix)
    
    try {
        # Get all tags that match the prefix pattern
        $tags = git tag -l "$TagPrefix*" --sort=-version:refname 2>$null
        
        if ($LASTEXITCODE -eq 0 -and $tags) {
            $latestTag = $tags[0]
            $version = $latestTag -replace "^$TagPrefix", ""
            
            Write-ColorOutput "Latest Git tag: $latestTag (version: $version)" "Cyan"
            return $version
        }
        else {
            Write-ColorOutput "No Git tags found with prefix '$TagPrefix'" "Yellow"
            return $null
        }
    }
    catch {
        Write-ColorOutput "Error reading Git tags: $($_.Exception.Message)" "Red"
        return $null
    }
}

function Get-CommitsSinceTag {
    param([string]$TagName)
    
    try {
        if ($TagName) {
            $commits = git rev-list "$TagName..HEAD" --oneline 2>$null
        }
        else {
            $commits = git rev-list HEAD --oneline 2>$null
        }
        
        if ($LASTEXITCODE -eq 0) {
            return $commits
        }
        else {
            return @()
        }
    }
    catch {
        Write-ColorOutput "Error reading commits: $($_.Exception.Message)" "Yellow"
        return @()
    }
}

function Analyze-CommitsForVersionType {
    param([string[]]$Commits)
    
    if (-not $Commits -or $Commits.Count -eq 0) {
        Write-ColorOutput "No new commits found, using default increment: $DefaultIncrement" "Yellow"
        return $DefaultIncrement
    }
    
    Write-ColorOutput "Analyzing $($Commits.Count) commits for version increment..." "Cyan"
    
    # Join all commit messages for analysis
    $allCommits = $Commits -join " "
    
    # Check for breaking changes (major version)
    $breakingPatterns = @(
        'BREAKING\s+CHANGE',
        'breaking\s+change',
        '\[breaking\]',
        '\bmajor\b',
        '\!:'
    )
    
    foreach ($pattern in $breakingPatterns) {
        if ($allCommits -match $pattern) {
            Write-ColorOutput "Breaking changes detected, incrementing MAJOR version" "Red"
            return "major"
        }
    }
    
    # Check for new features (minor version)
    $featurePatterns = @(
        '\bfeat\b',
        '\bfeature\b',
        '\badd\b',
        '\bnew\b',
        '\[feature\]',
        '\[feat\]'
    )
    
    foreach ($pattern in $featurePatterns) {
        if ($allCommits -match $pattern) {
            Write-ColorOutput "New features detected, incrementing MINOR version" "Green"
            return "minor"
        }
    }
    
    # Default to patch for bug fixes, docs, etc.
    Write-ColorOutput "Bug fixes/patches detected, incrementing PATCH version" "Blue"
    return "patch"
}

function Get-NextVersionFromGit {
    param(
        [string]$CurrentVersion,
        [string]$VersionType
    )
    
    if (-not $CurrentVersion) {
        # If no tags exist, start with 1.0.0
        switch ($VersionType) {
            "major" { return "1.0.0" }
            "minor" { return "0.1.0" }
            "patch" { return "0.0.1" }
            default { return "0.0.1" }
        }
    }
    
    if (-not ($CurrentVersion -match '^(\d+)\.(\d+)\.(\d+)(?:-(.+))?$')) {
        throw "Invalid version format: $CurrentVersion. Expected format: x.y.z or x.y.z-suffix"
    }
    
    $major = [int]$matches[1]
    $minor = [int]$matches[2]
    $patch = [int]$matches[3]
    $suffix = $matches[4]
    
    switch ($VersionType) {
        "major" {
            $major++
            $minor = 0
            $patch = 0
        }
        "minor" {
            $minor++
            $patch = 0
        }
        "patch" {
            $patch++
        }
    }
    
    $newVersion = "$major.$minor.$patch"
    if ($suffix) {
        $newVersion += "-$suffix"
    }
    
    return $newVersion
}

function Update-ProjectVersion {
    param(
        [string]$ProjectPath,
        [string]$NewVersion
    )
    
    if (-not (Test-Path $ProjectPath)) {
        throw "Project file not found: $ProjectPath"
    }
    
    [xml]$projectXml = Get-Content $ProjectPath
    $versionNode = $projectXml.Project.PropertyGroup.Version
    
    if (-not $versionNode) {
        throw "Version property not found in project file"
    }
    
    $oldVersion = $versionNode
    
    if (-not $DryRun) {
        $projectXml.Project.PropertyGroup.Version = $NewVersion
        $projectXml.Save((Resolve-Path $ProjectPath))
        Write-ColorOutput "✓ Updated version from $oldVersion to $NewVersion in $ProjectPath" "Green"
    }
    else {
        Write-ColorOutput "DRY RUN: Would update version from $oldVersion to $NewVersion in $ProjectPath" "Yellow"
    }
}

function Create-GitTag {
    param([string]$Version, [string]$TagPrefix)
    
    $tagName = "$TagPrefix$Version"
    
    if (-not $DryRun) {
        git tag $tagName
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Created Git tag: $tagName" "Green"
        }
        else {
            Write-ColorOutput "✗ Failed to create Git tag: $tagName" "Red"
        }
    }
    else {
        Write-ColorOutput "DRY RUN: Would create Git tag: $tagName" "Yellow"
    }
}

# Main execution
try {
    Write-ColorOutput "🏷️  Starting Git-based version increment..." "Cyan"
    
    if ($DryRun) {
        Write-ColorOutput "🔍 DRY RUN MODE - No changes will be made" "Yellow"
    }
    
    # Get latest version from Git tags
    $currentVersion = Get-LatestGitTag -TagPrefix $TagPrefix
    
    # Get commits since last tag
    $latestTag = if ($currentVersion) { "$TagPrefix$currentVersion" } else { $null }
    $commits = Get-CommitsSinceTag -TagName $latestTag
    
    # Analyze commits to determine version increment type
    $versionType = Analyze-CommitsForVersionType -Commits $commits
    
    # Calculate next version
    $nextVersion = Get-NextVersionFromGit -CurrentVersion $currentVersion -VersionType $versionType
    
    Write-ColorOutput "Current version: $($currentVersion ?? 'none')" "White"
    Write-ColorOutput "Version increment type: $versionType" "White"
    Write-ColorOutput "Next version: $nextVersion" "Green"
    
    # Update project file
    Update-ProjectVersion -ProjectPath $ProjectPath -NewVersion $nextVersion
    
    # Create Git tag if requested
    if ($CreateTag) {
        Create-GitTag -Version $nextVersion -TagPrefix $TagPrefix
    }
    
    Write-ColorOutput "✅ Git-based version increment completed!" "Green"
    
    # Output for CI/CD
    Write-Output "NEW_VERSION=$nextVersion"
    Write-Output "VERSION_TYPE=$versionType"
    Write-Output "COMMITS_COUNT=$($commits.Count)"
}
catch {
    Write-ColorOutput "❌ Error: $($_.Exception.Message)" "Red"
    exit 1
}

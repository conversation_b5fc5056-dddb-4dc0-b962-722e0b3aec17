name: Publish NuGet

on:
  push:
    branches:
     - main
    tags:
      - "v*"
  pull_request:
    branches:
      - main
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version increment type'
        required: false
        default: 'auto'
        type: choice
        options:
        - auto
        - patch
        - minor
        - major

permissions:
  contents: write
  packages: write
  pull-requests: write
  actions: read
  issues: write

env:
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  DOTNET_NOLOGO: true
  NuGetDirectory: ${{github.workspace}}/nuget

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ steps.version.outputs.new_version }}
      final_version: ${{ steps.version_tag.outputs.final_version }}
      should_publish: ${{ steps.version.outputs.should_publish }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"

      - name: Increment Version
        id: version
        run: |
          # Get current version from project file
          CURRENT_VERSION=$(grep -oP '<Version>\K[^<]+' src/Core/Core.csproj)
          echo "Current version: $CURRENT_VERSION"

          # Determine version type
          
          VERSION_TYPE="${{ github.event.inputs.version_type }}"
          if [ -z "$VERSION_TYPE" ]; then
            VERSION_TYPE="auto"
          fi

          # Parse current version
          IFS='.' read -r MAJOR MINOR PATCH <<< "$CURRENT_VERSION"

          # Determine increment based on commits if auto
          if [ "$VERSION_TYPE" = "auto" ]; then
            # Get recent commits since last push
            COMMITS=$(git log --oneline -10 --pretty=format:"%s")
            echo "Analyzing commits:"
            echo "$COMMITS" | head -5

            # Check for breaking changes (major)
            if echo "$COMMITS" | grep -qE '(BREAKING|breaking change|major|feat!|fix!)'; then
              VERSION_TYPE="major"
              echo "Detected: Breaking changes -> major increment"
            # Check for features (minor)
            elif echo "$COMMITS" | grep -qE '(feat:|feature:|add:|new:|implement:)'; then
              VERSION_TYPE="minor"
              echo "Detected: New features -> minor increment"
            # Check for fixes (patch)
            elif echo "$COMMITS" | grep -qE '(fix:|bug:|resolve:|patch:|correct:)'; then
              VERSION_TYPE="patch"
              echo "Detected: Bug fixes -> patch increment"
            else
              VERSION_TYPE="patch"
              echo "Default: patch increment"
            fi
          fi

          # Calculate new version
          case $VERSION_TYPE in
            "major")
              NEW_VERSION="$((MAJOR + 1)).0.0"
              ;;
            "minor")
              NEW_VERSION="$MAJOR.$((MINOR + 1)).0"
              ;;
            "patch")
              NEW_VERSION="$MAJOR.$MINOR.$((PATCH + 1))"
              ;;
          esac

          echo "Version type: $VERSION_TYPE"
          echo "New version: $NEW_VERSION"

          # Update project file
          sed -i "s/<Version>$CURRENT_VERSION<\/Version>/<Version>$NEW_VERSION<\/Version>/" src/Core/Core.csproj

          # Verify the change
          UPDATED_VERSION=$(grep -oP '<Version>\K[^<]+' src/Core/Core.csproj)
          echo "Updated version in file: $UPDATED_VERSION"

          # Set outputs
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "should_publish=true" >> $GITHUB_OUTPUT

      - name: Create version tag without commit
        id: version_tag
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Configure git with GitHub Actions bot
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"

          # Check if there are changes to commit
          git add src/Core/Core.csproj

          if git diff --staged --quiet; then
            echo "No version changes detected"
            # Set output with original version
            echo "final_version=${{ steps.version.outputs.new_version }}" >> $GITHUB_OUTPUT
          else
            NEW_VERSION="${{ steps.version.outputs.new_version }}"

            echo "Version was incremented to $NEW_VERSION"
            echo "Due to branch protection rules, we'll create the tag without committing the version change"
            echo "The version change will be included in the NuGet package"

            # Create and push tag using the current commit (with version change in memory)
            ORIGINAL_VERSION="$NEW_VERSION"
            TAG_NAME="v$NEW_VERSION"

            # Setup git remote
            git remote set-url origin https://x-access-token:$<EMAIL>/${{ github.repository }}.git
            git fetch origin --tags

            # Check if tag already exists and increment minor version if needed
            ATTEMPT=0
            MAX_ATTEMPTS=20

            while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
              echo "Checking if tag $TAG_NAME already exists..."

              if git ls-remote --tags origin | grep -q "refs/tags/$TAG_NAME$"; then
                echo "⚠️ Tag $TAG_NAME already exists, incrementing minor version..."

                # Parse current version
                IFS='.' read -r MAJOR MINOR PATCH <<< "$NEW_VERSION"

                # Increment minor version and reset patch to 0
                MINOR=$((MINOR + 1))
                PATCH=0
                NEW_VERSION="$MAJOR.$MINOR.$PATCH"
                TAG_NAME="v$NEW_VERSION"

                echo "New version: $NEW_VERSION"
                ATTEMPT=$((ATTEMPT + 1))
              else
                echo "✅ Tag $TAG_NAME is available"
                break
              fi
            done

            if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
              echo "❌ Could not find available version after $MAX_ATTEMPTS attempts"
              exit 1
            fi

            # Update the project file with the final version if it changed
            if [ "$NEW_VERSION" != "$ORIGINAL_VERSION" ]; then
              echo "Updating project file with final version: $NEW_VERSION"
              sed -i "s/<Version>$ORIGINAL_VERSION<\/Version>/<Version>$NEW_VERSION<\/Version>/" src/Core/Core.csproj

              # Verify the change
              UPDATED_VERSION=$(grep -oP '<Version>\K[^<]+' src/Core/Core.csproj)
              echo "Updated version in file: $UPDATED_VERSION"
            fi

            echo "Creating tag: $TAG_NAME"
            git tag "$TAG_NAME"
            git push origin "$TAG_NAME"

            echo "✅ Tag $TAG_NAME created successfully"
            echo "📦 Final version: $NEW_VERSION"
            echo "📝 Note: Version change is included in the build but not committed to repository"
            echo "📦 The NuGet package will have version $NEW_VERSION"

            # Set output with final version
            echo "final_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          fi


      
      # - name: Install EF Core CLI
      #   run: dotnet tool install --global dotnet-ef

      - name: Restore dependencies
        run: dotnet restore src/Core/Core.csproj

      - name: Build all projects (Domain, Infrastructure, Core)
        run: |
          dotnet build src/Domain/Domain.csproj --configuration Release --no-restore
          dotnet build src/Infrastructure/Infrastructure.csproj --configuration Release --no-restore
          dotnet build src/Core/Core.csproj --configuration Release --no-restore
      
      # - name: Run EF Migrations
      #   env:
      #     ConnectionStrings__DefaultConnection: ${{ secrets.DB_CONNECTION }}
      #   run: |
      #     dotnet ef database update \
      #       --project src/Infrastructure/Infrastructure.csproj \
      #       --startup-project src/Infrastructure/Infrastructure.csproj \
      #       --connection "$ConnectionStrings__DefaultConnection"

          
      - name: Create NuGet directory
        run: mkdir -p ${{ env.NuGetDirectory }}

      - name: Pack NuGet package
        run: dotnet pack src/Core/Core.csproj --configuration Release --output ${{ env.NuGetDirectory }} --no-build

      - name: Upload NuGet package as artifact
        uses: actions/upload-artifact@v4
        with:
          name: nuget-packages
          if-no-files-found: error
          retention-days: 7
          path: ${{ env.NuGetDirectory }}/*.nupkg

  migrate:
    runs-on: ubuntu-latest
    needs: [ build ]
    if: needs.build.outputs.should_publish == 'true' && (github.event_name == 'push' && github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch')
    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Install Cloud SQL Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.8.0/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        env:
          INSTANCE_CONNECTION_NAME: ${{ secrets.GCP_SQL_INSTANCE_CONNECTION_NAME }}
        run: |
          echo "Starting Cloud SQL Proxy for instance: $INSTANCE_CONNECTION_NAME"
          cloud-sql-proxy $INSTANCE_CONNECTION_NAME --port=5432 &

          # Wait for proxy to be ready
          echo "Waiting for Cloud SQL Proxy to be ready..."
          for i in {1..30}; do
            if nc -z localhost 5432; then
              echo "Cloud SQL Proxy is ready!"
              break
            fi
            echo "Waiting... ($i/30)"
            sleep 2
          done

          if ! nc -z localhost 5432; then
            echo "❌ Cloud SQL Proxy failed to start"
            exit 1
          fi

      - name: Install EF (Entity Framework) Core CLI
        run: dotnet tool install --global dotnet-ef

      - name: Restore dependencies
        run: dotnet restore src/Core/Core.csproj

      - name: Build all projects (Domain, Infrastructure, Core)
        run: |
          dotnet build src/Domain/Domain.csproj --configuration Release --no-restore
          dotnet build src/Infrastructure/Infrastructure.csproj --configuration Release --no-restore
          dotnet build src/Core/Core.csproj --configuration Release --no-restore

      - name: Run EF Migrations
        env:
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: ${{ secrets.GCP_DB_NAME }}
          DB_USER: ${{ secrets.GCP_DB_USER }}
          DB_PASSWORD: ${{ secrets.GCP_DB_PASSWORD }}
        run: |
          # Build connection string for local proxy
          CONNECTION_STRING="Host=$DB_HOST;Port=$DB_PORT;Database=$DB_NAME;Username=$DB_USER;Password=$DB_PASSWORD;SSL Mode=Disable;"
          echo "Running migrations with Cloud SQL Proxy..."

          dotnet ef database update \
            --project src/Infrastructure/Infrastructure.csproj \
            --startup-project src/Infrastructure/Infrastructure.csproj \
            --connection "$CONNECTION_STRING"

  publish:
    runs-on: ubuntu-latest
    needs: [ build, migrate ]
    if: needs.build.outputs.should_publish == 'true' && (github.event_name == 'push' && github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v') || github.event_name == 'workflow_dispatch')
    steps:
      - name: Download NuGet package artifact
        uses: actions/download-artifact@v4
        with:
          name: nuget-packages
          path: ${{ env.NuGetDirectory }}

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"

      - name: Publish to GitHub Packages
        env:
          FINAL_VERSION: ${{ needs.build.outputs.final_version || needs.build.outputs.new_version }}
        run: |
          echo "Publishing version: $FINAL_VERSION"
          for file in ${{ env.NuGetDirectory }}/*.nupkg
          do
            echo "Publishing: $file"
            dotnet nuget push "$file" --api-key ${{ secrets.NUGET_TOKEN }} --source "https://nuget.pkg.github.com/HighCapitalTech/index.json" --skip-duplicate
          done

      - name: Create GitHub Release
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FINAL_VERSION: ${{ needs.build.outputs.final_version || needs.build.outputs.new_version }}
        with:
          tag_name: v${{ env.FINAL_VERSION }}
          release_name: Release v${{ env.FINAL_VERSION }}
          body: |
            ## Changes in v${{ env.FINAL_VERSION }}

            This release was automatically generated.

            ### Package Information
            - **Package**: HighCapital.Core
            - **Version**: ${{ env.FINAL_VERSION }}
            - **Published**: ${{ github.event.head_commit.timestamp }}

            ### Installation
            ```bash
            dotnet add package HighCapital.Core --version ${{ env.FINAL_VERSION }}
            ```
          draft: false
          prerelease: false

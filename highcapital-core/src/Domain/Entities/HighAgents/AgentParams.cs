using HighCapital.Core.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace HighCapital.Core.Domain.HighAgents.Entities
{
    public class AgentParams
    {
        [Key]
        public int Id { get; set; }

        // Identidade e Persona
        public string? CompanyName { get; set; }
        public string? CompanyDescription { get; set; }
        public string? Tone { get; set; }
        public string? Goals { get; set; }
        public string? MainMission { get; set; }
        public string? ContextDescription { get; set; }
        public string? QualificationRules { get; set; }

        // Scripts e Framework do Agente
        public string? ConversationGuidelines { get; set; }
        public string? OpeningScript { get; set; }
        public string? PreQualificationQuestions { get; set; }
        public string? PainAgitationScript { get; set; }
        public string? PricingAgitationScript { get; set; }
        public string? TraditionalMethods { get; set; }
        public string? SolutionScript { get; set; }
        public string? ValueGenerationScript { get; set; }
        public string? FinalQualificationQuestions { get; set; }
        public string? OpportunityReinforcementScript { get; set; }
        public string? EmotionalActivationScript { get; set; }
        public string? CallToActionScript { get; set; }
        public string? DisqualifiedFlowScript { get; set; }
        public string? RestrictionsAndLimits { get; set; }

        // Agendamento e mensagens automáticas
        public string? AskAvailabilityStyle { get; set; }
        public string? ConfirmationStyle { get; set; }
        public string? UserTone { get; set; }
        public string? AlternativeSuggestionStyle { get; set; }
        public string? ReminderStyle { get; set; }
        public string? ReminderTiming { get; set; }
        public string? RecurrenceStyle { get; set; }
        public string? CallToAction { get; set; }
        public string? CourtesyMessage { get; set; }

        // Relacionamento com usuário
        public int? UserId { get; set; }
        public User? User { get; set; }
    }
}

using HighCapital.Core.Domain.Entities;

namespace HighCapital.Core.Domain.HighAgents.Entities
{
    public class Message
    {
        public int Id { get; set; }
        public int AgentId { get; set; }
        public required Agent Agent { get; set; }
        public required string ConversationIdentificator { get; set; } // Identificador único da mensagem
        public required string Role { get; set; } // "user" ou "assistant"
        public required string Content { get; set; }

    }
}

using System.Text.RegularExpressions;

namespace HighCapital.Core.Services;

public class ValidationHelperService 
{
    public static bool IsValidEmail(string email) => Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");

    public static bool IsValidPhoneNumber(string phoneNumber) =>
        Regex.IsMatch(phoneNumber, @"^\+?[1-9]\d{1,14}$");

    public static bool IsValidCpf(string cpf) => Regex.IsMatch(cpf, @"^\d{3}\.\d{3}\.\d{3}-\d{2}$");
}

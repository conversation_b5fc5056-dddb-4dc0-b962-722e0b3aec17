using System.Text;
using HighCapital.Core.Interfaces;

namespace HighCapital.Core.Services;

public static class IdGenService     
{
    
    public static string GetId()
    {
        var guid = Guid.NewGuid().ToString("N").ToUpper();
        var letterBuilder = new StringBuilder();

        foreach (char character in guid)
        {
            if (char.IsLetter(character))
            {
                var numericValue = character - 'A' + 1;
                letterBuilder.Append(numericValue);
            }
            else
            {
                letterBuilder.Append(character);
            }
        }
        return letterBuilder.ToString()[..17];
    }

    public static List<string> GetIds(int count = 1)
    {
        return Enumerable.Range(0, count).Select(_ => GetId()).ToList();
    }
}

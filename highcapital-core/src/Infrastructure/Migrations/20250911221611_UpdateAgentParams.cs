﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateAgentParams : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Goal",
                table: "AgentParamsHighAgents",
                newName: "ValueGenerationScript");

            migrationBuilder.AddColumn<int>(
                name: "AgentParamsId",
                table: "AgentsHighAgents",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CallToActionScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CompanyDescription",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContextDescription",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ConversationGuidelines",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DisqualifiedFlowScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "EmotionalActivationScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FinalQualificationQuestions",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Goals",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MainMission",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OpeningScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OpportunityReinforcementScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PainAgitationScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PreQualificationQuestions",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PricingAgitationScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "QualificationRules",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "RestrictionsAndLimits",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SolutionScript",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "TraditionalMethods",
                table: "AgentParamsHighAgents",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_AgentsHighAgents_AgentParamsId",
                table: "AgentsHighAgents",
                column: "AgentParamsId");

            migrationBuilder.AddForeignKey(
                name: "FK_AgentsHighAgents_AgentParamsHighAgents_AgentParamsId",
                table: "AgentsHighAgents",
                column: "AgentParamsId",
                principalTable: "AgentParamsHighAgents",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AgentsHighAgents_AgentParamsHighAgents_AgentParamsId",
                table: "AgentsHighAgents");

            migrationBuilder.DropIndex(
                name: "IX_AgentsHighAgents_AgentParamsId",
                table: "AgentsHighAgents");

            migrationBuilder.DropColumn(
                name: "AgentParamsId",
                table: "AgentsHighAgents");

            migrationBuilder.DropColumn(
                name: "CallToActionScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "CompanyDescription",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "ContextDescription",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "ConversationGuidelines",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "DisqualifiedFlowScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "EmotionalActivationScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "FinalQualificationQuestions",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "Goals",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "MainMission",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "OpeningScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "OpportunityReinforcementScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "PainAgitationScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "PreQualificationQuestions",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "PricingAgitationScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "QualificationRules",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "RestrictionsAndLimits",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "SolutionScript",
                table: "AgentParamsHighAgents");

            migrationBuilder.DropColumn(
                name: "TraditionalMethods",
                table: "AgentParamsHighAgents");

            migrationBuilder.RenameColumn(
                name: "ValueGenerationScript",
                table: "AgentParamsHighAgents",
                newName: "Goal");
        }
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ChangeTypeOfExternalId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Add a temporary column for the new UUID values
            migrationBuilder.AddColumn<Guid>(
                name: "ExternalId_New",
                table: "Users",
                type: "uuid",
                nullable: false,
                defaultValueSql: "gen_random_uuid()");

            // Step 2: Copy existing integer values as UUIDs (generate new UUIDs for existing records)
            migrationBuilder.Sql(@"
                UPDATE ""Users""
                SET ""ExternalId_New"" = gen_random_uuid()
                WHERE ""ExternalId_New"" IS NULL OR ""ExternalId_New"" = '00000000-0000-0000-0000-000000000000'::uuid;
            ");

            // Step 3: Drop the old integer column
            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "Users");

            // Step 4: Rename the new column to the original name
            migrationBuilder.RenameColumn(
                name: "ExternalId_New",
                table: "Users",
                newName: "ExternalId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Step 1: Add a temporary integer column
            migrationBuilder.AddColumn<int>(
                name: "ExternalId_Old",
                table: "Users",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            // Step 2: Generate sequential integer values for existing records
            migrationBuilder.Sql(@"
                UPDATE ""Users""
                SET ""ExternalId_Old"" = ""Id""
                WHERE ""ExternalId_Old"" = 0;
            ");

            // Step 3: Drop the UUID column
            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "Users");

            // Step 4: Rename the integer column back to original name
            migrationBuilder.RenameColumn(
                name: "ExternalId_Old",
                table: "Users",
                newName: "ExternalId");
        }
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class addAgentParams : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AgentParamsHighAgents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CompanyName = table.Column<string>(type: "text", nullable: false),
                    Tone = table.Column<string>(type: "text", nullable: false),
                    Goal = table.Column<string>(type: "text", nullable: false),
                    AskAvailabilityStyle = table.Column<string>(type: "text", nullable: false),
                    ConfirmationStyle = table.Column<string>(type: "text", nullable: false),
                    UserTone = table.Column<string>(type: "text", nullable: false),
                    AlternativeSuggestionStyle = table.Column<string>(type: "text", nullable: false),
                    ReminderStyle = table.Column<string>(type: "text", nullable: false),
                    ReminderTiming = table.Column<string>(type: "text", nullable: false),
                    RecurrenceStyle = table.Column<string>(type: "text", nullable: false),
                    CallToAction = table.Column<string>(type: "text", nullable: false),
                    CourtesyMessage = table.Column<string>(type: "text", nullable: false),
                    AgentId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentParamsHighAgents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentParamsHighAgents_AgentsHighAgents_AgentId",
                        column: x => x.AgentId,
                        principalTable: "AgentsHighAgents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AgentParamsHighAgents_AgentId",
                table: "AgentParamsHighAgents",
                column: "AgentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgentParamsHighAgents");
        }
    }
}

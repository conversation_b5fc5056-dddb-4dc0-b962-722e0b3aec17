import * as signalR from '@microsoft/signalr';

let connection: signalR.HubConnection | null = null;

export function startConnection(url: string) {
  connection = new signalR.HubConnectionBuilder()
    .withUrl(url)
    .withAutomaticReconnect()
    .build();
  return connection
    .start()
    .then(() => console.log('Conectado ao SignalR'))
    .catch((err) => console.error('Erro na conexão:', err));
}

export function getConnection() {
  return connection;
}

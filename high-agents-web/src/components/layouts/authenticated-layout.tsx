import { Outlet } from 'react-router-dom';
import { Sidebar } from '../organisms';
import { SidebarItem } from '../organisms/sidebar';
import { BarChart3, GitGraph } from 'lucide-react';

interface AuthenticatedLayoutProps {
  className?: string;
}

export function AuthenticatedLayout({ className }: AuthenticatedLayoutProps) {
  return (
    <div className={`w-full min-h-screen ${className || ''}`}>
      <main className="w-full flex">
        <Sidebar>
          <SidebarItem icon={<BarChart3 size={20} />} text="Dashboard" active alert />
          <SidebarItem icon={<GitGraph size={20} />} text="Estatisticas" />
        </Sidebar>
        <div className="py-16 px-8 flex-1 overflow-y-auto">
          <Outlet />
        </div>
      </main>
    </div>
  );
}

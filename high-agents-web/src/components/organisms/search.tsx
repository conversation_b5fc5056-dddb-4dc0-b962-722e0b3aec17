import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useCallback, useEffect, useState } from 'react';

interface SearchProps<T> {
  searchFn: (query: string) => Promise<T[]>;
  getLabel: (item: T) => string;
  onSelect: (item: T) => void;
  debounceMs?: number;
  initialLabel?: string;
}

export function Search<T>({
  searchFn,
  getLabel,
  onSelect,
  debounceMs = 300,
  initialLabel,
}: SearchProps<T>) {
  const [inputValue, setInputValue] = useState(initialLabel || '');
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const debouncedSearch = useCallback(() => {
    const handler = setTimeout(async () => {
      if (!query.trim()) {
        setResults([]);
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const data = await searchFn(query);
        setResults(data);
      } catch (err) {
        setError('Erro ao buscar resultados');
      } finally {
        setLoading(false);
      }
    }, debounceMs);

    return () => clearTimeout(handler);
  }, [query, debounceMs]);

  const handleSelect = (item: T) => {
    onSelect(item);
    setInputValue(getLabel(item));
    setResults([]);
  };

  useEffect(() => {
    const cleanup = debouncedSearch();
    return cleanup;
  }, [debouncedSearch]);

  useEffect(() => {
    setInputValue(initialLabel || '');
  }, [initialLabel]);

  return (
    <div className="w-full max-w-md mx-auto space-y-4">
      <Input
        placeholder="Digite para buscar..."
        className="w-full h-10"
        value={inputValue}
        onChange={(e) => {
          const val = e.target.value;
          setInputValue(val);
          setQuery(val);
        }}
      />

      {loading && (
        <p className="text-sm text-muted-foreground">Carregando...</p>
      )}
      {error && <p className="text-sm text-red-500">{error}</p>}

      {results.length > 0 && (
        <ScrollArea className="h-64 border rounded-md">
          <ul className="divide-y">
            {results.map((item, i) => (
              <li
                key={i}
                onClick={() => handleSelect(item)}
                className="cursor-pointer p-2 hover:bg-muted transition-colors text-center"
              >
                {getLabel(item)}
              </li>
            ))}
          </ul>
        </ScrollArea>
      )}
    </div>
  );
}

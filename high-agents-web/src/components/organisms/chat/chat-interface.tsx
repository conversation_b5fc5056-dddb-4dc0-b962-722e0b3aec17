import { <PERSON><PERSON>, <PERSON> } from '@/components';
import useConversation from '@/hooks/useConversation';
import { AlertCircleIcon, ArrowLeftIcon, RefreshCcw } from 'lucide-react';
import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import ConversationList from './conversation-list';
import MessageInput from './message-input';
import MessageList from './message-list';

const ChatInterface: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    loading,
    conversations,
    selectedConversation,
    messages,
    newMessage,
    sendingMessage,
    messagesEndRef,
    setNewMessage,
    selectConversation,
    loadConversations,
  } = useConversation();

  if (loading && !id) {
    return (
      <div className="h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-white">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-gray-600 font-medium">Carregando dados do agente...</p>
        </div>
      </div>
    );
  }

  if (!id) {
    return (
      <Card className="h-full flex items-center justify-center bg-gradient-to-br from-red-50 to-white">
        <div className="text-center p-8">
          <AlertCircleIcon size={64} className="text-red-400 mx-auto mb-4 drop-shadow-lg" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Agente não encontrado
          </h3>
          <p className="text-gray-500">
            Não foi possível carregar os dados do agente selecionado.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="h-full flex bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      {/* Lista de Conversas - Container Principal */}
      <aside className="w-80 flex-shrink-0 border-r border-gray-200 h-full bg-gradient-to-b from-gray-50 to-white flex flex-col">
        <div className="flex gap-3 p-4 border-b border-gray-100 bg-white items-center">
          <Button
            className="flex items-center gap-2 px-3 py-2 rounded-md text-gray-700 hover:bg-blue-50 transition font-semibold"
            variant="ghost"
            onClick={() => navigate(-1)}
          >
            <ArrowLeftIcon size={18} className="text-blue-500" />
            <span>Voltar</span>
          </Button>
          <Button
            className="flex items-center gap-2 px-3 py-2 rounded-md text-gray-700 hover:bg-green-50 transition font-semibold"
            variant="ghost"
            onClick={() => loadConversations()}
          >
            <RefreshCcw size={18} className="text-green-500" />
            <span>Atualizar</span>
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto">
          <ConversationList
            conversations={conversations}
            selectedConversation={selectedConversation}
            onSelectConversation={selectConversation}
            loading={loading}
          />
        </div>
      </aside>

      {/* Área de Mensagens - Container Secundário */}
      <div className="flex-1 flex flex-col h-full min-w-0 bg-gradient-to-br from-white to-blue-50">
        {/* Lista de Mensagens */}
        <div className="flex-1 min-h-0 px-6 py-4">
          <MessageList
            messages={messages}
            selectedConversation={selectedConversation}
            messagesEndRef={messagesEndRef}
            loading={loading}
          />
        </div>

        {/* Input de Mensagem */}
        {selectedConversation && (
          <div className="flex-shrink-0 px-6 py-4 bg-white border-t border-gray-100">
            <MessageInput
              value={newMessage}
              onChange={setNewMessage}
              onSend={() => {}}
              disabled
              loading={sendingMessage}
              placeholder={`Comming Son...`}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInterface;

import { Avatar, AvatarFallback, AvatarImage } from '@/components';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { IConversation, IMessage } from '@/interfaces/IChat';
import getInitials from '@/utils/getInitialChar';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  BotIcon,
  CheckCheckIcon,
  CheckIcon,
  ClockIcon,
  XCircleIcon,
} from 'lucide-react';
import React from 'react';
import LoaderComponent from '../loader';

interface MessageListProps {
  messages: IMessage[];
  selectedConversation: IConversation | null;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  loading?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  selectedConversation,
  messagesEndRef,
  loading = false,
}) => {
  const formatTime = (date: Date) => {
    return format(date, 'HH:mm', { locale: ptBR });
  };

  const formatDate = (date: Date) => {
    return format(date, "dd 'de' MMMM", { locale: ptBR });
  };

  const getStatusIcon = (status = 'sent') => {
    switch (status) {
      case 'sending':
        return <ClockIcon size={14} className="text-gray-400" />;
      case 'sent':
        return <CheckIcon size={14} className="text-gray-400" />;
      case 'delivered':
        return <CheckCheckIcon size={14} className="text-gray-400" />;
      case 'read':
        return <CheckCheckIcon size={14} className="text-blue-500" />;
      case 'failed':
        return <XCircleIcon size={14} className="text-red-500" />;
      default:
        return null;
    }
  };

  const shouldShowDateSeparator = (
    currentMessage: IMessage,
    previousMessage?: IMessage
  ) => {
    if (!previousMessage) return true;

    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();

    return currentDate !== previousDate;
  };

  // if (loading) {
  //   return (
  //     <div className="flex-1 flex items-center justify-center">
  //       <LoaderComponent title="Carregando mensagens..." type='bars'/>
  //     </div>
  //   );
  // }

  if (!selectedConversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 h-full ">
        {loading ? (
          <LoaderComponent title="Carregando mensagens..." type="bars" />
        ) : (
          <div className="text-center p-8">
            <BotIcon size={64} className="text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Selecione uma conversa
            </h3>
            <p className="text-gray-500">
              Escolha uma conversa da lista para começar a visualizar as
              mensagens
            </p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={undefined} alt={selectedConversation.username} />
            <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
              {getInitials(selectedConversation.username)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-medium text-gray-900">
              {selectedConversation.username}
            </h3>
            <p className="text-sm text-gray-500">
              {selectedConversation.conversationIdentificator}
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 min-h-0 p-4">
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Nenhuma mensagem ainda</p>
              <p className="text-sm text-gray-400 mt-1">
                Inicie a conversa enviando uma mensagem
              </p>
            </div>
          ) : (
            messages.map((message, index) => {
              const previousMessage =
                index > 0 ? messages[index - 1] : undefined;
              const showDateSeparator = shouldShowDateSeparator(
                message,
                previousMessage
              );

              return (
                <div key={message.id}>
                  {/* Date Separator */}
                  {showDateSeparator && (
                    <div className="flex justify-center my-4">
                      <span className="bg-white px-3 py-1 rounded-full text-xs text-gray-500 border">
                        {formatDate(new Date(message.timestamp))}
                      </span>
                    </div>
                  )}

                  {/* Message */}
                  <div
                    className={`flex gap-3 items-center ${
                      message.role === 'assistant'
                        ? 'justify-start'
                        : 'justify-end'
                    }`}
                  >
                    {!(message.role === 'user') && (
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          <BotIcon size={16} />
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={`
                        max-w-xs lg:max-w-md px-4 py-2 rounded-lg
                        ${
                          message.role === 'assistant'
                            ? 'bg-white border border-gray-200 text-gray-900'
                            : 'bg-blue-500 text-white'
                        }
                      `}
                    >
                      <p className="text-sm whitespace-pre-wrap break-words">
                        {message.content}
                      </p>
                      <div
                        className={`
                          flex items-center justify-end gap-1 mt-1
                          ${message.role === 'assistant' ? 'text-gray-500' : 'text-blue-100'}
                          `}
                      >
                        <span className="text-xs">
                          {formatTime(new Date(message.timestamp))}
                        </span>
                        {!(message.role === 'assistant') &&
                          getStatusIcon('sent')}
                      </div>
                    </div>
                    {/* {message.role === 'user' && (
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarFallback className="bg-gray-100 text-gray-600">
                          <UserIcon size={16} />
                        </AvatarFallback>
                      </Avatar>
                    )} */}
                  </div>
                </div>
              );
            })
          )}

          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
    </div>
  );
};

export default MessageList;

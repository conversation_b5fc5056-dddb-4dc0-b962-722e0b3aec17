import { Avatar, AvatarFallback, AvatarImage } from '@/components';
import { Input } from '@/components/atoms/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { IConversation } from '@/interfaces/IChat';
import getInitials from '@/utils/getInitialChar';
import { MessageCircleIcon, SearchIcon } from 'lucide-react';
import React from 'react';
import LoaderComponent from '../loader';

interface ConversationListProps {
  conversations: IConversation[];
  selectedConversation: IConversation | null;
  onSelectConversation: (conversation: IConversation) => void;
  loading?: boolean;
}

const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  selectedConversation,
  onSelectConversation,
  loading = false,
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');

  if (loading) {
    <div className='flex justify-center items-center h-full'>
    <LoaderComponent/>
    </div>
  }

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="mb-2">
          <h2 className="text-lg font-semibold text-gray-800">Conversas</h2>
        </div>
        <div className="relative">
          <SearchIcon size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
        type="text"
        placeholder="Buscar conversa (em breve)"
        value={searchTerm}
        disabled
        onChange={(e) => setSearchTerm(e.target.value)}
        className="pl-10 pr-4 py-2 w-full text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-200 bg-gray-50"
          />
        </div>
      </div>

      {/* Conversations List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {conversations.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <MessageCircleIcon size={48} className="text-gray-300 mb-3" />
              <p className="text-gray-500 text-sm">
                {searchTerm ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa disponível'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {conversations.map((conversation) => {
                return (
                <div
                  key={conversation.conversationIdentificator}
                  onClick={() => {
                    console.log(conversation);

                    onSelectConversation(conversation)
                  }}
                  className={`
                    flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors
                    ${selectedConversation?.conversationIdentificator === conversation.conversationIdentificator
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-gray-50'
                    }
                  `}
                >
                  {/* Avatar */}
                  <div className="relative">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={undefined} alt={conversation.username} />
                      <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                        {getInitials(conversation.username)}
                      </AvatarFallback>
                    </Avatar>
                    {/* {conversation.unreadCount > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
                      >
                        {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                      </Badge>
                    )} */}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-gray-900 truncate">
                        {conversation.username}
                      </h3>
                      {/* <span className="text-xs text-gray-500 flex-shrink-0">
                        {formatTime(conversation.)}
                      </span> */}
                    </div>

                    {/* <div className="flex items-center justify-between">
                      <p className={`
                        text-sm truncate flex-1
                        ${conversation.unreadCount > 0
                          ? 'text-gray-900 font-medium'
                          : 'text-gray-500'
                        }
                      `}>
                        {conversation.lastMessage}
                      </p>
                    </div> */}

                    <p className="text-xs text-gray-400 mt-1">
                      {conversation.conversationIdentificator}
                    </p>
                  </div>
                </div>
              )
              })}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default ConversationList;

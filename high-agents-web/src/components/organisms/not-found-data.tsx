import React from 'react';

interface NotFoundDataProps {
    message?: string;
    imageSrc?: string;
    altText?: string;
}

const defaultImage =
    'https://cdn-icons-png.flaticon.com/512/2748/2748558.png';

export const NotFoundData: React.FC<NotFoundDataProps> = ({
    message = 'Nenhum resultado encontrado.',
    imageSrc = defaultImage,
    altText = 'Sem resultados',
}) => (
    <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '32px',
        color: '#555',
    }}>
        <img
            src={imageSrc}
            alt={altText}
            style={{ width: 64, height: 64, marginBottom: 16, opacity: 0.7 }}
        />
        <span>{message}</span>
    </div>
);

export default NotFoundData;
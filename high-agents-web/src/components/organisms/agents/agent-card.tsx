import { Button } from '@/components/atoms';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { IAgent } from '@/interfaces/IAgents';
import { sliceLargeString } from '@/utils/sliceLargeString';
import { Bot, Cog, Edit, ScrollText, Trash } from 'lucide-react'; // Ícone de redirecionamento
import { useRef } from 'react';
import NotFoundData from '../not-found-data';

interface AgentCardListProps {
  agents: IAgent[];
  onSelect?: (agent: IAgent) => void;
  onEdit?: (agent: IAgent) => void;
  onDelete?: (agent: IAgent) => void;
  showActions?: boolean;
}

export function AgentCardList({
  agents,
  onEdit,
  onDelete,
  showActions = true,
  onSelect,
}: AgentCardListProps) {
  const popoverRef = useRef<HTMLButtonElement>(null);
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {agents.length > 0 ? (
        agents?.map((agent) => (
            <Card
            key={agent.id}
            className={`relative ${
              !showActions
              ? 'cursor-pointer transition-transform duration-200 hover:shadow-lg hover:scale-[1.03]'
              : 'transition-transform duration-200 hover:shadow-lg relative'
            }`}
            onClick={() =>onSelect?.(agent)}
            >
            <CardHeader className="flex items-center justify-between">
              <div className="flex items-center gap-2">
              <Bot color="blue" size={20} />
              <h3 className="text-lg font-semibold">
                {sliceLargeString(agent.name, 18)}
              </h3>
              </div>
              {showActions && (
                <div className="flex items-center gap-2 flex-col">
                  <Tooltip>
                    <TooltipTrigger>
                      <Button
                        className="bg-transparent border border-gray-300 hover:bg-gray-100 cursor-pointer hover:scale-105 transition-transform"
                        onClick={() => onEdit?.(agent)}
                      >
                        <Edit className=" text-yellow-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Editar agente</p>
                    </TooltipContent>
                  </Tooltip>
                  <Popover>
                    <PopoverTrigger ref={popoverRef}>
                      <Tooltip>
                        <TooltipTrigger>
                          <Button className="bg-transparent border border-gray-300 hover:bg-gray-100 cursor-pointer hover:scale-105 transition-transform">
                            <Trash className="text-red-500" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Excluir Agente</p>
                        </TooltipContent>
                      </Tooltip>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-80 p-6 rounded-lg shadow-lg"
                      side="top"
                    >
                      <h4 className="text-lg font-semibold mb-2 text-center text-red-600">
                        Confirmar exclusão
                      </h4>
                      <p className="text-sm text-muted-foreground mb-4 text-center">
                        Tem certeza que deseja excluir o agente{' '}
                        {sliceLargeString(agent.name, 18)}?
                      </p>
                      <div className="flex justify-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          className="px-4 cursor-pointer"
                          onClick={() => {
                            popoverRef.current?.click();
                          }
                           }
                        >
                          Cancelar
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          className="px-4 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelete?.(agent);
                          }}
                        >
                          Excluir
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              )}
            </CardHeader>
            <CardContent className="flex flex-col gap-2">
              <p className="text-muted-foreground flex items-center gap-1">
                <ScrollText size={20} /> Descrição:{' '}
                {sliceLargeString(agent.context)}
              </p>

              <p className="text-muted-foreground flex items-center gap-1">
                <Cog size={20} />
                Parâmetro: {sliceLargeString(agent.paramName || 'Não definido')}
              </p>
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="col-span-full">
          <NotFoundData />
        </div>
      )}
    </div>
  );
}

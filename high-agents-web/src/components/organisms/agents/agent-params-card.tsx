import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { IAgentParams } from '@/interfaces/IAgents';
import { Building2, Edit, Trash } from 'lucide-react';
import { useRef } from 'react';
import NotFoundData from '../not-found-data';

interface Props {
  agentParams: IAgentParams[] | null;
  onEdit?: (param: IAgentParams) => void;
  onDelete?: (param: IAgentParams) => void;
}

export function AgentParamsCard({ agentParams, onEdit, onDelete }: Props) {
  const refPopover = useRef<HTMLButtonElement>(null);
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {agentParams && agentParams.length > 0 ? (
        agentParams.map((param, index) => (
          <Card
            key={index}
            className="hover:shadow-lg relative"
          >
            <CardHeader className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                {param.paramName || 'Parâmetro sem nome'}
              </h3>
              <div className="flex items-center gap-2 flex-col">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="border border-gray-300 hover:bg-gray-100 cursor-pointer"
                      onClick={() => onEdit?.(param)}
                    >
                      <Edit className="text-yellow-500" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Editar Parâmetro</p>
                  </TooltipContent>
                </Tooltip>
                <Popover>
                  <PopoverTrigger ref={refPopover}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="border border-gray-300 hover:bg-gray-100 cursor-pointer"
                        >
                          <Trash className="text-red-500" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Excluir Parâmetro</p>
                      </TooltipContent>
                    </Tooltip>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-80 p-6 rounded-lg shadow-lg"
                    side="top"
                  >
                    <h4 className="text-lg font-semibold mb-2 text-center text-red-600">
                      Confirmar exclusão
                    </h4>
                    <p className="text-sm text-muted-foreground mb-4 text-center">
                      Tem certeza que deseja excluir o parâmetro{' '}
                      {param.paramName || 'Sem nome do parâmetro'}?
                    </p>
                    <div className="flex justify-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="px-4 cursor-pointer"
                        onClick={() => {
                          refPopover.current?.click();
                        }}
                      >
                        Cancelar
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="px-4 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete?.(param);
                          // Fechar popover: depende da implementação do Popover
                        }}
                      >
                        Excluir
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground flex items-center gap-1 mb-2">
                <Building2 size={20} />{' '}
                {param.companyName || 'Sem nome da empresa'}
              </p>
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="col-span-full">
          <NotFoundData />
        </div>
      )}
    </div>
  );
}

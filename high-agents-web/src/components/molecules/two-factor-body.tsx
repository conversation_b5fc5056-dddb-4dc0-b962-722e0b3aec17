import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '../atoms/input-otp';
import { Button } from '../atoms';
import { toast } from 'sonner';

const schema = z.object({
  otp: z.string().length(6, 'The code must be 6 digits long.'),
});

type FormData = z.infer<typeof schema>;

export function BodyTwoFactorAuth() {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const onSubmit = (data: FormData) => {
    console.log(data);

    toast('your code has been verified', {
      description: 'Thank you, your code has been verified.',
      action: {
        label: 'okay',
        onClick: () => toast.dismiss(),
      },
      style: {
        background: 'white',
        color: 'var(--hc-scooter)',
      },
      actionButtonStyle: {
        backgroundColor: 'var(--hc-scooter)',
        color: 'white',
        borderRadius: '6px',
        padding: '4px 10px',
      },
    });
  };

  return (
    <div className="p-4 sm:p-6">
      <h1 className="text-2xl sm:text-3xl font-semibold mt-2 text-center">
        Verify your code
      </h1>
      <h6 className="text-xs sm:text-sm text-gray-500 mt-4 text-center">
        Please enter the code to authenticate
      </h6>

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 mt-6 items-center">
        <Controller
          name="otp"
          control={control}
          defaultValue=""
          render={({ field }) => (
            <InputOTP
              maxLength={6}
              value={field.value}
              onChange={field.onChange}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          )}
        />

        {errors.otp && (
          <p className="text-red-500 text-xs">{errors.otp.message}</p>
        )}

        <Button
          type="submit"
          className="bg-[#2ec0df] text-white font-semibold hover:bg-[#2593ac] w-full sm:w-auto"
        >
          Verify
        </Button>
      </form>
    </div>
  );
}

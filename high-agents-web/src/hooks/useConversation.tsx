import { baseURL, signalRMessagesHub } from '@/config';
import type { IConversation, IMessage } from '@/interfaces/IChat';
import { getConnection, startConnection } from '@/services/signalR';
import axios from 'axios';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'sonner';

function useConversation() {
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [conversations, setConversations] = useState<IConversation[]>([]);
  const [selectedConversation, setSelectedConversation] =
    useState<IConversation | null>(null);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const selectedConversationRef = useRef<string | null>(null);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const getAllConversationByAgentId = async (
    agentId: number
  ): Promise<IConversation[]> => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${baseURL}/Message/agent/${agentId}/conversations`
      );
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar conversas:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const getAllMessagesByConversationId = async (
    conversationId: string
  ): Promise<IMessage[]> => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${baseURL}/Message/conversation/${conversationId}`
      );
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const loadConversations = useCallback(async () => {
    try {
      if (!id) return;
      setLoading(true);

      const convs = await getAllConversationByAgentId(+id);
      setConversations(convs);
    } catch (error) {
      console.error('Erro ao carregar conversas:', error);
      toast.error('Erro ao carregar conversas');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const loadMessages = useCallback(async (conversation: IConversation) => {
    try {
      setLoading(true);
      const msgs = await getAllMessagesByConversationId(
        conversation.conversationIdentificator
      );
      setMessages(msgs);
    } catch (error) {
      console.error('Erro ao carregar mensagens:', error);
      toast.error('Erro ao carregar mensagens');
    } finally {
      setLoading(false);
    }
  }, []);

  const selectConversation = useCallback(
    (conversation: IConversation) => {
      setSelectedConversation(conversation);
      loadMessages(conversation);
    },
    [loadMessages]
  );

  const addMessageIfSelected = useCallback((incoming: any) => {
    const convId = incoming.conversationIdentificator;
    const username = incoming.username;
    const content = incoming.content;

    if (convId !== selectedConversationRef.current) {
      return;
    }

    setMessages((prev) => {
      const exists =
        (incoming.id && prev.some((m) => m.id === incoming.id)) ||
        (!incoming.id &&
          prev.some((m) => m.content === content && m.username === username));

      if (exists) return prev;

      const newMsg: IMessage = {
        id: incoming.id ?? `${Date.now()}-${Math.random()}`,
        conversationIdentificator: convId,
        content,
        username,
        timestamp: new Date().toISOString(),
        role: incoming.username === "Assistant" ? "assistant" : "user",
      } as any;

      return [...prev, newMsg];
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    async function initSignalR() {
      await startConnection(signalRMessagesHub);
      const connection = getConnection();
      if (!connection) return;

      const connAny = connection as any;
      if (!connAny.__hasNewMessageSubscription) {
        connection.on('NewMessage', (message: any) => {
          addMessageIfSelected(message);
        });
        connAny.__hasNewMessageSubscription = true;
      }

      if (conversations.length > 0) {
        for (const c of conversations) {
          try {
            await connection.invoke(
              'JoinConversationGroup',
              c.conversationIdentificator
            );
          } catch (err) {
            console.warn(
              'Erro ao join grupo',
              c.conversationIdentificator,
              err
            );
          }
        }
      }
    }

    if (mounted) initSignalR();

    return () => {
      mounted = false;
    };
  }, [conversations, addMessageIfSelected]);

  useEffect(() => {
    if (id) {
      loadConversations();
    }
  }, [id, loadConversations]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  useEffect(() => {
    selectedConversationRef.current =
      selectedConversation?.conversationIdentificator ?? null;
  }, [selectedConversation]);

  return {
    loading,
    conversations,
    selectedConversation,
    messages,
    newMessage,
    sendingMessage,
    messagesEndRef,
    setNewMessage,
    selectConversation,
    scrollToBottom,
    loadConversations
  };
}

export default useConversation;

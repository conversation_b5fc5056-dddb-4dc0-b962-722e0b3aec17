import type { IAgentParams, IAgentParamsWithoutId, SearchAgentInputInterface, SearchAgentOutputInterface } from '@/interfaces/IAgents';
import { createAgentInputs } from '@/mocks/agents/Inputs';
import {
  CreateAgentParamsSchema,
  type CreateAgentParamsInput,
} from '@/schemas/agents-schema';
import { useAgentsParamsService } from '@/services/agents/agents-params.service';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

export default function useAgentsParams() {
  const [loading, setLoading] = useState(false);
  const [agentParams, setAgentParams] = useState<IAgentParams[]>([]);
  const [selectedAgentParam, setSelectedAgentParam] = useState<IAgentParams | null>(null);
  const [tabs, setTabs] = useState<string>('list-params');

  
  const paramsForm = useForm<CreateAgentParamsInput>({
    resolver: zodResolver(CreateAgentParamsSchema),
  });

  const editParamsForm = useForm<CreateAgentParamsInput>({
    resolver: zodResolver(CreateAgentParamsSchema),
  });

  const handleChangeTab = (value: string) => {
    setTabs(value);
  };

    const handleSearchAgentByName = async (name: string) => {
    try {
      setLoading(true);
      const params: SearchAgentInputInterface = {
        ParamName: name,
        userId: 2,
      };
      return await useAgentsParamsService.searchByName(params);
    } catch (error) {
      console.error('Error searching agent params by name:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const editInputs = [
    ...createAgentInputs,
    {
      name: 'agentParamsId',
      label: 'Buscar Parâmetro',
      placeholder: 'Digite o nome do parâmetro',
      type: 'search',
      searchFn: handleSearchAgentByName,
      getLabel: (item: SearchAgentOutputInterface) => item.paramName,
      debounceMs: 300,
    },
  ];

  const handleCreateAgentParams = async (params: IAgentParamsWithoutId) => {
    try {
      setLoading(true);
      const createdParam: IAgentParamsWithoutId = {...params, userId: 2};
       const response = await useAgentsParamsService.create(createdParam);
      toast.success('Parâmetro de agente criado com sucesso!', {description: 'Lista atualizada.'});
       setAgentParams((prev) => [...prev, response.value]);
      paramsForm.reset();
      setTabs('list-params');

    } catch (error) {
      console.error('Error creating agent params:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleGetAllByUser = async (userId: number) => {
    try {
      setLoading(true);
      const params = await useAgentsParamsService.getByUserId(userId);
      setAgentParams(params);
    } catch (error) {
      console.error('Error fetching agent params:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setLoading(true);
      await useAgentsParamsService.delete(id);
      setAgentParams((prev) => prev.filter(param => param.id !== id));
      toast.success('Parâmetro de agente deletado com sucesso!', {description: 'Lista atualizada.'});
      if (selectedAgentParam?.id === id) {
        setSelectedAgentParam(null);
        setTabs('list-params');
      }
    } catch (error) {
      console.error('Error deleting agent params:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }

  const handleSelectParam = (param: IAgentParams) => {
    editParamsForm.reset(param);
    setTabs('edit-params');
    setSelectedAgentParam(param);
  }

  const handleEditAgentParams = async (id: number, data: IAgentParams) => {
    try {
      setLoading(true);
      const params = {...data, id};
      const response = await useAgentsParamsService.update(id, params);
      setAgentParams((prev) => prev.map(param => param.id === id ? response.value : param));
      toast.success('Parâmetro de agente atualizado com sucesso!', {description: 'Lista atualizada.'});
      setSelectedAgentParam(response.value);
      setTabs('list-params');
    } catch (error) {
      console.error('Error updating agent params:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    handleGetAllByUser(2);
  }, []);

  return {
    paramsForm,
    handleCreateAgentParams,
    loadingParams: loading,
    agentParams,
    handleEditAgentParams,
    tabsParams: tabs,
    handleChangeTabParam: handleChangeTab,
    handleSelectParam,
    selectedAgentParam,
    editParamsForm,
    handleDeleteParams: handleDelete,
    handleGetParams: handleGetAllByUser,
    editInputs
  };
}

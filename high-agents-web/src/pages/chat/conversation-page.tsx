import { ChatInterface } from '@/components/organisms';
import PageHeader from '@/components/organisms/page-header';
import { MessageCircleIcon } from 'lucide-react';

function ConversationChatPage() {


  return (
    <main className="h-full container mx-auto flex flex-col overflow-hidden">
      <PageHeader
        title="Conversa"
        description="Gerencie suas conversas de forma eficaz."
        icon={<MessageCircleIcon size={30} className="text-blue-500" />}
      />

      {/* Chat Interface - Container principal e secundário */}
      <section className="flex-1 min-h-0 max-h-[600px]">
        <ChatInterface />
      </section>
    </main>
  );
}
export default ConversationChatPage;

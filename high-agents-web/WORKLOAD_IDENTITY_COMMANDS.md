# 🔐 Comandos Úteis para Workload Identity

Este arquivo contém comandos úteis para gerenciar e troubleshoot a configuração do Workload Identity Provider.

## 📋 Variáveis de Ambiente

Defina essas variáveis para facilitar o uso dos comandos:

```bash
export PROJECT_ID="seu-project-id"
export GITHUB_REPO="owner/repo"
export SERVICE_ACCOUNT_EMAIL="github-actions@${PROJECT_ID}.iam.gserviceaccount.com"
export POOL_ID="projects/${PROJECT_ID}/locations/global/workloadIdentityPools/github-pool"
export PROVIDER_ID="projects/${PROJECT_ID}/locations/global/workloadIdentityPools/github-pool/providers/github-provider"
```

## 🔍 Verificação da Configuração

### Listar Workload Identity Pools
```bash
gcloud iam workload-identity-pools list --location=global --project=$PROJECT_ID
```

### Listar Providers
```bash
gcloud iam workload-identity-pools providers list \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID
```

### Verificar Configuração do Provider
```bash
gcloud iam workload-identity-pools providers describe github-provider \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID
```

### Verificar Bindings da Service Account
```bash
gcloud iam service-accounts get-iam-policy $SERVICE_ACCOUNT_EMAIL \
    --project=$PROJECT_ID
```

## 🛠️ Comandos de Manutenção

### Atualizar Mapeamento de Atributos
```bash
gcloud iam workload-identity-pools providers update-oidc github-provider \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID \
    --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository,attribute.repository_owner=assertion.repository_owner,attribute.ref=assertion.ref"
```

### Adicionar Novo Repositório
```bash
gcloud iam service-accounts add-iam-policy-binding \
    --role roles/iam.workloadIdentityUser \
    --member "principalSet://iam.googleapis.com/${POOL_ID}/attribute.repository/NOVO_OWNER/NOVO_REPO" \
    $SERVICE_ACCOUNT_EMAIL
```

### Remover Repositório
```bash
gcloud iam service-accounts remove-iam-policy-binding \
    --role roles/iam.workloadIdentityUser \
    --member "principalSet://iam.googleapis.com/${POOL_ID}/attribute.repository/OWNER/REPO" \
    $SERVICE_ACCOUNT_EMAIL
```

### Restringir por Branch (apenas main)
```bash
gcloud iam service-accounts add-iam-policy-binding \
    --role roles/iam.workloadIdentityUser \
    --member "principalSet://iam.googleapis.com/${POOL_ID}/attribute.repository/${GITHUB_REPO}" \
    --condition='expression=assertion.ref=="refs/heads/main"' \
    $SERVICE_ACCOUNT_EMAIL
```

## 🔧 Troubleshooting

### Testar Token JWT (localmente)
```bash
# Instalar gcloud alpha components
gcloud components install alpha

# Gerar token de teste
gcloud alpha iamcredentials generate-access-token \
    --impersonate-service-account=$SERVICE_ACCOUNT_EMAIL \
    --scope=https://www.googleapis.com/auth/cloud-platform
```

### Verificar Logs de Auditoria
```bash
# Ver logs de autenticação
gcloud logging read 'protoPayload.serviceName="iamcredentials.googleapis.com"' \
    --project=$PROJECT_ID \
    --limit=50 \
    --format="table(timestamp,protoPayload.authenticationInfo.principalEmail,protoPayload.methodName)"
```

### Verificar Permissões da Service Account
```bash
# Listar roles da service account
gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --format="table(bindings.role)" \
    --filter="bindings.members:$SERVICE_ACCOUNT_EMAIL"
```

## 🚨 Comandos de Emergência

### Desabilitar Provider (emergência)
```bash
gcloud iam workload-identity-pools providers update-oidc github-provider \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID \
    --disabled
```

### Reabilitar Provider
```bash
gcloud iam workload-identity-pools providers update-oidc github-provider \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID \
    --no-disabled
```

### Deletar Provider (cuidado!)
```bash
gcloud iam workload-identity-pools providers delete github-provider \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID
```

### Deletar Pool (cuidado!)
```bash
gcloud iam workload-identity-pools delete github-pool \
    --location=global \
    --project=$PROJECT_ID
```

## 📊 Monitoramento

### Criar Alerta para Falhas de Autenticação
```bash
# Criar política de alerta
gcloud alpha monitoring policies create --policy-from-file=- <<EOF
{
  "displayName": "Workload Identity Authentication Failures",
  "conditions": [
    {
      "displayName": "Authentication failures",
      "conditionThreshold": {
        "filter": "resource.type=\"iam_service_account\" AND protoPayload.serviceName=\"iamcredentials.googleapis.com\" AND severity=\"ERROR\"",
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 5,
        "duration": "300s"
      }
    }
  ],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF
```

### Verificar Métricas de Uso
```bash
# Ver estatísticas de uso da service account
gcloud logging read 'protoPayload.serviceName="iamcredentials.googleapis.com" AND protoPayload.authenticationInfo.principalEmail="'$SERVICE_ACCOUNT_EMAIL'"' \
    --project=$PROJECT_ID \
    --limit=100 \
    --format="value(timestamp)"
```

## 🔄 Backup e Restore

### Exportar Configuração do Pool
```bash
gcloud iam workload-identity-pools describe github-pool \
    --location=global \
    --project=$PROJECT_ID \
    --format="export" > workload-identity-pool-backup.yaml
```

### Exportar Configuração do Provider
```bash
gcloud iam workload-identity-pools providers describe github-provider \
    --workload-identity-pool=github-pool \
    --location=global \
    --project=$PROJECT_ID \
    --format="export" > workload-identity-provider-backup.yaml
```

### Restaurar Pool (se necessário)
```bash
gcloud iam workload-identity-pools create github-pool \
    --location=global \
    --project=$PROJECT_ID \
    --config-from-file=workload-identity-pool-backup.yaml
```

## 📝 Notas Importantes

1. **Tempo de Propagação**: Mudanças podem levar até 5 minutos para propagar
2. **Cache de Tokens**: Tokens têm TTL de 1 hora
3. **Limites**: Máximo de 300 pools por projeto
4. **Auditoria**: Todos os acessos são logados automaticamente
5. **Custos**: Workload Identity é gratuito, mas há limites de quota

## 🔗 Links Úteis

- [Documentação Oficial](https://cloud.google.com/iam/docs/workload-identity-federation)
- [GitHub Actions com GCP](https://github.com/google-github-actions/auth)
- [Troubleshooting Guide](https://cloud.google.com/iam/docs/troubleshooting-workload-identity-federation)
- [Best Practices](https://cloud.google.com/iam/docs/best-practices-for-using-workload-identity-federation)

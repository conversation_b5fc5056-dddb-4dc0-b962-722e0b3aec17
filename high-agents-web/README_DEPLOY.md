# 🚀 Deploy Automático para Google Cloud Storage

Sistema completo de CI/CD para deploy automático do frontend React no Google Cloud Storage usando GitHub Actions com **Workload Identity Provider**.

## 📋 Resumo

- ✅ **Deploy automático** na branch `main`
- ✅ **Preview deploys** para Pull Requests
- ✅ **Workload Identity** (sem chaves JSON!)
- ✅ **<PERSON><PERSON> otimizado** para performance
- ✅ **Cleanup automático** de previews
- ✅ **Website estático** configurado
- ✅ **Buckets públicos** para acesso direto
- ✅ **Segurança aprimorada** com rotação automática

## 🚀 Configuração Rápida

### Opção 1: Script Automático (Recomendado)

```bash
# Tornar o script executável
chmod +x scripts/setup-gcp-deploy.sh

# Executar configuração automática
./scripts/setup-gcp-deploy.sh
```

### Opção 2: Configuração Manual

Siga o guia detalhado em [`DEPLOYMENT_SETUP.md`](./DEPLOYMENT_SETUP.md)

## 📁 Arquivos Criados

```
.github/
├── workflows/
│   ├── deploy.yml      # Deploy principal (main branch)
│   └── preview.yml     # Preview deploys (PRs)
scripts/
└── setup-gcp-deploy.sh # Script de configuração automática
DEPLOYMENT_SETUP.md     # Guia detalhado de configuração
```

## 🔐 GitHub Secrets Necessários

| Secret | Descrição | Exemplo |
|--------|-----------|---------|
| `GCP_PROJECT_ID` | ID do projeto GCP | `meu-projeto-123` |
| `GCS_BUCKET_NAME` | Bucket de produção | `meu-site-prod` |
| `GCS_PREVIEW_BUCKET_NAME` | Bucket de preview | `meu-site-preview` |
| `WIF_PROVIDER` | Workload Identity Provider | `projects/123/locations/global/workloadIdentityPools/github-pool/providers/github-provider` |
| `WIF_SERVICE_ACCOUNT` | Email da service account | `<EMAIL>` |

### 🔒 Vantagens do Workload Identity
- **Sem chaves JSON** para armazenar ou gerenciar
- **Rotação automática** de credenciais
- **Auditoria completa** de todos os acessos
- **Princípio do menor privilégio** aplicado automaticamente

## 🌊 Fluxo de Deploy

### Deploy de Produção
```mermaid
graph LR
    A[Push to main] --> B[Build & Test]
    B --> C[Deploy to GCS]
    C --> D[Configure Cache]
    D --> E[Set Public Access]
    E --> F[🎉 Live!]
```

### Preview Deploy
```mermaid
graph LR
    A[Create PR] --> B[Build & Test]
    B --> C[Deploy to Preview]
    C --> D[Comment PR URL]
    D --> E[👀 Review]
    E --> F[Merge/Close]
    F --> G[Cleanup Preview]
```

## 🌐 URLs de Acesso

### Produção
```
https://storage.googleapis.com/SEU_BUCKET_PRODUCAO/index.html
```

### Preview (Pull Requests)
```
https://storage.googleapis.com/SEU_BUCKET_PREVIEW/pr-123/index.html
```

## ⚡ Otimizações Incluídas

### Cache Headers
- **Assets estáticos** (JS, CSS, imagens): `max-age=31536000` (1 ano)
- **HTML**: `no-cache, no-store, must-revalidate`

### Compressão
- Gzip automático pelo Vite
- Minificação de assets
- Tree shaking para reduzir bundle size

### Performance
- CDN global do Google Cloud
- Compressão Brotli quando disponível
- Headers de cache otimizados

## 🔧 Comandos Úteis

### Verificar deploy local
```bash
npm run build
npm run preview
```

### Verificar arquivos no bucket
```bash
gsutil ls -la gs://SEU_BUCKET/
```

### Limpar bucket manualmente
```bash
gsutil -m rm -r gs://SEU_BUCKET/*
```

### Ver logs do deploy
```bash
# No GitHub: Actions > Workflow executado > Logs
```

## 🐛 Troubleshooting

### Build falha
1. Verifique se `npm run build` funciona localmente
2. Confirme se não há erros de lint
3. Verifique dependências no `package.json`

### Deploy falha
1. Verifique se todos os secrets estão configurados
2. Confirme permissões da service account
3. Verifique se os buckets existem

### Site não carrega
1. Confirme se bucket é público
2. Verifique se `index.html` existe
3. Teste URL diretamente no navegador

## 📊 Monitoramento

### Métricas disponíveis
- Tempo de build
- Tamanho do bundle
- Tempo de deploy
- Taxa de sucesso

### Logs
- GitHub Actions logs
- GCP Cloud Logging
- Browser DevTools

## 🔄 Atualizações

Para atualizar os workflows:
1. Edite arquivos em `.github/workflows/`
2. Commit e push
3. Workflows são atualizados automaticamente

## 🆘 Suporte

### Recursos úteis
- [GitHub Actions Docs](https://docs.github.com/en/actions)
- [Google Cloud Storage Docs](https://cloud.google.com/storage/docs)
- [Vite Build Docs](https://vitejs.dev/guide/build.html)

### Problemas comuns
- **Permissões**: Verifique roles da service account
- **Secrets**: Confirme se estão configurados corretamente
- **Build**: Teste localmente primeiro
- **Cache**: Limpe cache do navegador para ver mudanças

## 🎯 Próximos Passos

1. **Domínio customizado**: Configure CNAME para seu domínio
2. **HTTPS**: Use Cloud Load Balancer ou Cloudflare
3. **CDN**: Ative Cloud CDN para melhor performance
4. **Monitoramento**: Configure alertas para falhas de deploy
5. **Backup**: Configure versionamento nos buckets

---

**💡 Dica**: Mantenha sempre uma cópia local dos arquivos de configuração e chaves de acesso em local seguro!

import { type ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth-context';

interface AuthGuardProps {
  children: ReactNode;
}

export const AuthGuard = ({ children }: AuthGuardProps) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const isAuthRoute = location.pathname.startsWith('/auth');
  const is404Route = location.pathname === '/404';

  if (!isAuthenticated && !isAuthRoute && !is404Route) {
    return <Navigate to="/auth/login" replace />;
  }

  if (isAuthenticated && isAuthRoute) {
    return <Navigate to="/app/dashboard" replace />;
  }

  return <>{children}</>;
};

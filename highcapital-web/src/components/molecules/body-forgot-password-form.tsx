import z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { InputLabel } from './input-label';
import { Button } from '../atoms';
import { MdEmail } from 'react-icons/md';
import { toast } from 'sonner';

const forgotPasswordSchema = z.object({
  email: z.string().nonempty('Email is required ').email('Invalid email'),
});

export type ConfirmEmailProps = z.infer<typeof forgotPasswordSchema>;

const BodyForgotPass = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ConfirmEmailProps>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = (data: ConfirmEmailProps) => {
    toast('Email has been sent', {
      description: 'Verify your email mailbox',
      action: {
        label: 'okay',
        onClick: () => console.log('Undo'),
      },
      style: {
        background: 'white',
        color: 'var(--hc-scooter)',
      },
      actionButtonStyle: {
        backgroundColor: 'var(--hc-scooter)',
        color: 'white',
        borderRadius: '6px',
        padding: '4px 10px',
      },
    });

    console.log('Form enviado:', data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-4 sm:p-6">
      <h1 className="text-2xl sm:text-3xl font-semibold mt-4 flex flex-col sm:flex-row gap-2 items-center sm:items-start text-center sm:text-left">
        Confirm your account <MdEmail className="mt-0 sm:mt-1" />
      </h1>
      <h6 className="text-xs sm:text-sm text-gray-500 mt-4 mb-4 text-center sm:text-left">
        We need to confirm that it really is you
      </h6>

      <InputLabel
        label="Email to confirm"
        placeholder="Email"
        type="email"
        {...register('email')}
      />

      {errors.email && (
        <p className="text-red-500 text-[10px] mt-1">{errors.email.message}</p>
      )}

      <Button
        type="submit"
        className="mt-6 bg-gradient-to-r text-white text-xs bg-[#2ec0df] font-medium hover:bg-[#2593ac]"
      >
        send code
      </Button>
    </form>
  );
};

export default BodyForgotPass;

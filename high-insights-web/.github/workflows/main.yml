name: Deploy to Cloud Run

on:
  push:
    branches:
      - release
  workflow_dispatch:

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  SERVICE_NAME: ${{ secrets.GCP_SERVICE_NAME }}
  REGION: ${{ secrets.GCP_REGION }}
  IMAGE_NAME: gcr.io/${{ secrets.GCP_PROJECT_ID }}/high-insights-web

jobs:
  deploy:
    name: Deploy to Cloud Run
    runs-on: ubuntu-latest

    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Clean Cache
        run: npm cache clean --force

      - name: Delete Node Modules and Cache Files
        run: rm -rf node_modules package-lock.json pnpm-lock.yaml yarn.lock

      - name: Install Rollup Linux x64
        run: npm install @rollup/rollup-linux-x64-gnu --legacy-peer-deps

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Run linter
        run: npm run lint

      - name: Build project
        run: npm run build
        env:
          CI: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Build Docker image
        run: |
          docker build -t $IMAGE_NAME:$GITHUB_SHA .
          docker tag $IMAGE_NAME:$GITHUB_SHA $IMAGE_NAME:latest

      - name: Push Docker image
        run: |
          docker push $IMAGE_NAME:$GITHUB_SHA
          docker push $IMAGE_NAME:latest

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy $SERVICE_NAME \
            --image $IMAGE_NAME:$GITHUB_SHA \
            --platform managed \
            --region $REGION \
            --allow-unauthenticated \
            --port 80 \
            --memory 512Mi \
            --cpu 1 \
            --min-instances 0 \
            --max-instances ${{ secrets.GCP_MAX_INSTANCES }} \
            --set-env-vars NODE_ENV=production

      - name: Get service URL
        run: |
          SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')
          echo "Service deployed at: $SERVICE_URL"
          echo "SERVICE_URL=$SERVICE_URL" >> $GITHUB_ENV

      - name: Comment PR with deployment URL
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Deployment successful!**\n\nYour application has been deployed to: ${{ env.SERVICE_URL }}`
            })

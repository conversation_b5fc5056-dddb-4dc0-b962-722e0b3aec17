'use client'

import { useState, useEffect, useRef } from 'react'
import { ChevronDown, X } from 'lucide-react'

interface Option {
  id: number
  name: string
  [key: string]: any
}

interface AutocompleteProps {
  label: string
  options: Option[]
  value: string
  onChange: (value: string, option?: Option) => void
  placeholder?: string
  loading?: boolean
  required?: boolean
  className?: string
}

export default function Autocomplete({
  label,
  options,
  value,
  onChange,
  placeholder = 'Selecione uma opção...',
  loading = false,
  required = false,
  className = ''
}: AutocompleteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedOption, setSelectedOption] = useState<Option | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Encontrar a opção selecionada baseada no value
  useEffect(() => {
    if (value && options.length > 0) {
      const option = options.find(opt => opt.id.toString() === value)
      if (option) {
        setSelectedOption(option)
        setSearchTerm(option.name)
      }
    } else {
      setSelectedOption(null)
      setSearchTerm('')
    }
  }, [value, options])

  // Filtrar opções baseado no termo de busca
  const filteredOptions = options.filter(option =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Fechar dropdown quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        // Restaurar o nome da opção selecionada se não houver seleção válida
        if (selectedOption) {
          setSearchTerm(selectedOption.name)
        } else {
          setSearchTerm('')
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [selectedOption])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchTerm = e.target.value
    setSearchTerm(newSearchTerm)
    setIsOpen(true)
    
    // Se o campo estiver vazio, limpar a seleção
    if (!newSearchTerm) {
      setSelectedOption(null)
      onChange('')
    }
  }

  const handleOptionSelect = (option: Option) => {
    setSelectedOption(option)
    setSearchTerm(option.name)
    setIsOpen(false)
    onChange(option.id.toString(), option)
  }

  const handleClear = () => {
    setSelectedOption(null)
    setSearchTerm('')
    onChange('')
    inputRef.current?.focus()
  }

  const handleInputFocus = () => {
    setIsOpen(true)
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          className="input-field pr-20"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          required={required}
          autoComplete="off"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 space-x-1">
          {selectedOption && (
            <button
              type="button"
              onClick={handleClear}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          <button
            type="button"
            onClick={() => setIsOpen(!isOpen)}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {loading ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              Carregando...
            </div>
          ) : filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <button
                key={option.id}
                type="button"
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                onClick={() => handleOptionSelect(option)}
              >
                <div className="font-medium">{option.name}</div>
                {option.email && (
                  <div className="text-xs text-gray-500">{option.email}</div>
                )}
                {option.price && (
                  <div className="text-xs text-gray-500">
                    R$ {option.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </div>
                )}
              </button>
            ))
          ) : (
            <div className="px-3 py-2 text-sm text-gray-500">
              Nenhuma opção encontrada
            </div>
          )}
        </div>
      )}
    </div>
  )
}

'use client'

import { profileOptions } from '@/lib/auth'
import { useEffect, useState } from 'react'

interface CompanyAutocompleteProps {
  value: string
  onChange: (value: string) => void
  required?: boolean
  className?: string
}

export default function CompanyAutocomplete({
  value,
  onChange,
  required = false,
  className = ''
}: CompanyAutocompleteProps) {
  const [isGlobalAdmin, setIsGlobalAdmin] = useState(false)
  const [userCompanyId, setUserCompanyId] = useState<string>('')

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isAdmin = localStorage.getItem('is_admin') === 'true'
      const companyId = localStorage.getItem('company_id') || ''

      setIsGlobalAdmin(isAdmin)
      setUserCompanyId(companyId)

      // Se não é admin global e não há valor selecionado, define a empresa do usuário
      if (!isAdmin && !value && companyId) {
        onChange(companyId)
      }
    }
  }, [value, onChange])

  // Se não é admin global, retorna um campo desabilitado com a empresa do usuário
  if (!isGlobalAdmin) {
    const userCompany = profileOptions.find(p => p.id.toString() === userCompanyId)

    return (
      <div className={className}>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Empresa {required && <span className="text-red-500">*</span>}
        </label>
        <input
          type="text"
          className="input-field bg-gray-100 cursor-not-allowed"
          value={userCompany?.name || ''}
          disabled
          readOnly
        />
        <p className="mt-1 text-xs text-gray-500">
          Você só pode criar registros para sua empresa
        </p>
      </div>
    )
  }

  // Se é admin global, mostra um select simples
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        Empresa {required && <span className="text-red-500">*</span>}
      </label>
      <select
        className="input-field"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required={required}
      >
        <option value="">Selecione uma empresa...</option>
        {profileOptions.map((profile) => (
          <option key={profile.id} value={profile.id.toString()}>
            {profile.name}
          </option>
        ))}
      </select>
    </div>
  )
}

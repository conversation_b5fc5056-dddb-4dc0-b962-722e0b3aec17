'use client'

import Autocomplete from '@/components/Autocomplete'
import CompanyAutocomplete from '@/components/CompanyAutocomplete'
import Layout from '@/components/Layout'
import api from '@/lib/api'
import { Download, Edit, Plus, Trash2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'

interface Sale {
  id: number
  lead_id?: number
  product_id?: number
  amount: number
  sale_date: string
  notes?: string
  company_id?: number
  company_name?: string
  created_at: string
  updated_at: string
  lead?: { name: string }
  product?: { name: string }
}

interface Lead {
  id: number
  name: string
  email?: string
  phone?: string
  social_media?: string
  source?: string
  status: string
}

interface Product {
  id: number
  name: string
  price: number
}

export default function SalesPage() {
  const [sales, setSales] = useState<Sale[]>([])
  const [leads, setLeads] = useState<Lead[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [leadsLoading, setLeadsLoading] = useState(false)
  const [productsLoading, setProductsLoading] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [editingSale, setEditingSale] = useState<Sale | null>(null)
  const [formData, setFormData] = useState({
    lead_id: '',
    product_id: '',
    amount: '',
    sale_date: '',
    notes: '',
    company_id: ''
  })

  // Função para formatar valor como moeda brasileira
  const formatCurrency = (value: string): string => {
    // Remove tudo que não é dígito
    const numericValue = value.replace(/\D/g, '')

    // Se não há valor, retorna vazio
    if (!numericValue) return ''

    // Converte para número e divide por 100 para ter os centavos
    const numberValue = parseInt(numericValue) / 100

    // Formata como moeda brasileira
    return numberValue.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    })
  }

  // Função para converter valor formatado para número
  const parseCurrencyToNumber = (value: string): number => {
    // Remove símbolos de moeda e espaços, substitui vírgula por ponto
    const numericString = value
      .replace(/[R$\s]/g, '')
      .replace(/\./g, '')
      .replace(',', '.')

    return parseFloat(numericString) || 0
  }

  // Função para lidar com mudança no campo de valor
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCurrency(e.target.value)
    setFormData({ ...formData, amount: formattedValue })
  }

  useEffect(() => {
    fetchSales()
    fetchLeads()
    fetchProducts()
  }, [])

  const fetchSales = async () => {
    try {
      const response = await api.get('/sales')
      setSales(response.data)
    } catch (error) {
      toast.error('Erro ao carregar vendas')
    } finally {
      setLoading(false)
    }
  }

  const fetchLeads = async () => {
    setLeadsLoading(true)
    try {
      const response = await api.get('/leads')
      setLeads(response.data)
    } catch (error) {
      toast.error('Erro ao carregar leads')
    } finally {
      setLeadsLoading(false)
    }
  }

  const fetchProducts = async () => {
    setProductsLoading(true)
    try {
      const response = await api.get('/products')
      setProducts(response.data)
    } catch (error) {
      toast.error('Erro ao carregar produtos')
    } finally {
      setProductsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const saleData = {
        ...formData,
        lead_id: formData.lead_id ? parseInt(formData.lead_id) : null,
        product_id: formData.product_id ? parseInt(formData.product_id) : null,
        amount: parseCurrencyToNumber(formData.amount),
        company_id: formData.company_id ? parseInt(formData.company_id) : null
      }
      
      if (editingSale) {
        await api.put(`/sales/${editingSale.id}`, saleData)
        toast.success('Venda atualizada com sucesso!')
      } else {
        await api.post('/sales', saleData)
        toast.success('Venda criada com sucesso!')
      }
      setShowModal(false)
      setEditingSale(null)
      setFormData({ lead_id: '', product_id: '', amount: '', sale_date: '', notes: '', company_id: '' })
      fetchSales()
    } catch (error) {
      toast.error('Erro ao salvar venda')
    }
  }

  const handleEdit = (sale: Sale) => {
    setEditingSale(sale)
    setFormData({
      lead_id: sale.lead_id?.toString() || '',
      product_id: sale.product_id?.toString() || '',
      amount: formatCurrency((sale.amount * 100).toString()),
      sale_date: sale.sale_date,
      notes: sale.notes || '',
      company_id: sale.company_id?.toString() || ''
    })
    setShowModal(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja deletar esta venda?')) {
      try {
        await api.delete(`/sales/${id}`)
        toast.success('Venda deletada com sucesso!')
        fetchSales()
      } catch (error) {
        toast.error('Erro ao deletar venda')
      }
    }
  }

  const handleExport = async () => {
    try {
      const response = await api.get('/export/sales', {
        responseType: 'blob'
      })
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'vendas.csv')
      document.body.appendChild(link)
      link.click()
      link.remove()
      
      toast.success('Vendas exportadas com sucesso!')
    } catch (error) {
      toast.error('Erro ao exportar vendas')
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vendas</h1>
            <p className="mt-1 text-sm text-gray-500">
              Acompanhe suas vendas e performance
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleExport}
              className="btn-secondary flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar CSV
            </button>
            <button
              onClick={() => {
                setEditingSale(null)
                setFormData({ lead_id: '', product_id: '', amount: '', sale_date: '', notes: '', company_id: '' })
                setShowModal(true)
              }}
              className="btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Venda
            </button>
          </div>
        </div>

        {/* Tabela de Vendas */}
        <div className="card">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="table-header">Lead</th>
                  <th className="table-header">Produto</th>
                  <th className="table-header">Valor</th>
                  <th className="table-header">Empresa</th>
                  <th className="table-header">Data da Venda</th>
                  <th className="table-header">Notas</th>
                  <th className="table-header">Ações</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sales.map((sale) => (
                  <tr key={sale.id}>
                    <td className="table-cell">{sale.lead?.name || '-'}</td>
                    <td className="table-cell">{sale.product?.name || '-'}</td>
                    <td className="table-cell font-medium">
                      R$ {sale.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </td>
                    <td className="table-cell">{sale.company_name || '-'}</td>
                    <td className="table-cell">
                      {new Date(sale.sale_date).toLocaleDateString('pt-BR')}
                    </td>
                    <td className="table-cell">{sale.notes || '-'}</td>
                    <td className="table-cell">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(sale)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(sale.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modal de Criação/Edição */}
        {showModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingSale ? 'Editar Venda' : 'Nova Venda'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <Autocomplete
                    label="Lead"
                    options={leads}
                    value={formData.lead_id}
                    onChange={(value) => setFormData({ ...formData, lead_id: value })}
                    placeholder="Selecione um lead..."
                    loading={leadsLoading}
                  />
                  <Autocomplete
                    label="Produto"
                    options={products}
                    value={formData.product_id}
                    onChange={(value) => setFormData({ ...formData, product_id: value })}
                    placeholder="Selecione um produto..."
                    loading={productsLoading}
                  />
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Valor *</label>
                    <input
                      type="text"
                      required
                      className="input-field"
                      placeholder="R$ 0,00"
                      value={formData.amount}
                      onChange={handleAmountChange}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Data da Venda *</label>
                    <input
                      type="date"
                      required
                      className="input-field"
                      value={formData.sale_date}
                      onChange={(e) => setFormData({ ...formData, sale_date: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notas</label>
                    <textarea
                      className="input-field"
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    />
                  </div>
                  <CompanyAutocomplete
                    value={formData.company_id}
                    onChange={(value: string) => setFormData({ ...formData, company_id: value })}
                    required
                  />
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="btn-secondary"
                    >
                      Cancelar
                    </button>
                    <button type="submit" className="btn-primary">
                      {editingSale ? 'Atualizar' : 'Criar'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
'use client'

import { useEffect, useState } from 'react'
import Layout from '@/components/Layout'
import api from '@/lib/api'
import { Plus, Edit, Trash2 } from 'lucide-react'
import toast from 'react-hot-toast'

interface Goal {
  id: number
  title: string
  description?: string
  target_value: number
  current_value: number
  goal_type: string
  period_type: string
  start_date: string
  end_date: string
  created_at: string
  updated_at: string
}

export default function GoalsPage() {
  const [goals, setGoals] = useState<Goal[]>([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    target_value: '',
    current_value: '',
    goal_type: 'sales',
    period_type: 'monthly',
    start_date: '',
    end_date: ''
  })

  useEffect(() => {
    fetchGoals()
  }, [])

  const fetchGoals = async () => {
    try {
      const response = await api.get('/goals')
      setGoals(response.data)
    } catch (error) {
      toast.error('Erro ao carregar metas')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const goalData = {
        ...formData,
        target_value: parseFloat(formData.target_value),
        current_value: parseFloat(formData.current_value)
      }
      
      if (editingGoal) {
        await api.put(`/goals/${editingGoal.id}`, goalData)
        toast.success('Meta atualizada com sucesso!')
      } else {
        await api.post('/goals', goalData)
        toast.success('Meta criada com sucesso!')
      }
      setShowModal(false)
      setEditingGoal(null)
      setFormData({
        title: '',
        description: '',
        target_value: '',
        current_value: '',
        goal_type: 'sales',
        period_type: 'monthly',
        start_date: '',
        end_date: ''
      })
      fetchGoals()
    } catch (error) {
      toast.error('Erro ao salvar meta')
    }
  }

  const handleEdit = (goal: Goal) => {
    setEditingGoal(goal)
    setFormData({
      title: goal.title,
      description: goal.description || '',
      target_value: goal.target_value.toString(),
      current_value: goal.current_value.toString(),
      goal_type: goal.goal_type,
      period_type: goal.period_type,
      start_date: goal.start_date,
      end_date: goal.end_date
    })
    setShowModal(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja deletar esta meta?')) {
      try {
        await api.delete(`/goals/${id}`)
        toast.success('Meta deletada com sucesso!')
        fetchGoals()
      } catch (error) {
        toast.error('Erro ao deletar meta')
      }
    }
  }

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100)
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Metas</h1>
            <p className="mt-1 text-sm text-gray-500">
              Defina e acompanhe suas metas de negócio
            </p>
          </div>
          <button
            onClick={() => {
              setEditingGoal(null)
              setFormData({
                title: '',
                description: '',
                target_value: '',
                current_value: '',
                goal_type: 'sales',
                period_type: 'monthly',
                start_date: '',
                end_date: ''
              })
              setShowModal(true)
            }}
            className="btn-primary flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nova Meta
          </button>
        </div>

        {/* Cards de Metas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {goals.map((goal) => {
            const progress = getProgressPercentage(goal.current_value, goal.target_value)
            return (
              <div key={goal.id} className="card">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{goal.title}</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(goal)}
                      className="text-primary-600 hover:text-primary-900"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(goal.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                {goal.description && (
                  <p className="text-sm text-gray-600 mb-4">{goal.description}</p>
                )}
                
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Progresso</span>
                    <span className="font-medium">
                      {goal.current_value.toLocaleString('pt-BR')} / {goal.target_value.toLocaleString('pt-BR')}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        progress >= 100 ? 'bg-green-500' : 
                        progress >= 75 ? 'bg-blue-500' : 
                        progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{goal.goal_type} • {goal.period_type}</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    {new Date(goal.start_date).toLocaleDateString('pt-BR')} - {new Date(goal.end_date).toLocaleDateString('pt-BR')}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Modal de Criação/Edição */}
        {showModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingGoal ? 'Editar Meta' : 'Nova Meta'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Título *</label>
                    <input
                      type="text"
                      required
                      className="input-field"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Descrição</label>
                    <textarea
                      className="input-field"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Valor Alvo *</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      required
                      className="input-field"
                      value={formData.target_value}
                      onChange={(e) => setFormData({ ...formData, target_value: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Valor Atual</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      className="input-field"
                      value={formData.current_value}
                      onChange={(e) => setFormData({ ...formData, current_value: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tipo de Meta</label>
                    <select
                      className="input-field"
                      value={formData.goal_type}
                      onChange={(e) => setFormData({ ...formData, goal_type: e.target.value })}
                    >
                      <option value="sales">Vendas</option>
                      <option value="leads">Leads</option>
                      <option value="revenue">Receita</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Período</label>
                    <select
                      className="input-field"
                      value={formData.period_type}
                      onChange={(e) => setFormData({ ...formData, period_type: e.target.value })}
                    >
                      <option value="monthly">Mensal</option>
                      <option value="quarterly">Trimestral</option>
                      <option value="yearly">Anual</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Data de Início *</label>
                    <input
                      type="date"
                      required
                      className="input-field"
                      value={formData.start_date}
                      onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Data de Fim *</label>
                    <input
                      type="date"
                      required
                      className="input-field"
                      value={formData.end_date}
                      onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                    />
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowModal(false)}
                      className="btn-secondary"
                    >
                      Cancelar
                    </button>
                    <button type="submit" className="btn-primary">
                      {editingGoal ? 'Atualizar' : 'Criar'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
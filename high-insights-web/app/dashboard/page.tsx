'use client'

import { useEffect, useState } from 'react'
import Layout from '@/components/Layout'
import api from '@/lib/api'
import { 
  DollarSign, 
  Users, 
  TrendingUp, 
  Target, 
  Calendar,
  UserCheck,
  BarChart3
} from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts'

interface DashboardKPIs {
  total_revenue: number
  total_leads: number
  conversion_rate: number
  monthly_sales: number
  goals_achieved: number
  total_goals: number
  pending_followups: number
  total_team_members: number
}

export default function DashboardPage() {
  const [kpis, setKpis] = useState<DashboardKPIs | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchKPIs()
  }, [])

  const fetchKPIs = async () => {
    try {
      const response = await api.get('/dashboard/kpis')
      setKpis(response.data)
    } catch (error) {
      console.error('Erro ao buscar KPIs:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  if (!kpis) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-gray-500">Erro ao carregar dados do dashboard</p>
        </div>
      </Layout>
    )
  }

  const kpiCards = [
    {
      title: 'Receita Total',
      value: `R$ ${kpis.total_revenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Total de Leads',
      value: kpis.total_leads.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Taxa de Conversão',
      value: `${kpis.conversion_rate}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Vendas do Mês',
      value: `R$ ${kpis.monthly_sales.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      icon: BarChart3,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      title: 'Metas Atingidas',
      value: `${kpis.goals_achieved}/${kpis.total_goals}`,
      icon: Target,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    },
    {
      title: 'Follow-ups Pendentes',
      value: kpis.pending_followups.toString(),
      icon: Calendar,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      title: 'Membros da Equipe',
      value: kpis.total_team_members.toString(),
      icon: UserCheck,
      color: 'text-teal-600',
      bgColor: 'bg-teal-100'
    }
  ]

  // Dados de exemplo para gráficos
  const salesData = [
    { name: 'Jan', vendas: 4000 },
    { name: 'Fev', vendas: 3000 },
    { name: 'Mar', vendas: 5000 },
    { name: 'Abr', vendas: 4500 },
    { name: 'Mai', vendas: 6000 },
    { name: 'Jun', vendas: 5500 },
  ]

  const leadsData = [
    { name: 'Novos', value: 45, color: '#3B82F6' },
    { name: 'Contatados', value: 30, color: '#10B981' },
    { name: 'Qualificados', value: 15, color: '#F59E0B' },
    { name: 'Convertidos', value: 10, color: '#EF4444' },
  ]

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Visão geral dos principais indicadores da sua empresa
          </p>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi) => (
            <div key={kpi.title} className="card">
              <div className="flex items-center">
                <div className={`flex-shrink-0 p-3 rounded-md ${kpi.bgColor}`}>
                  <kpi.icon className={`h-6 w-6 ${kpi.color}`} />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {kpi.title}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {kpi.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Gráficos */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Gráfico de Vendas */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Vendas por Mês</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`R$ ${value.toLocaleString('pt-BR')}`, 'Vendas']} />
                <Bar dataKey="vendas" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Gráfico de Leads */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Status dos Leads</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={leadsData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {leadsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Resumo das Metas */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Progresso das Metas</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Metas Atingidas</span>
              <span className="text-sm text-gray-500">
                {kpis.goals_achieved} de {kpis.total_goals}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full"
                style={{ width: `${(kpis.goals_achieved / kpis.total_goals) * 100}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500">
              {((kpis.goals_achieved / kpis.total_goals) * 100).toFixed(1)}% das metas foram atingidas
            </p>
          </div>
        </div>
      </div>
    </Layout>
  )
}
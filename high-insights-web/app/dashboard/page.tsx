'use client'

import Layout from '@/components/Layout'
import api from '@/lib/api'
import {
  BarChart3,
  Calendar,
  DollarSign,
  Target,
  TrendingUp,
  UserCheck,
  Users
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { Bar, BarChart, CartesianGrid, Cell, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

interface DashboardKPIs {
  total_revenue: number
  total_leads: number
  conversion_rate: number
  monthly_sales: number
  goals_achieved: number
  total_goals: number
  pending_followups: number
  total_team_members: number
}

export interface DashboardGraphs {
  leads_resume: LeadsResume[]
  lastSixMonthResume: LastSixMonthResume[]
}

export interface LeadsResume {
  name: string
  value: number
  color: string
}

export interface LastSixMonthResume {
  name: string
  vendas: number
}


export default function DashboardPage() {
  const [kpis, setKpis] = useState<DashboardKPIs | null>(null)
  const [salesData, setSalesData] = useState<LastSixMonthResume[]>([])
  const [leadsData, setLeadsData] = useState<LeadsResume[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchKPIs()
  }, [])

  const fetchKPIs = async () => {
    try {

      api.interceptors.request.use((config) => {
        const token = typeof window !== 'undefined' ? localStorage.getItem("token") || '' : '';
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          config.headers['company-id'] = typeof window !== 'undefined' ? localStorage.getItem("company_id") || '' : '';
          config.headers['is-admin'] = typeof window !== 'undefined' ? localStorage.getItem("is_admin") || '' : '';
        }
        return config;
      });

      const response = await api.get('/dashboard/kpis')
      const responseGraphs = await api.get('/dashboard/graphs')
      setSalesData(responseGraphs.data.lastSixMonthResume)
      setLeadsData(responseGraphs.data.leads_resume)
      setKpis(response.data)
    } catch (error) {
      console.error('Erro ao buscar KPIs:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    )
  }

  if (!kpis) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-gray-500">Erro ao carregar dados do dashboard</p>
        </div>
      </Layout>
    )
  }

  const kpiCards = [
    {
      title: 'Receita Total',
      value: `R$ ${(kpis.total_revenue || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Total de Leads',
      value: (kpis.total_leads || 0).toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Taxa de Conversão',
      value: `${kpis.conversion_rate}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Vendas do Mês',
      value: `R$ ${(kpis.monthly_sales || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      icon: BarChart3,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      title: 'Metas Atingidas',
      value: `${kpis.goals_achieved}/${kpis.total_goals}`,
      icon: Target,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    },
    {
      title: 'Follow-ups Pendentes',
      value: (kpis.pending_followups || 0).toString(),
      icon: Calendar,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      title: 'Membros da Equipe',
      value: (kpis.total_team_members || 0).toString(),
      icon: UserCheck,
      color: 'text-teal-600',
      bgColor: 'bg-teal-100'
    }
  ]


  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Visão geral dos principais indicadores da sua empresa
          </p>
        </div>

        
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {kpiCards.map((kpi) => (
            <div key={kpi.title} className="card">
              <div className="flex items-center">
                <div className={`flex-shrink-0 p-3 rounded-md ${kpi.bgColor}`}>
                  <kpi.icon className={`h-6 w-6 ${kpi.color}`} />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {kpi.title}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {kpi.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          ))}
        </div>

       
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
         
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Vendas por Mês</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`R$ ${value.toLocaleString('pt-BR')}`, 'Vendas']} />
                <Bar dataKey="vendas" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Gráfico de Leads */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Status dos Leads</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={leadsData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {leadsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Resumo das Metas */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Progresso das Metas</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Metas Atingidas</span>
              <span className="text-sm text-gray-500">
                {kpis.goals_achieved} de {kpis.total_goals}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full"
                style={{ width: `${(kpis.goals_achieved / kpis.total_goals) * 100}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500">
              {((kpis.goals_achieved / kpis.total_goals) * 100).toFixed(1)}% das metas foram atingidas
            </p>
          </div>
        </div>
      </div>
    </Layout>
  )
}
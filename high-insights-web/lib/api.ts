import axios from "axios";

const api = axios.create({
  baseURL: "http://localhost:8000/",
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use((config) => {
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI4IiwiY29tcGFueV9pZCI6OCwiZXhwIjoxNzU4NzM4OTQzfQ.yOW5ViwWIrF7yb_NH4fEmeZLeXkjrmyEkwIP7rI7HDs";
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api;
import axios from "axios";

const api = axios.create({
  baseURL: "https://api-high-insights.highcapital.io/",
  headers: {
    "Content-Type": "application/json",
    "company-id": typeof window !== 'undefined' ? localStorage.getItem("company_id") || '' : '',
    "is-admin": typeof window !== 'undefined' ? localStorage.getItem("is_admin") || '' : '',
  },
});

api.interceptors.request.use((config) => {
  const token = typeof window !== 'undefined' ? localStorage.getItem("token") || '' : '';
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api;
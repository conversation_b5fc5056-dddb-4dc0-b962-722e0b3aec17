const authService = {
  isAuthenticated: () => {
    if (typeof window === 'undefined') return false
    return true
  },
  setToken: (token: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', token)
    }
  },
  clearToken: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token')
    }
  },
  getCompany: () => {
    if (typeof window === 'undefined') return null
    const company = localStorage.getItem('company')
    return 1
  }
}

export { authService }
import { redirect } from "next/navigation";
import api from "./api";

const authService = {
  isAuthenticated: () => {
    if (typeof window === 'undefined') return false
    const token = localStorage.getItem('token');
    return !!token
  },
  setToken: (token: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', token)
    }
  },
  clearToken: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token')
    }
  },
  getCompany: () => {
    if (typeof window === 'undefined') return null
    const company = localStorage.getItem('company')
    return company ? JSON.parse(company) : null
  },
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('https://api-high-insights.highcapital.io/auth/login', credentials);
    return response.data
  },
  logout: () => {
    localStorage.removeItem('token')
    localStorage.removeItem('company')
    redirect('/login')
  }
}

export { authService };

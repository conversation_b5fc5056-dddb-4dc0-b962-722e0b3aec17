import { redirect } from "next/navigation";
import api from "./api";


export interface Profile {
  id: number;
  name: string;
}


  export const profileOptions: Profile[] = [
      { id: 1, name: 'High webinar' },
      { id: 2, name: 'High rec' },
      { id: 3, name: 'High copy' },
      { id: 4, name: 'High capital' },
      { id: 5, name: 'High finance' },
      { id: 6, name: 'High stage' },
      { id: 7, name: 'High pulse' },
      { id: 8, name: 'High agents' },
      { id: 0, name: 'Geral' },
    ];


const authService = {
  isAuthenticated: () => {
    if (typeof window === 'undefined') return false;
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') || '' : '';
    return !!token;
  },
  
  setToken: (token: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', token);
    }
  },
  
  clearToken: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
    }
  },
  
  getCompany: () => {
    if (typeof window === 'undefined') return null;
    const company = typeof window !== 'undefined' ? localStorage.getItem('company') || '' : '';
    return company ? JSON.parse(company) : null;
  },
  
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('https://api-high-insights.highcapital.io/auth/login', credentials);
    api.defaults.headers['Authorization'] = `Bearer ${response.data.access_token}`;
    api.defaults.headers['company-id'] = response.data.company.id;
    api.defaults.headers['is-admin'] = response.data.is_global_admin;

    return response.data;
  },
  
  logout: () => {
    if (typeof window !== 'undefined') {
    localStorage.removeItem('token');
    localStorage.removeItem('company');
    localStorage.removeItem('profile');
    localStorage.removeItem('is_admin');
    localStorage.removeItem('company_id');
  }
    redirect('/login');
  },
  
  getProfile: (): Profile => {
    if (typeof window === 'undefined') return profileOptions[0];
    const profile = typeof window !== 'undefined' ? localStorage.getItem('profile') || '' : '';
    return profile ? JSON.parse(profile) : profileOptions[0];
  },
  
 
};

export { authService };

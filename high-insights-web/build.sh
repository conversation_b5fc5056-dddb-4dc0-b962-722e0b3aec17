#!/bin/bash

# <PERSON>ript para build e deploy da aplicação Next.js

echo "🚀 Iniciando build da aplicação..."

# Limpar builds anteriores
echo "🧹 Limpando builds anteriores..."
rm -rf .next out

# Instalar dependências
echo "📦 Instalando dependências..."
npm install --legacy-peer-deps

# Build da aplicação Next.js
echo "🔨 Fazendo build da aplicação..."
npm run build

# Verificar se o build foi bem-sucedido
if [ $? -eq 0 ]; then
    echo "✅ Build concluído com sucesso!"
    echo "📁 Arquivos estáticos gerados em: ./out"
    echo "🖼️  Imagens públicas copiadas para: ./out/assests"
    
    # Opcional: Build da imagem Docker
    read -p "🐳 Deseja fazer build da imagem Docker? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🐳 Fazendo build da imagem Docker..."
        echo "📋 Escolha a versão do Dockerfile:"
        echo "1) Dockerfile (principal - simplificado)"
        echo "2) Dockerfile.fixed (mais robusto)"
        echo "3) Dockerfile.simple (mais simples)"
        echo "4) Dockerfile.minimal (mínimo - evita problemas de cópia)"
        read -p "Escolha (1-4): " -n 1 -r dockerfile_choice
        echo

        case $dockerfile_choice in
            1)
                dockerfile_name="Dockerfile"
                ;;
            2)
                dockerfile_name="Dockerfile.fixed"
                ;;
            3)
                dockerfile_name="Dockerfile.simple"
                ;;
            4)
                dockerfile_name="Dockerfile.minimal"
                ;;
            *)
                dockerfile_name="Dockerfile"
                ;;
        esac

        echo "📋 Usando $dockerfile_name"
        docker build -f $dockerfile_name -t high-insights-web . --no-cache

        if [ $? -eq 0 ]; then
            echo "✅ Imagem Docker criada com sucesso!"
            echo "🚀 Para executar: docker run -p 80:80 high-insights-web"
            echo "🔍 Para testar: curl http://localhost/health"
            echo "🖼️  Imagens disponíveis em: http://localhost/assests/"
        else
            echo "❌ Erro ao criar imagem Docker com $dockerfile_name"
            echo "💡 Tente uma das outras opções de Dockerfile"
            exit 1
        fi
    fi
else
    echo "❌ Erro no build da aplicação"
    exit 1
fi

echo "🎉 Processo concluído!"

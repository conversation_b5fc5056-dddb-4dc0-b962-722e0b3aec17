# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next
out
build

# Important: DO NOT ignore public folder - it contains static assets
# public

# Environment files
.env
.env.local
.env.development
.env.production
.env*.local

# Testing
coverage
.nyc_output

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile.*
Dockerfile-*
docker-compose*
*.dockerfile

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary folders
tmp
temp

# Documentation
README.md
docs

# CI/CD
.github

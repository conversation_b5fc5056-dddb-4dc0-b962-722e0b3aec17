name: Code Quality

on:
  pull_request:
    branches:
      - main
      - release
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  code-analysis:
    name: Code Quality Analysis
    runs-on: ubuntu-latest

    permissions:
      contents: read
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build for analysis
      run: dotnet build --configuration Release --no-restore

    - name: Run .NET Format check
      id: format-check
      continue-on-error: true
      run: |
        echo "## 🎨 Code Formatting Analysis" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if dotnet format --verify-no-changes --verbosity diagnostic 2>&1 | tee format-output.txt; then
          echo "✅ **Code Formatting**: All files are properly formatted" >> $GITHUB_STEP_SUMMARY
          echo "format-status=success" >> $GITHUB_OUTPUT
        else
          echo "❌ **Code Formatting**: Issues found" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Count formatting issues
          ISSUES_COUNT=$(grep -c "error WHITESPACE" format-output.txt || echo "0")
          FILES_COUNT=$(grep "Formatted code file" format-output.txt | wc -l || echo "0")

          echo "- **Files with issues**: $FILES_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- **Total formatting issues**: $ISSUES_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Fix with**: \`dotnet format\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "format-status=failed" >> $GITHUB_OUTPUT
        fi

    - name: Security scan with .NET Security
      id: security-scan
      continue-on-error: true
      run: |
        echo "## 🔒 Security Analysis" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        dotnet list package --vulnerable --include-transitive 2>&1 | tee security-report.txt

        if grep -q "has the following vulnerable packages" security-report.txt; then
          echo "❌ **Security Scan**: Vulnerable packages found" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Vulnerable packages detected:**" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          grep -A 10 "has the following vulnerable packages" security-report.txt >> $GITHUB_STEP_SUMMARY || true
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          echo "security-status=failed" >> $GITHUB_OUTPUT
        else
          echo "✅ **Security Scan**: No vulnerable packages found" >> $GITHUB_STEP_SUMMARY
          echo "security-status=success" >> $GITHUB_OUTPUT
        fi

    - name: Dockerfile security scan
      id: dockerfile-scan
      continue-on-error: true
      uses: hadolint/hadolint-action@v3.1.0
      with:
        dockerfile: Dockerfile
        failure-threshold: warning

    - name: Process Dockerfile scan results
      if: always()
      run: |
        echo "## 🐳 Dockerfile Analysis" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if [ "${{ steps.dockerfile-scan.outcome }}" == "success" ]; then
          echo "✅ **Dockerfile Scan**: No issues found" >> $GITHUB_STEP_SUMMARY
          echo "dockerfile-status=success" >> $GITHUB_OUTPUT
        else
          echo "⚠️ **Dockerfile Scan**: Issues detected" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Check the Dockerfile scan step above for detailed issues." >> $GITHUB_STEP_SUMMARY
          echo "dockerfile-status=warning" >> $GITHUB_OUTPUT
        fi

    - name: Run CodeQL Analysis
      id: codeql-init
      continue-on-error: true
      uses: github/codeql-action/init@v3
      with:
        languages: csharp

    - name: Autobuild
      id: codeql-build
      continue-on-error: true
      if: steps.codeql-init.outcome == 'success'
      uses: github/codeql-action/autobuild@v3

    - name: Perform CodeQL Analysis
      id: codeql-analyze
      continue-on-error: true
      if: steps.codeql-build.outcome == 'success'
      uses: github/codeql-action/analyze@v3

    - name: Process CodeQL results
      if: always()
      run: |
        echo "## 🔍 Static Code Analysis (CodeQL)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if [ "${{ steps.codeql-analyze.outcome }}" == "success" ]; then
          echo "✅ **CodeQL Analysis**: Completed successfully" >> $GITHUB_STEP_SUMMARY
          echo "codeql-status=success" >> $GITHUB_OUTPUT
        elif [ "${{ steps.codeql-init.outcome }}" == "failure" ]; then
          echo "❌ **CodeQL Analysis**: Failed to initialize" >> $GITHUB_STEP_SUMMARY
          echo "codeql-status=failed" >> $GITHUB_OUTPUT
        elif [ "${{ steps.codeql-build.outcome }}" == "failure" ]; then
          echo "❌ **CodeQL Analysis**: Build failed" >> $GITHUB_STEP_SUMMARY
          echo "codeql-status=failed" >> $GITHUB_OUTPUT
        else
          echo "⚠️ **CodeQL Analysis**: Analysis incomplete" >> $GITHUB_STEP_SUMMARY
          echo "codeql-status=warning" >> $GITHUB_OUTPUT
        fi

    - name: Generate Code Quality Summary
      if: always()
      run: |
        echo "## 📊 Final Code Quality Report" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Get individual statuses
        FORMAT_STATUS="${{ steps.format-check.outputs.format-status }}"
        SECURITY_STATUS="${{ steps.security-scan.outputs.security-status }}"
        DOCKERFILE_STATUS="${{ steps.dockerfile-scan.outputs.dockerfile-status }}"
        CODEQL_STATUS="${{ steps.codeql-analyze.outputs.codeql-status }}"

        # Count issues
        ISSUES=0
        WARNINGS=0

        echo "| Check | Status | Result |" >> $GITHUB_STEP_SUMMARY
        echo "|-------|--------|--------|" >> $GITHUB_STEP_SUMMARY

        # Format check
        if [ "$FORMAT_STATUS" == "success" ]; then
          echo "| Code Formatting | ✅ | Passed |" >> $GITHUB_STEP_SUMMARY
        else
          echo "| Code Formatting | ❌ | Failed |" >> $GITHUB_STEP_SUMMARY
          ISSUES=$((ISSUES + 1))
        fi

        # Security check
        if [ "$SECURITY_STATUS" == "success" ]; then
          echo "| Security Scan | ✅ | Passed |" >> $GITHUB_STEP_SUMMARY
        else
          echo "| Security Scan | ❌ | Failed |" >> $GITHUB_STEP_SUMMARY
          ISSUES=$((ISSUES + 1))
        fi

        # Dockerfile check
        if [ "$DOCKERFILE_STATUS" == "success" ]; then
          echo "| Dockerfile Lint | ✅ | Passed |" >> $GITHUB_STEP_SUMMARY
        elif [ "$DOCKERFILE_STATUS" == "warning" ]; then
          echo "| Dockerfile Lint | ⚠️ | Warning |" >> $GITHUB_STEP_SUMMARY
          WARNINGS=$((WARNINGS + 1))
        else
          echo "| Dockerfile Lint | ❌ | Failed |" >> $GITHUB_STEP_SUMMARY
          ISSUES=$((ISSUES + 1))
        fi

        # CodeQL check
        if [ "$CODEQL_STATUS" == "success" ]; then
          echo "| Static Analysis | ✅ | Passed |" >> $GITHUB_STEP_SUMMARY
        elif [ "$CODEQL_STATUS" == "warning" ]; then
          echo "| Static Analysis | ⚠️ | Warning |" >> $GITHUB_STEP_SUMMARY
          WARNINGS=$((WARNINGS + 1))
        else
          echo "| Static Analysis | ❌ | Failed |" >> $GITHUB_STEP_SUMMARY
          ISSUES=$((ISSUES + 1))
        fi

        echo "" >> $GITHUB_STEP_SUMMARY

        # Overall status
        if [ $ISSUES -eq 0 ] && [ $WARNINGS -eq 0 ]; then
          echo "🎉 **Overall Status**: ALL CHECKS PASSED" >> $GITHUB_STEP_SUMMARY
        elif [ $ISSUES -eq 0 ]; then
          echo "⚠️ **Overall Status**: PASSED WITH WARNINGS ($WARNINGS warnings)" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Overall Status**: FAILED ($ISSUES issues, $WARNINGS warnings)" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Quick Fixes:**" >> $GITHUB_STEP_SUMMARY

        if [ "$FORMAT_STATUS" != "success" ]; then
          echo "- Run \`dotnet format\` to fix formatting issues" >> $GITHUB_STEP_SUMMARY
        fi

        if [ "$SECURITY_STATUS" != "success" ]; then
          echo "- Update vulnerable packages with \`dotnet add package <package> --version <safe-version>\`" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Commit**: \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Workflow**: [View Details](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY

        # Set final exit code based on critical issues
        if [ $ISSUES -gt 0 ]; then
          echo "❌ Code quality check failed with $ISSUES critical issues"
          exit 1
        fi

    - name: Set job status output
      if: always()
      id: status
      run: |
        echo "status=${{ job.status }}" >> $GITHUB_OUTPUT
        echo "emoji=${{ job.status == 'success' && '✅' || '❌' }}" >> $GITHUB_OUTPUT

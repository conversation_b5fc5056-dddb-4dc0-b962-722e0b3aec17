using FluentAssertions;
using HighCapital.Core.Domain.Entities;
using NUnit.Framework;
using Shouldly;


[TestFixture]
public class UserTests
{
    [Test]
    public void User_WhenCreated_ShouldHaveNullOrEmptyValues()
    {
        // Act
        var user = new User();

        // Assert
      
        user.Name.Should().BeNullOrEmpty();
        user.Phone.Should().BeNullOrEmpty();
        user.Email.Should().BeNullOrEmpty();
        user.Id.Should().ShouldNotBeNull();
    }

    [Test]
    public void User_WhenPropertiesSet_ShouldRetainValues()
    {
        // Arrange
        const int expectedId = 123;
        const string expectedName = "John";
        const string expectedPhone = "+5522996043721";
        const string expectedEmail = "<EMAIL>";

        // Act
        var user = new User
        {
            Id = expectedId,
            Name = expectedName,
            Phone = expectedPhone,
            Email = expectedEmail
        };

        // Assert
        user.Id.Should().Be(expectedId);
        user.Name.Should().Be(expectedName);
        user.Phone.Should().Be(expectedPhone);
        user.Email.Should().Be(expectedEmail);
    }
    

    [Test]
    public void User_WhenCompared_ShouldUseIdForEquality()
    {
        // Arrange
        var user1 = new User { Id = 1, Name = "Peter", Phone = "+5522996043721" };
        var user2 = new User { Id = 1, Name = "Rony", Phone = "+5521996043721" };
        var user3 = new User { Id = 3, Name = "John", Phone = "+5594996043721" };

        // Act & Assert
        user1.Should().BeEquivalentTo(user2, options => options.Including(u => u.Id));
        user1.Should().NotBeEquivalentTo(user3, options => options.Including(u => u.Id));
    }
}

using FluentAssertions;
using HighCapital.AuthenticationService.Application.Common.Models;
using NUnit.Framework;

namespace HighCapital.AuthenticationService.Application.UnitTests.Common.Models;

[TestFixture]
public class ResultTests
{
    [Test]
    public void Success_ShouldCreateSuccessfulResult()
    {
        // Act
        var result = Result.Success();

        // Assert
        result.Succeeded.Should().BeTrue();
        result.Failed.Should().BeFalse();
        result.Errors.Should().BeEmpty();
        result.FirstError.Should().BeNull();
    }

    [Test]
    public void Failure_WithSingleError_ShouldCreateFailedResult()
    {
        // Arrange
        const string errorMessage = "Something went wrong";

        // Act
        var result = Result.Failure(errorMessage);

        // Assert
        result.Succeeded.Should().BeFalse();
        result.Failed.Should().BeTrue();
        result.Errors.Should().ContainSingle().Which.Should().Be(errorMessage);
        result.FirstError.Should().Be(errorMessage);
    }

    [Test]
    public void Failure_WithMultipleErrors_ShouldCreateFailedResult()
    {
        // Arrange
        var errors = new[] { "Error 1", "Error 2", "Error 3" };

        // Act
        var result = Result.Failure(errors);

        // Assert
        result.Succeeded.Should().BeFalse();
        result.Failed.Should().BeTrue();
        result.Errors.Should().Equal(errors);
        result.FirstError.Should().Be("Error 1");
    }

    [Test]
    public void ImplicitConversion_FromString_ShouldCreateFailedResult()
    {
        // Arrange
        const string errorMessage = "Implicit error";

        // Act
        Result result = errorMessage;

        // Assert
        result.Succeeded.Should().BeFalse();
        result.Errors.Should().ContainSingle().Which.Should().Be(errorMessage);
    }

    [Test]
    public void ToString_WhenSuccessful_ShouldReturnSuccess()
    {
        // Arrange
        var result = Result.Success();

        // Act
        var stringResult = result.ToString();

        // Assert
        stringResult.Should().Be("Success");
    }

    [Test]
    public void ToString_WhenFailed_ShouldReturnFirstError()
    {
        // Arrange
        const string errorMessage = "Test error";
        var result = Result.Failure(errorMessage);

        // Act
        var stringResult = result.ToString();

        // Assert
        stringResult.Should().Be("Failed: Test error");
    }
}

[TestFixture]
public class ResultGenericTests
{
    [Test]
    public void Success_WithValue_ShouldCreateSuccessfulResult()
    {
        // Arrange
        const int expectedValue = 42;

        // Act
        var result = Result<int>.Success(expectedValue);

        // Assert
        result.Succeeded.Should().BeTrue();
        result.Failed.Should().BeFalse();
        result.Value.Should().Be(expectedValue);
        result.Errors.Should().BeEmpty();
    }

    [Test]
    public void Failure_WithError_ShouldCreateFailedResult()
    {
        // Arrange
        const string errorMessage = "Something failed";

        // Act
        var result = Result<string>.Failure(errorMessage);

        // Assert
        result.Succeeded.Should().BeFalse();
        result.Failed.Should().BeTrue();
        result.Value.Should().BeNull();
        result.Errors.Should().ContainSingle().Which.Should().Be(errorMessage);
    }

    [Test]
    public void ImplicitConversion_FromValue_ShouldCreateSuccessfulResult()
    {
        // Arrange
        const string expectedValue = "Test value";

        // Act
        Result<string> result = Result<string>.Success(expectedValue);

        // Assert
        result.Succeeded.Should().BeTrue();
        result.Value.Should().Be(expectedValue);
    }

    [Test]
    public void ImplicitConversion_FromString_ShouldCreateFailedResult()
    {
        // Arrange
        const string errorMessage = "Implicit error";

        // Act
        Result<int> result = errorMessage;

        // Assert
        result.Succeeded.Should().BeFalse();
        result.Errors.Should().ContainSingle().Which.Should().Be(errorMessage);
    }

    [Test]
    public void ToString_WhenSuccessful_ShouldReturnValueString()
    {
        // Arrange
        var result = Result<int>.Success(123);

        // Act
        var stringResult = result.ToString();

        // Assert
        stringResult.Should().Be("Success: 123");
    }

    [Test]
    public void ToString_WhenFailed_ShouldReturnFirstError()
    {
        // Arrange
        const string errorMessage = "Test error";
        var result = Result<int>.Failure(errorMessage);

        // Act
        var stringResult = result.ToString();

        // Assert
        stringResult.Should().Be("Failed: Test error");
    }
}

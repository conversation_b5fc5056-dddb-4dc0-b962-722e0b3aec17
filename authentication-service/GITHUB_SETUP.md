# GitHub Repository Setup

Este guia explica como configurar o repositório GitHub para que os workflows funcionem com permissões limitadas.

## 🔐 Configurações de Permissões

### ✅ Configuração Atual (Sem Permissões de Admin)

Os workflows foram configurados para funcionar apenas com o `GITHUB_TOKEN` padrão e permissões mínimas:

- **Não requer** permissões de administrador
- **Não requer** configurações especiais no repositório
- **Funciona** com permissões padrão de colaborador

### 📊 Como Ver os Resultados

Em vez de comentários nos PRs, os resultados são exibidos:

1. **GitHub Step Summary**: Resumo detalhado na aba "Summary" do workflow
2. **Workflow Status**: Status checks visíveis no PR
3. **Logs Detalhados**: Logs completos disponíveis no GitHub Actions

### 2. Configurar Branch Protection Rules

Vá para **Settings** > **Branches** e configure regras para as branches `main` e `release`:

#### Para branch `main`:
- ✅ **Require a pull request before merging**
- ✅ **Require status checks to pass before merging**
  - Adicione: `Build, Test and Validate`
  - Adicione: `Code Quality Analysis`
- ✅ **Require branches to be up to date before merging**
- ✅ **Restrict pushes that create files larger than 100MB**

#### Para branch `release`:
- ✅ **Require a pull request before merging**
- ✅ **Require status checks to pass before merging**
- ✅ **Require branches to be up to date before merging**

## 🔑 Secrets Necessários

Configure os seguintes secrets em **Settings** > **Secrets and variables** > **Actions**:

### Repository Secrets:
- `GCP_SA_KEY`: JSON da Service Account do Google Cloud

### Environment Secrets (opcional):
Se você criou um environment chamado "production":
- `GCP_SA_KEY`: JSON da Service Account do Google Cloud

## 🚀 Fluxo de Trabalho Recomendado

### Para Desenvolvimento:
1. Crie uma branch a partir de `main`
2. Faça suas alterações
3. Abra um Pull Request para `main`
4. Os workflows de CI executam automaticamente
5. Após aprovação, merge para `main`

### Para Deploy:
1. Crie um Pull Request de `main` para `release`
2. Após merge para `release`, o deploy automático acontece

## 🔧 Troubleshooting

### Erro: "Resource not accessible by integration"
**Solução**: Verifique se as permissões do GitHub Actions estão configuradas corretamente (passo 1 acima).

### Erro: "Required status check is expected"
**Solução**: 
1. Execute os workflows pelo menos uma vez
2. Depois configure as branch protection rules
3. Os status checks aparecerão automaticamente

### Erro de autenticação no Google Cloud
**Solução**: Verifique se o secret `GCP_SA_KEY` está configurado corretamente com o JSON completo da Service Account.

### Workflows não executam em PRs de forks
**Solução**: Configure "Run workflows from fork pull requests" nas configurações do Actions.

## 📋 Checklist de Configuração

- [ ] Permissões do GitHub Actions configuradas
- [ ] Secret `GCP_SA_KEY` adicionado
- [ ] Branch protection rules configuradas
- [ ] Workflows testados com um PR de exemplo
- [ ] Deploy testado na branch `release`

## 🎯 Status Checks Esperados

Quando tudo estiver configurado, você verá estes status checks nos PRs:

- ✅ **Build, Test and Validate** (ci.yml)
- ✅ **Code Quality Analysis** (code-quality.yml)
- ✅ **CodeQL** (análise de segurança)

## 📞 Suporte

Se encontrar problemas:

1. Verifique os logs dos workflows no GitHub Actions
2. Confirme se todas as permissões estão configuradas
3. Teste com um PR simples primeiro
4. Verifique se o cluster GKE está acessível

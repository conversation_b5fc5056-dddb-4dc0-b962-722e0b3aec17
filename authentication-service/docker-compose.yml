services:
#   web:
#     build:
#       context: .
#       dockerfile: Dockerfile
#     ports:
#       - "5000:8080"
#     depends_on:
#       - postgres
#     environment:
#       - ASPNETCORE_ENVIRONMENT=Development
#       - ASPNETCORE_URLS=http://+:8080
#       - ConnectionStrings__HighCapital.AuthenticationServiceDb=Host=postgres;Database=HighCapital.AuthenticationServiceDb;Username=postgres;Password=postgres123
#       - SKIP_DB_INIT=false
#     networks:
#       - app-network

  postgres:
    image: postgres:16-alpine
    environment:
      - POSTGRES_DB=HighCapital.AuthenticationServiceDb
      - POSTGRES_USER=high_capital_dev
      - POSTGRES_PASSWORD=YUli3z5([ZEJ%UOZ
    ports:
      - "5432:5432"
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge

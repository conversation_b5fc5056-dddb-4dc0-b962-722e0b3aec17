using HighCapital.AuthenticationService.Application.Common.Interfaces;
using Microsoft.AspNetCore.Mvc;
using HighCapital.AuthenticationService.Domain.Dtos;
using Google.Apis.Auth;
using HighCapital.AuthenticationService.Infrastructure.ExternalServices;

namespace HighCapital.AuthenticationService.Api.Controllers.Auth;

[ApiController]
[Route("api/v1/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly GoogleOAuthService _googleOAuthService;

    public AuthController(IUserService userService, GoogleOAuthService googleOAuthService)
    {
        _userService = userService;
        _googleOAuthService = googleOAuthService;
    }

    [HttpPost("register")]
    public async Task<ActionResult> Register([FromBody] CreateUserDto request)
    {
        var result = await _userService.RegisterUserAsync(request);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { message = "User registered successfully", userId = result.Value });
    }

    [HttpPost("login")]
    public async Task<ActionResult> Login([FromBody] LoginDto request)
    {
        var result = await _userService.LoginUserAsync(request);

        if (!result.Succeeded || result.Value == null)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { token = result.Value });
    }

    [HttpGet("login/google/redirect")]
    public IActionResult RedirectToGoogle()
    {
        var url = "https://accounts.google.com/o/oauth2/v2/auth?client_id=************-dpd7qmpieh9j32akglvd1s2r6nmjnvjd.apps.googleusercontent.com&redirect_uri=http://localhost:5000/api/v1/Auth/login/google/callback&response_type=code&scope=openid email profile&access_type=offline";
        return Redirect(url);
    }

    [HttpPost("login/google")]
    public async Task<ActionResult> LoginWithGoogle([FromBody] GoogleLoginDto request)
    {
        var result = await _userService.LoginWithGoogleAsync(request.IdToken);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { token = result.Value });
    }
    
    [HttpGet("login/google/callback")]
    public async Task<IActionResult> GoogleCallback([FromQuery] string code)
        {
          try
        {
        Console.WriteLine($"code recebido: {code}");

        var tokenResponse = await _googleOAuthService.ExchangeCodeForTokensAsync(code);
        var result = await _userService.LoginWithGoogleAsync(tokenResponse.IdToken);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { token = result.Value });
        }
        catch (Exception ex)
        {
        Console.WriteLine($"Erro no callback do Google: {ex.Message}");
        return BadRequest(new { error = ex.Message });
    }
        }
    }

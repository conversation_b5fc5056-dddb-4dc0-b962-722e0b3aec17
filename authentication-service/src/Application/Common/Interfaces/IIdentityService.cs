using HighCapital.AuthenticationService.Application.Common.Models;
using HighCapital.AuthenticationService.Domain.Dtos;
namespace HighCapital.AuthenticationService.Application.Common.Interfaces;

public interface IIdentityService
{
    Task<string?> GetUserNameAsync(string userId);

    Task<bool> IsInRoleAsync(string userId, string role);

    Task<bool> AuthorizeAsync(string userId, string policyName);

    Task<(Result Result, string UserId)> CreateUserAsync(CreateUserDto data);

    Task<Result> DeleteUserAsync(string userId);

    Task<(Result Result, string UserId, string? Email, IEnumerable<string> Roles)> AuthenticateAsync(LoginDto data);
}

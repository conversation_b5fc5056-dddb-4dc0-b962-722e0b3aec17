using System.Reflection;
using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Services;
using HighCapital.Core.Infrastructure;
using HighCapital.Core.Infrastructure.Database;
using HighCapital.Core.Infrastructure.Repository;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;


namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddApplicationServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddAutoMapper(Assembly.GetExecutingAssembly());
        builder.Services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        
        builder.Services.AddScoped<UserRepository>();
        // Register Application Services
        builder.Services.AddScoped<IUserService, UserService>();
        // Add more services as needed
    }
}

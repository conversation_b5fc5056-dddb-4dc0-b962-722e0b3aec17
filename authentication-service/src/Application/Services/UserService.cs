using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Common.Models;
using HighCapital.AuthenticationService.Domain.Dtos;
using HighCapital.Core.Domain.Entities;
using HighCapital.Core.Services;
using HighCapital.Core.Infrastructure.Database;
using HighCapital.Core.Infrastructure.Repository;
using Microsoft.Extensions.Logging;
using Google.Apis.Auth;

namespace HighCapital.AuthenticationService.Application.Services;

public class UserService : IUserService
{
    private readonly CoreDbContext _context;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly UserRepository _userRepository;
    private readonly ILogger<UserService> _logger;
    public UserService(
        CoreDbContext context,
        IJwtTokenService jwtTokenService,
        UserRepository userRepository,
        ILogger<UserService> logger)
    {
        _context = context;
        _jwtTokenService = jwtTokenService;
        _userRepository = userRepository;
        _logger = logger;
    }

    public async Task<Result<int>> RegisterUserAsync(CreateUserDto data)
    {
        try
        {
            _logger.LogInformation("Attempting to register user with email: {Email}", data.Email);

            var isValidEmail = ValidationHelperService.IsValidEmail(data.Email);
            var isValidPhone = ValidationHelperService.IsValidPhoneNumber(data.Phone);
            if (!isValidEmail || !isValidPhone)
            {
                return Result<int>.Failure(new[] { "Error to register user, Email or Phone invalid" });
            }

            var existingUser = await _userRepository.GetByEmailAsync(data.Email);

            if (existingUser != null)
            {
                return Result<int>.Failure(new[] { "User with this email already exists." });
            }

            var passwordHash = new HashService().Sha256(data.Password);

            var user = new User
            {
                Name = data.Name,
                Email = data.Email,
                Password = passwordHash,
                Phone = data.Phone,
                ExternalId =  Guid.NewGuid()
            };

            await _userRepository.InsertAsync(user);

            _logger.LogInformation("User registered successfully with ID: {UserId}", user.Id);

            return Result<int>.Success(user.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while registering user with email: {Email}", data.Email);
            return Result<int>.Failure(new[] { "An error occurred while registering the user." });
        }
    }

    public async Task<Result<string>> LoginUserAsync(LoginDto data)
    {
        try
        {
            _logger.LogInformation("Attempting to login user with email: {Email}", data.Email);

            // Generate JWT token
            var token = await _jwtTokenService.GenerateTokenAsync(data.Email);

            _logger.LogInformation("User logged in successfully: {Email}", data.Email);

            return Result<string>.Success(token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while logging in user with email: {Email}", data.Email);
            return Result<string>.Failure(new[] { "An error occurred during login." });
        }
    }

    public async Task<Result<User?>> GetUserByIdAsync(int id)
    {
        try
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == id);


            //await new version of core to be publish
            //var user = await _userRepository.SelectAsync(id);

            return Result<User?>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting user by ID: {UserId}", id);
            return Result<User?>.Failure(new[] { "An error occurred while retrieving the user." });
        }
    }

    public async Task<Result<User?>> GetUserByEmailAsync(string email)
    {
        try
        {
            var user = await _userRepository.GetByEmailAsync(email);

            return Result<User?>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting user by email: {Email}", email);
            return Result<User?>.Failure(new[] { "An error occurred while retrieving the user." });
        }
    }

    public async Task<Result<IEnumerable<User>>> GetAllUsersAsync()
    {
        try
        {
            var users = await _userRepository.SelectAsync();
            return Result<IEnumerable<User>>.Success(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all users");
            return Result<IEnumerable<User>>.Failure(new[] { "An error occurred while retrieving users." });
        }
    }

    public async Task<Result> UpdateUserAsync(int id, CreateUserDto data)
    {
        try
        {

            var user = await _userRepository.SelectAsync(id);
  
            if (user == null)
            {
                return Result.Failure(new[] { "User not found." });
            }

            if (user.Email != data.Email)
            {
                var existingUser = await _userRepository.GetByEmailAsync(data.Email);

                if (existingUser != null)
                {
                    return Result.Failure(new[] { "Email is already in use." });
                }
            }

            var passwordHash = new HashService().Sha256(data.Password);

            var userData = new User
            {
                Name = data.Name,
                Email = data.Email,
                Password = passwordHash,
                Phone = data.Phone,
                ExternalId = new Guid()
            };



            await _userRepository.UpdateAsync(userData,id);

            _logger.LogInformation("User updated successfully: {UserId}", id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating user: {UserId}", id);
            return Result.Failure(new[] { "An error occurred while updating the user." });
        }
    }

    public async Task<Result> DeleteUserAsync(int id)
    {
        try
        {

            var user = await _userRepository.SelectAsync(id);
   
            if (user == null)
            {
                return Result.Failure(new[] { "User not found." });
            }

            await _userRepository.DeleteAsync(id);
        
            _logger.LogInformation("User deleted successfully: {UserId}", id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting user: {UserId}", id);
            return Result.Failure(new[] { "An error occurred while deleting the user." });
        }
    }

    public async Task<Result<string>> LoginWithGoogleAsync(string idToken)
    {
        try
        {
            var payload = await GoogleJsonWebSignature.ValidateAsync(idToken);
            var email = payload.Email;

            var user = await _userRepository.GetByEmailAsync(payload.Email);

            if (user == null)
            {
                user = new User
                {
                    Email = email,
                    Name = payload.Name ?? email,
                    Phone = "",
                    Password = "",
                };

                await _userRepository.InsertAsync(user);
            }
            
            var token = await _jwtTokenService.GenerateTokenAsync(user.Email);
            if (token == null)
            {
                return Result<string>.Failure(new[] { "Error to Generete jwt with google login." });
            }

            return Result<string>.Success(token);
       
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging in with Google token");
            return Result<string>.Failure(new[] { "Invalid Google token." });
        }
        
    }

}

{"info": {"_postman_id": "whatsapp-integration-service", "name": "WhatsApp Integration Service", "description": "Collection para testar os endpoints do serviço de integração WhatsApp", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "WhatsApp", "item": [{"name": "Test Connection", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/whatsapp/test", "host": ["{{baseUrl}}"], "path": ["api", "whatsapp", "test"]}, "description": "Testa se o serviço está funcionando"}, "response": []}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"number\": \"5511999999999\",\n    \"text\": \"Ol<PERSON>! Esta é uma mensagem de teste do serviço de integração WhatsApp.\",\n    \"delay\": 1000\n}"}, "url": {"raw": "{{baseUrl}}/api/whatsapp/{{instanceName}}/send-message", "host": ["{{baseUrl}}"], "path": ["api", "whatsapp", "{{instanceName}}", "send-message"]}, "description": "Envia uma mensagem de texto para um número do WhatsApp"}, "response": []}, {"name": "Get QR Code", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/whatsapp/{{instanceName}}/qrcode", "host": ["{{baseUrl}}"], "path": ["api", "whatsapp", "{{instanceName}}", "qrcode"]}, "description": "Obtém o QR Code para conectar uma instância do WhatsApp"}, "response": []}, {"name": "Get Connection Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/whatsapp/{{instanceName}}/connection-status", "host": ["{{baseUrl}}"], "path": ["api", "whatsapp", "{{instanceName}}", "connection-status"]}, "description": "Consulta o status da conexão de uma instância do WhatsApp"}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "https://localhost:7000", "description": "URL base do serviço de integração WhatsApp"}, {"key": "instanceName", "value": "my-whatsapp-instance", "description": "Nome da instância do WhatsApp"}]}
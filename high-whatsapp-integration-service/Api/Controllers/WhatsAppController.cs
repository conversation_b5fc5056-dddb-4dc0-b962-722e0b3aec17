using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WhatsAppController : ControllerBase
{
    private readonly ILogger<WhatsAppController> _logger;
    private readonly HttpClient _httpClient;

    public WhatsAppController(ILogger<WhatsAppController> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    /// <summary>
    /// Envia uma mensagem de texto para um número do WhatsApp
    /// </summary>
    /// <param name="instanceName">Nome da instância do WhatsApp</param>
    /// <param name="request">Dados da mensagem a ser enviada</param>
    /// <returns>Resultado do envio da mensagem</returns>
    [HttpPost("{instanceName}/send-message")]
    public async Task<IActionResult> SendMessage(
        string instanceName,
        [FromBody] SendMessageRequestDto request
    )
    {
        _logger.LogInformation(
            "Received request to send message to {Number} via instance {InstanceName}",
            request.Number,
            instanceName
        );

        try
        {
            // Configurar HttpClient para Evolution API
            var baseUrl = "http://************/";
            var apiKey = "e8df3c66-e837-469a-899c-441cc0dbde08";

            _httpClient.BaseAddress = new Uri(baseUrl);
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("apikey", apiKey);

            // Preparar payload para Evolution API
            var payload = new
            {
                number = request.Number,
                text = request.Text,
                delay = request.Delay ?? 1000,
            };

            var json = System.Text.Json.JsonSerializer.Serialize(payload);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            // Fazer chamada para Evolution API
            var response = await _httpClient.PostAsync($"message/sendText/{instanceName}", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = new
                {
                    Success = true,
                    Message = "Message sent successfully",
                    Data = new
                    {
                        InstanceName = instanceName,
                        Number = request.Number,
                        Text = request.Text,
                        MessageId = Guid.NewGuid().ToString(),
                        EvolutionResponse = responseContent,
                    },
                };
                return Ok(result);
            }
            else
            {
                return BadRequest(
                    new
                    {
                        Success = false,
                        Message = "Failed to send message",
                        Error = responseContent,
                    }
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error sending message to {Number} via instance {InstanceName}",
                request.Number,
                instanceName
            );

            return StatusCode(
                500,
                new
                {
                    Success = false,
                    Message = "Internal server error",
                    Error = ex.Message,
                }
            );
        }
    }

    /// <summary>
    /// Obtém o QR Code para conectar uma instância do WhatsApp
    /// </summary>
    /// <param name="instanceName">Nome da instância do WhatsApp</param>
    /// <returns>QR Code em base64</returns>
    [HttpGet("{instanceName}/qrcode")]
    public async Task<IActionResult> GetQrCode(string instanceName)
    {
        _logger.LogInformation(
            "Received request to get QR code for instance {InstanceName}",
            instanceName
        );

        // TODO: Implementar integração com Evolution API
        var response = new
        {
            Success = true,
            Message = "QR Code generated successfully",
            Data = new
            {
                InstanceName = instanceName,
                QrCode = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                Status = "disconnected",
            },
        };

        return Ok(response);
    }

    /// <summary>
    /// Consulta o status da conexão de uma instância do WhatsApp
    /// </summary>
    /// <param name="instanceName">Nome da instância do WhatsApp</param>
    /// <returns>Status da conexão</returns>
    [HttpGet("{instanceName}/connection-status")]
    public async Task<IActionResult> GetConnectionStatus(string instanceName)
    {
        _logger.LogInformation(
            "Received request to get connection status for instance {InstanceName}",
            instanceName
        );

        // TODO: Implementar integração com Evolution API
        var response = new
        {
            Success = true,
            Message = "Connection status retrieved successfully",
            Data = new
            {
                InstanceName = instanceName,
                Status = "connected",
                IsConnected = true,
                PhoneNumber = "+5511999999999",
            },
        };

        return Ok(response);
    }

    [HttpGet("test")]
    public IActionResult Test()
    {
        return Ok("WhatsApp Controller is working!");
    }
}

public class SendMessageRequestDto
{
    public string Number { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public int? Delay { get; set; }
}

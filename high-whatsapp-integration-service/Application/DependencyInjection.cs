using Infrastructure.Dependencies;
using Infrastructure.WhatsApp.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Dependencies;

public static class ApplicationDependencyInjection
{
    public static IServiceCollection AddApplicationServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.AddMenssegerServices(configuration);
        services.AddWhatsAppServices(configuration);
        return services;
    }
}

# WhatsApp Integration Service

Serviço de integração com WhatsApp utilizando Evolution API v2.2.2, desenvolvido em .NET 9 seguindo os princípios de Clean Architecture.

## 🚀 Funcionalidades

- ✅ **Envio de Mensagens**: Enviar mensagens de texto para números do WhatsApp
- ✅ **QR Code**: Gerar QR Code para conectar instâncias do WhatsApp
- ✅ **Status de Conexão**: Consultar o status da conexão de instâncias do WhatsApp
- ✅ **API REST**: Endpoints RESTful para todas as funcionalidades
- ✅ **Documentação OpenAPI**: Swagger/Scalar para documentação automática
- ✅ **Collection Postman**: Collection pronta para testes

## 🏗️ Arquitetura

O projeto segue os princípios de Clean Architecture com as seguintes camadas:

```
├── Api/                    # Camada de apresentação (Controllers, Program.cs)
├── Application/            # Camada de aplicação (Services, Use Cases)
├── Domain/                 # Camada de domínio (Entities, DTOs, Interfaces)
├── Infrastructure/         # Camada de infraestrutura (External APIs, Extensions)
```

## 📋 Pré-requisitos

- .NET 9 SDK
- Evolution API v2.2.2 configurada e rodando
- Postman (opcional, para testes)

## ⚙️ Configuração

1. **Clone o repositório**
2. **Configure o appsettings.json** no projeto Api:
```json
{
  "EvolutionApi": {
    "BaseUrl": "http://localhost:8080",
    "GlobalApiKey": "sua-api-key-aqui"
  }
}
```

3. **Execute o projeto**:
```bash
dotnet run --project Api
```

## 🔗 Endpoints

### Base URL: `http://localhost:5230/api/whatsapp`

| Método | Endpoint | Descrição |
|--------|----------|-----------|
| GET | `/test` | Testa se o serviço está funcionando |
| POST | `/{instanceName}/send-message` | Envia mensagem de texto |
| GET | `/{instanceName}/qrcode` | Obtém QR Code para conexão |
| GET | `/{instanceName}/connection-status` | Consulta status da conexão |

### Exemplos de Uso

#### Enviar Mensagem
```bash
POST /api/whatsapp/my-instance/send-message
Content-Type: application/json

{
  "number": "5511999999999",
  "text": "Olá! Esta é uma mensagem de teste.",
  "delay": 1000
}
```

#### Obter QR Code
```bash
GET /api/whatsapp/my-instance/qrcode
```

#### Consultar Status
```bash
GET /api/whatsapp/my-instance/connection-status
```

## 📚 Documentação

- **Swagger UI**: `http://localhost:5230/swagger`
- **Scalar UI**: `http://localhost:5230/scalar/v1`

## 🧪 Testes

Importe a collection `WhatsApp Integration Service.postman_collection.json` no Postman para testar todos os endpoints.

### Variáveis da Collection:
- `baseUrl`: `http://localhost:5230`
- `instanceName`: `my-whatsapp-instance`

## 🔧 Status Atual

### ✅ Implementado
- Estrutura completa do projeto com Clean Architecture
- Controllers REST com todos os endpoints solicitados
- DTOs para requests e responses
- Interfaces para serviços
- Configuração de dependências
- Collection Postman para testes
- Documentação OpenAPI/Swagger

### 🚧 Próximos Passos
- Integração real com Evolution API (atualmente usando dados mock)
- Implementação dos serviços de infraestrutura
- Tratamento de erros e validações
- Testes unitários e de integração
- Logging estruturado

## 🛠️ Tecnologias Utilizadas

- .NET 9
- ASP.NET Core Web API
- Clean Architecture
- OpenAPI/Swagger
- Scalar Documentation
- Evolution API v2.2.2

## 📝 Notas

O serviço está atualmente retornando dados mock para permitir testes da estrutura da API. A integração real com a Evolution API será implementada na próxima fase do desenvolvimento.

using Domain.WhatsApp.Interfaces;
using Infrastructure.WhatsApp.Extensions;
using Infrastructure.WhatsApp.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Dependencies;

public static class InfrastructureDependencyInjection
{
    public static IServiceCollection AddMenssegerServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.AddScoped<IEvolutionApiService, EvolutionApiService>();
        services.AddScoped<IWhatsAppIntegrationService, WhatsAppIntegrationService>();
        services.AddWhatsAppServices(configuration);
        return services;
    }
}

﻿namespace Domain.WhatsApp.Entities;

public class WhatsAppMessage
{
    public string Number { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public int? Delay { get; set; }
}

public class WhatsAppConnection
{
    public string InstanceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsConnected { get; set; }
}

public class WhatsAppQrCode
{
    public string Base64 { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
}

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import logging

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://high-capital-dev:YUli3z5([ZEJ%UOZ@localhost:5432/high-insigts")

# Configurar logging para SQLAlchemy (mostra todas as queries SQL)
if os.getenv("DEBUG") == "True":
    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# Para PostgreSQL, não precisamos de configurações especiais
engine = create_engine(
    DATABASE_URL,
    echo=os.getenv("DEBUG") == "True"  # Echo SQL queries quando DEBUG=True
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import Admin, TeamMember
from schemas import TeamMemberCreate, TeamMemberUpdate, TeamMemberResponse
from auth import get_current_admin

router = APIRouter()

@router.get("/", response_model=List[TeamMemberResponse])
async def get_team_members(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    team_members = db.query(TeamMember).filter(TeamMember.company_id == current_admin.company_id).all()
    return team_members

@router.post("/", response_model=TeamMemberResponse)
async def create_team_member(
    team_member_data: TeamMemberCreate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    team_member = TeamMember(
        company_id=current_admin.company_id,
        **team_member_data.dict()
    )
    db.add(team_member)
    db.commit()
    db.refresh(team_member)
    return team_member

@router.get("/{team_member_id}", response_model=TeamMemberResponse)
async def get_team_member(
    team_member_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    team_member = db.query(TeamMember).filter(
        TeamMember.id == team_member_id,
        TeamMember.company_id == current_admin.company_id
    ).first()
    
    if not team_member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Membro da equipe não encontrado"
        )
    
    return team_member

@router.put("/{team_member_id}", response_model=TeamMemberResponse)
async def update_team_member(
    team_member_id: int,
    team_member_data: TeamMemberUpdate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    team_member = db.query(TeamMember).filter(
        TeamMember.id == team_member_id,
        TeamMember.company_id == current_admin.company_id
    ).first()
    
    if not team_member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Membro da equipe não encontrado"
        )
    
    update_data = team_member_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(team_member, field, value)
    
    db.commit()
    db.refresh(team_member)
    return team_member

@router.delete("/{team_member_id}")
async def delete_team_member(
    team_member_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    team_member = db.query(TeamMember).filter(
        TeamMember.id == team_member_id,
        TeamMember.company_id == current_admin.company_id
    ).first()
    
    if not team_member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Membro da equipe não encontrado"
        )
    
    db.delete(team_member)
    db.commit()
    return {"message": "Membro da equipe deletado com sucesso"}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: high-insights-service-hpa
  labels:
    app: high-insights-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: high-insights-service-deployment
  minReplicas: 1
  maxReplicas: 1
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60

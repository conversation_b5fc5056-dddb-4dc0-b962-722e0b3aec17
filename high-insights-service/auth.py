from datetime import datetime, timedelta
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from models import Admin, Company
from database import get_db
import os

SECRET_KEY = os.getenv("JWT_SECRET", "your-super-secret-jwt-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> dict:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token inválido",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_admin(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Admin:
    token = credentials.credentials
    payload = verify_token(token)
    
    admin_id: int = payload.get("sub")
    if admin_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token inválido",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    admin = db.query(Admin).filter(Admin.id == admin_id).first()
    if admin is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Admin não encontrado",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return admin

def authenticate_admin(db: Session, username: str, password: str) -> Optional[Admin]:
    from debug_utils import debug_query_result, debug_model_data
    import os
    
    # 🔍 Query para buscar admin por username
    admin_query = db.query(Admin).filter(Admin.username == username)
    
    # 🐛 DEBUG: Query do admin
    if os.getenv("DEBUG") == "True":
        debug_query_result(str(admin_query), admin_query.all(), f"Busca admin por username: {username}")
    
    admin = admin_query.first()
    
    # 🐛 DEBUG: Admin encontrado
    if os.getenv("DEBUG") == "True":
        debug_model_data(admin, f"Admin encontrado para '{username}'")
    
    if not admin:
        return None
    if not verify_password(password, admin.password_hash):
        if os.getenv("DEBUG") == "True":
            print(f"🔒 Senha incorreta para o usuário: {username}")
        return None
    return admin
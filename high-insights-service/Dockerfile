# Try using a different registry or approach
FROM alpine:3.18

WORKDIR /app

# Install system dependencies and Python
RUN apk add --no-cache \
    python3 \
    py3-pip \
    gcc \
    musl-dev \
    curl \
    && ln -sf python3 /usr/bin/python \
    && ln -sf pip3 /usr/bin/pip

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user for security
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser:appuser /app
USER appuser

# Tornar o script executável
RUN chmod +x /app/seed_database.py

EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/docs || exit 1

# Comando para iniciar o servidor (sem --reload para produção)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
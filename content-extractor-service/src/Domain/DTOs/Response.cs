using System.Text.Json.Serialization;

namespace Domain.DTOs;

public class Response<TData>
{
    private readonly int _code;
    public TData? Data { get; set; }
    public string? Message { get; set; }
    public string? Ex { get; set; }

    [JsonConstructor]
    public Response() => _code = Configuration.DefaultStatusCode;

    public Response(TData? data, int code = Configuration.DefaultStatusCode, string? message = null)
    {
        Data = data;
        _code = code;
        Message = message;
    }
    
    public Response(TData? data, int code = Configuration.DefaultStatusCode, string? message = null, string? ex = null)
    {
        Ex = ex;
        Data = data;
        _code = code;
        Message = message;
    }
    
    [JsonIgnore] public bool IsSucess => _code is >= 200 and < 300;
}
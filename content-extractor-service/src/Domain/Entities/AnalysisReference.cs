using Domain.Interfaces;
using Google.Cloud.Firestore;

namespace Domain.Entities;

public class AnalysisReference<TData> where TData : class, ITypeFactory
{
    [FirestoreProperty] public int Id { get; set; }
    [FirestoreProperty] public int RefenceId { get; set; }
    [FirestoreProperty] public AnalysisResponse<TData>? AnalysisResponse { get; set; }
    public Reference Reference { get; set; } = null!;
    
    public AnalysisReference(int refenceId, AnalysisResponse<TData>? analysisResponse)
    {
        Id = Convert.ToInt32(HighCapital.Core.Services.IdGenService.GetId());
        RefenceId = refenceId;
        AnalysisResponse = analysisResponse;
    }
}
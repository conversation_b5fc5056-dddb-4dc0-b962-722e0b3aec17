using Domain.Enums;
using Domain.Interfaces;
using Google.Cloud.Firestore;
using HighCapital.Core.Services;

namespace Domain.Entities;

public class Reference
{
    [FirestoreProperty] public int Id { get; set; }
    [FirestoreProperty] public TypeEnum Type { get; set; }
    [FirestoreProperty] public string[]? Websites { get; set; }
    [FirestoreProperty] public string[]? Filesname { get; set; }

    public Reference(TypeEnum type, string[]? websites, string[]? filesname)
    {
        Id = Convert.ToInt32(IdGenService.GetId());
        Type = type;
        Websites = websites;
        Filesname = filesname;
    }
}
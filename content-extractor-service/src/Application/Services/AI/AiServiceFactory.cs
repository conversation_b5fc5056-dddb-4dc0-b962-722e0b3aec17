using Domain.Interfaces.Factory;
using Domain.Interfaces.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Services.AI;

public class AiServiceFactory(IServiceProvider provider) : IAiServiceFactory
{
    private readonly IServiceProvider _serviceProvider = provider ?? throw new ArgumentNullException("",nameof(provider));
    public IGenerativeAiService CreateService(string providerName)
    {
        return providerName.ToLowerInvariant() switch
        {
            "chatgpt" or "openai" => _serviceProvider.GetRequiredService<ChatGptService>(),
            "gemini" or "google" => _serviceProvider.GetRequiredService<GeminiService>(),
            _ => throw new NotSupportedException($"The provider '{providerName}' is not supported.")
        };
    }
}
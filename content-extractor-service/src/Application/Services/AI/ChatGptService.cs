using System.Text;
using System.Text.Json;
using Domain;
using Domain.DTOs;
using Domain.Interfaces.Services;
using HighCapital.Core.Domain.Entities.HighCopy.DTOs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Application.Services.AI;

public class ChatGptService : IGenerativeAiService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiKey;
    private readonly ILogger<ChatGptService> _logger;
    private const string BaseUrl = Configuration.ChatGptDefaultUrl;

    public ChatGptService(HttpClient httpClient, IConfiguration configuration, ILogger<ChatGptService> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _apiKey = configuration.GetConnectionString("OPENAI_API_KEY")
                  ?? throw new ArgumentException("OPENAI_API_KEY not configured in appsettings.json.");
        
        _httpClient.Timeout = TimeSpan.FromSeconds(30);
    }

    public async Task<Response<AiResponse>> GenerateAsync(GenerateContentRequest generateContentRequest)
    {
        if (string.IsNullOrWhiteSpace(generateContentRequest.Prompt))
            throw new ArgumentException("Prompt cannot be empty.", nameof(generateContentRequest.Prompt));

        try
        {
            var requestBody = new ChatGptRequest
            {
                Model = Configuration.OpenAiModel,
                Messages = new[]
                {
                    new ChatGptMessage { Role = "system", Content = Configuration.DefaultContext},
                    new ChatGptMessage { Role = "user", Content = generateContentRequest.Prompt }
                }
            };

            var jsonBody = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

            using var request = new HttpRequestMessage(HttpMethod.Post, BaseUrl);
            request.Headers.Add("Authorization", $"Bearer {_apiKey}");
            request.Content = content;

            _logger.LogInformation("Sending request to ChatGPT API");

            var response = await _httpClient.SendAsync(request);
            
            if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
            {
                _logger.LogWarning("ChatGPT API returned 429 (Rate Limit). Fallback will be used.");
                throw new InvalidOperationException("AI service communication failure - Rate limit reached");
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.ServiceUnavailable ||
                response.StatusCode == System.Net.HttpStatusCode.BadGateway ||
                response.StatusCode == System.Net.HttpStatusCode.GatewayTimeout)
            {
                _logger.LogWarning("ChatGPT API unavailable (Status: {StatusCode}). Fallback will be used.", response.StatusCode);
                throw new InvalidOperationException($"AI service communication failure - Service unavailable ({response.StatusCode})");
            }

            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var chatGptResponse = JsonSerializer.Deserialize<ChatGptResponse>(responseBody);

            var generatedText = chatGptResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty;
            
            _logger.LogInformation("Response received successfully from ChatGPT");

            var data = new AiResponse
            {
                Content = generatedText,
                Provider = Configuration.OpenAiModel
            };
            
            return new Response<AiResponse>(data, 200, "Content generated successfully.");
        }
        catch (Exception e)
        {
            _logger.LogError("Error generating content with ChatGpt: {Message}", e.Message);
            throw new ApplicationException("Error generating content with ChatGpt");
        }
    }
}
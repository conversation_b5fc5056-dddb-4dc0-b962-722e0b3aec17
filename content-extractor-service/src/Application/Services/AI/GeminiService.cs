using Domain;
using Domain.DTOs;
using Domain.Interfaces.Services;
using GenerativeAI;
using GenerativeAI.Types;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;


namespace Application.Services.AI;

public abstract class GeminiService : IGenerativeAiService
{
    private readonly GenerativeModel _generativeModel;
    private readonly ILogger<GeminiService> _logger;

    protected GeminiService(IConfiguration configuration, ILogger<GeminiService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        var apiKey = configuration.GetValue("Gemini:ApiKey", string.Empty) ?? throw new ArgumentNullException("Gemini API key is not configured.");

        var client = new GoogleAi(apiKey);
        _generativeModel = client.CreateGenerativeModel(Configuration.GeminiModel);
    }
    public async Task<Response<AiResponse>> GenerateAsync(HighCapital.Core.Domain.Entities.HighCopy.DTOs.GenerateContentRequest generateContentRequest)
    {
        try
        {
            var contents = new List<Content>();
            
            contents.Add(new Content(prompt: Configuration.DefaultContext, role: "system"));
            contents.Add(new Content(prompt: generateContentRequest.Prompt, role: "user"));

            var request = new GenerateContentRequest
            {
                Contents = contents,
                GenerationConfig = new GenerationConfig
                {
                    Temperature = 0.7f,
                    ResponseMimeType = "JSON"
                }
            };

            var response = await _generativeModel.GenerateContentAsync(request);
            var data = new AiResponse
            {
                Content = response.Text,
                Provider = response.ModelVersion ?? generateContentRequest.PreferredProvider!
            };
            return new Response<AiResponse>(data, 200, "Content generated successfully");
        }
        catch (Exception e)
        {
            _logger.LogError("Error generating content with Gemini: {Message}", e.Message);
            throw new ApplicationException("Error generating content with Gemini");
        }
    }
}
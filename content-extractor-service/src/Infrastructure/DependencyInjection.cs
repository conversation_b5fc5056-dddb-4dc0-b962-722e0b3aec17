using Domain;
using Domain.Interfaces.Repositories;
using Infrastructure.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Google.Cloud.Firestore;
using Microsoft.CodeAnalysis;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        services.AddScoped(typeof(IAnalysisReferenceRepository<>), typeof(AnalysisReferenceRepository<>));
        services.AddScoped<IReferenceRepository, ReferenceRepository>();
        return services;
    }
}
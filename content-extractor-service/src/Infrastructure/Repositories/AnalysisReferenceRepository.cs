using Domain.Entities;
using Domain.Interfaces;
using Domain.Interfaces.Repositories;
using Google.Cloud.Firestore;

namespace Infrastructure.Repositories;

public class AnalysisReferenceRepository<TData>(FirestoreDb firestoreDb) : IAnalysisReferenceRepository<TData>
    where TData : class, ITypeFactory
{
    private readonly FirestoreDb _firestoreDb = firestoreDb;
    private const string CollectionName = "AnalysisReferences";
    private readonly CollectionReference _collection = firestoreDb.Collection(CollectionName);

    public async Task AddAsync(AnalysisReference<TData> analysisReference)
    {
        await _collection.Document(analysisReference.Id.ToString()).SetAsync(analysisReference);
    }

    public async Task<AnalysisReference<TData>> GetByIdAsync(int id)
    {
        var analysis = await _collection.Document(id.ToString()).GetSnapshotAsync();
        return analysis.ConvertTo<AnalysisReference<TData>>();
    }

    public async Task<AnalysisReference<TData>> GetByReferenceIdAsync(int referenceId)
    {
        var analysis = await _collection.WhereEqualTo("RefenceId", referenceId).Limit(1).GetSnapshotAsync();
        return analysis.Documents[0].ConvertTo<AnalysisReference<TData>>();
    }

    public async Task DeleteAsync(int id)
    {
        await _collection.Document(id.ToString()).DeleteAsync();
    }
}
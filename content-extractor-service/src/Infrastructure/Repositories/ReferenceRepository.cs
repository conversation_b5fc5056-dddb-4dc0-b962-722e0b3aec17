using Domain.Entities;
using Domain.Interfaces.Repositories;
using Google.Cloud.Firestore;

namespace Infrastructure.Repositories;

public class ReferenceRepository(FirestoreDb firestoreDb) : IReferenceRepository
{
    private readonly FirestoreDb _firestoreDb = firestoreDb;
    private const string CollectionName = "References";
    private readonly CollectionReference _collection = firestoreDb.Collection(CollectionName);
    
    public async Task AddAsync(Reference reference)
    {
        await _collection.Document(reference.Id.ToString()).SetAsync(reference);
    }

    public async Task<Reference?> GetByIdAsync(int id)
    {
        var reference = await _collection.Document(id.ToString()).GetSnapshotAsync();
        return reference.ConvertTo<Reference>();
    }

    public async Task UpdateAsync(Reference reference)
    {
        var docRef = _collection.Document(reference.Id.ToString());
        await docRef.SetAsync(reference, SetOptions.Overwrite);
    }

    public async Task DeleteAsync(int id)
    {
        await _collection.Document(id.ToString()).DeleteAsync();
    }
}
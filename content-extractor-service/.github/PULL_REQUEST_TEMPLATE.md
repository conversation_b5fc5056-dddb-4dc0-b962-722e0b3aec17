# Descrição

Explique de forma resumida o que foi alterado, qual problema foi resolvido e o motivo da mudança. Se houver dependências, liste aqui.

Resolve # (issue)

## Tipo de mudança

Marque apenas o que se aplica:

- [ ] Correção de bug (não quebra funcionalidades existentes)
- [ ] Nova funcionalidade (não quebra funcionalidades existentes)
- [ ] Mudança que pode quebrar algo existente
- [ ] Atualização de documentação

## Como foi testado?

Descreva os testes realizados e como podemos reproduzir.

- [ ] Teste A
- [ ] Teste B

# Implementação de Headers para Company ID e Is Admin

## Resumo das Mudanças

Esta implementação substitui o uso do decorator `@CurrentUser()` por dois novos decorators que extraem informações dos headers HTTP:

- `@CompanyId()`: Extrai o `company_id` do header `company-id`
- `@IsAdmin()`: Extrai o valor booleano do header `is-admin`

## Novos Decorators

### CompanyId Decorator

```typescript
export const CompanyId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const companyId = request.headers['company-id'];

    if (!companyId) {
      throw new Error('Header company-id é obrigatório');
    }

    const parsedCompanyId = parseInt(companyId as string, 10);
    if (isNaN(parsedCompanyId)) {
      throw new Error('Header company-id deve ser um número válido');
    }

    return parsedCompanyId;
  },
);
```

### IsAdmin Decorator

```typescript
export const IsAdmin = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const isAdmin = request.headers['is-admin'];

    if (isAdmin === undefined || isAdmin === null) {
      throw new Error('Header is-admin é obrigatório');
    }

    // Converte string para boolean
    return isAdmin === 'true' || isAdmin === true;
  },
);
```

## Comportamento dos Endpoints GET

A aplicação agora suporta 3 cenários específicos:

### 1. Usuário Normal (company_id > 0 e is-admin = false)

Retorna apenas os registros da empresa especificada no `company-id`.

### 2. Admin Global - Todas as Empresas (company_id = 0 e is-admin = true)

Retorna **todos os registros de todas as empresas** sem filtro.

### 3. Admin Global - Empresa Específica (company_id > 0 e is-admin = true)

Retorna todos os registros da empresa específica (mesmo comportamento que usuário normal, mas com privilégios de admin).

## Headers Obrigatórios

Todas as requisições autenticadas agora devem incluir os seguintes headers:

```http
company-id: 123  # Pode ser 0 para admin global ver todas as empresas
is-admin: false  # true para admin, false para usuário normal
```

## Exemplos de Uso

### 1. Usuário normal - empresa específica

```http
GET /api/leads
Authorization: Bearer <token>
company-id: 1
is-admin: false
```

Retorna apenas os leads da empresa ID 1.

### 2. Admin global - todas as empresas

```http
GET /api/leads
Authorization: Bearer <token>
company-id: 0
is-admin: true
```

Retorna todos os leads de todas as empresas.

### 3. Admin global - empresa específica

```http
GET /api/leads
Authorization: Bearer <token>
company-id: 1
is-admin: true
```

Retorna todos os leads da empresa ID 1 (com privilégios de admin).

## Módulos Atualizados

Os seguintes módulos foram atualizados para usar os novos decorators:

1. **Dashboard** - `DashboardController` e `DashboardService`
2. **Followups** - `FollowupsController` e `FollowupsService`
3. **Leads** - `LeadsController` e `LeadsService`
4. **Goals** - `GoalsController` e `GoalsService`
5. **Preorders** - `PreordersController` e `PreordersService`
6. **Products** - `ProductsController` e `ProductsService`
7. **Sales** - `SalesController` e `SalesService`
8. **Team** - `TeamController` e `TeamService`

## Mudanças nos Services

Todos os métodos `findAll` dos services agora implementam a lógica dos 3 cenários:

```typescript
async findAll(company_id: number, is_admin: boolean = false): Promise<ResponseDto[]> {
  let whereCondition = {};

  if (company_id === 0 && is_admin) {
    // Cenário 2: Admin global - todas as empresas
    whereCondition = {};
  } else if (company_id > 0) {
    // Cenário 1 e 3: Empresa específica (usuário normal ou admin)
    whereCondition = { company_id };
  } else {
    throw new Error('company_id deve ser maior que 0 quando is_admin for false');
  }

  const records = await this.repository.find({
    where: whereCondition,
    order: { created_at: 'DESC' },
  });

  return records.map(record => this.mapToResponseDto(record));
}
```

## Mudanças nos Controllers

Exemplo de como os controllers foram atualizados:

### Antes

```typescript
@Get()
async findAll(@CurrentUser() user: Admin): Promise<ResponseDto[]> {
  return this.service.findAll(user.company_id);
}
```

### Depois

```typescript
@Get()
async findAll(
  @CompanyId() companyId: number,
  @IsAdmin() isAdmin: boolean,
): Promise<ResponseDto[]> {
  return this.service.findAll(companyId, isAdmin);
}
```

## Validação de Headers

Os decorators incluem validação automática:

- `company-id` deve ser um número válido
- `is-admin` deve estar presente (aceita 'true', 'false', true, false)
- Ambos os headers são obrigatórios

## Compatibilidade

Esta implementação mantém a funcionalidade existente para operações que não são GET (POST, PUT, DELETE), que continuam usando apenas o `company_id` para filtrar por empresa.

## Testes

Para testar a implementação, certifique-se de incluir os headers necessários em todas as requisições:

```bash
# Teste como usuário normal
curl -H "Authorization: Bearer <token>" \
     -H "company-id: 1" \
     -H "is-admin: false" \
     http://localhost:3000/api/leads

# Teste como admin global
curl -H "Authorization: Bearer <token>" \
     -H "company-id: 1" \
     -H "is-admin: true" \
     http://localhost:3000/api/leads
```

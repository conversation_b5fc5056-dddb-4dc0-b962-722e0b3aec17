import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Followup } from './followup.entity';

@Entity('team_members')
export class TeamMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  company_id: number;

  @Column({ name: 'name', type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ name: 'email', type: 'varchar', length: 100, nullable: true })
  email: string;

  @Column({ name: 'phone', type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({ name: 'position', type: 'varchar', length: 100, nullable: true })
  position: string;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.teamMembers)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @OneToMany(() => Followup, (followup) => followup.teamMember)
  followups: Followup[];
}

import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Lead } from './lead.entity';
import { Product } from './product.entity';

@Entity('preorders')
export class Preorder {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  companyId: number;

  @Column({ name: 'lead_id', nullable: true })
  leadId: number;

  @Column({ name: 'product_id', nullable: true })
  productId: number;

  @Column({
    name: 'amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: false,
  })
  amount: number;

  @Column({ 
    name: 'status', 
    type: 'varchar', 
    length: 50, 
    default: 'pending',
    nullable: false 
  })
  status: string; // 'pending', 'confirmed', 'cancelled'

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.preorders)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @ManyToOne(() => Lead, (lead) => lead.preorders, { nullable: true })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @ManyToOne(() => Product, (product) => product.preorders, { nullable: true })
  @JoinColumn({ name: 'product_id' })
  product: Product;
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Company } from './company.entity';

@Entity('goals')
export class Goal {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  companyId: number;

  @Column({ type: 'varchar', length: 200, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    name: 'target_value',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: false,
  })
  targetValue: number;

  @Column({
    name: 'current_value',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  currentValue: number;

  @Column({
    name: 'goal_type',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  goalType: string; // 'sales', 'leads', 'revenue'

  @Column({
    name: 'period_type',
    type: 'varchar',
    length: 20,
    nullable: false,
  })
  periodType: string; // 'monthly', 'quarterly', 'yearly'

  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.goals)
  @JoinColumn({ name: 'company_id' })
  company: Company;
}

import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.entity';

@Entity('goals')
export class Goal {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  company_id: number;

  @Column({ type: 'varchar', length: 200, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    name: 'target_value',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: false,
  })
  target_value: number;

  @Column({
    name: 'current_value',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  current_value: number;

  @Column({
    name: 'goal_type',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  goal_type: string; // 'sales', 'leads', 'revenue'

  @Column({
    name: 'period_type',
    type: 'varchar',
    length: 20,
    nullable: false,
  })
  period_type: string; // 'monthly', 'quarterly', 'yearly'

  @Column({ name: 'start_date', type: 'date', nullable: false })
  start_date: Date;

  @Column({ name: 'end_date', type: 'date', nullable: false })
  end_date: Date;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.goals)
  @JoinColumn({ name: 'company_id' })
  company: Company;
}

import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Admin } from './admin.entity';
import { Lead } from './lead.entity';
import { Product } from './product.entity';
import { Sale } from './sale.entity';
import { Goal } from './goal.entity';
import { TeamMember } from './team-member.entity';
import { Followup } from './followup.entity';
import { Preorder } from './preorder.entity';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, unique: true, nullable: false })
  name: string;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relacionamentos
  @OneToMany(() => Admin, (admin) => admin.company)
  admins: Admin[];

  @OneToMany(() => Lead, (lead) => lead.company)
  leads: Lead[];

  @OneToMany(() => Product, (product) => product.company)
  products: Product[];

  @OneToMany(() => Sale, (sale) => sale.company)
  sales: Sale[];

  @OneToMany(() => Goal, (goal) => goal.company)
  goals: Goal[];

  @OneToMany(() => TeamMember, (teamMember) => teamMember.company)
  teamMembers: TeamMember[];

  @OneToMany(() => Followup, (followup) => followup.company)
  followups: Followup[];

  @OneToMany(() => Preorder, (preorder) => preorder.company)
  preorders: Preorder[];
}

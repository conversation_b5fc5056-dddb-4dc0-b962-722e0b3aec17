import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Lead } from './lead.entity';
import { Product } from './product.entity';

@Entity('sales')
export class Sale {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  company_id: number;

  @Column({ name: 'lead_id', nullable: true })
  lead_id: number;

  @Column({ name: 'product_id', nullable: true })
  product_id: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: false,
  })
  amount: number;

  @Column({ name: 'sale_date', type: 'date', nullable: false })
  sale_date: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.sales)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @ManyToOne(() => Lead, (lead) => lead.sales, { nullable: true })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @ManyToOne(() => Product, (product) => product.sales, { nullable: true })
  @JoinColumn({ name: 'product_id' })
  product: Product;
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Lead } from './lead.entity';
import { Product } from './product.entity';

@Entity('sales')
export class Sale {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  companyId: number;

  @Column({ name: 'lead_id', nullable: true })
  leadId: number;

  @Column({ name: 'product_id', nullable: true })
  productId: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: false,
  })
  amount: number;

  @Column({ name: 'sale_date', type: 'date', nullable: false })
  saleDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.sales)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @ManyToOne(() => Lead, (lead) => lead.sales, { nullable: true })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @ManyToOne(() => Product, (product) => product.sales, { nullable: true })
  @JoinColumn({ name: 'product_id' })
  product: Product;
}

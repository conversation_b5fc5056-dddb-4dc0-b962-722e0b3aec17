import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Lead } from './lead.entity';
import { TeamMember } from './team-member.entity';

@Entity('followups')
export class Followup {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  companyId: number;

  @Column({ name: 'lead_id', nullable: true })
  leadId: number;

  @Column({ name: 'team_member_id', nullable: true })
  teamMemberId: number;

  @Column({ name: 'title', type: 'varchar', length: 200, nullable: false })
  title: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'scheduled_date', type: 'timestamp', nullable: false })
  scheduledDate: Date;

  @Column({ 
    name: 'status', 
    type: 'varchar', 
    length: 20, 
    default: 'pending',
    nullable: false 
  })
  status: string; // 'pending', 'complete', 'declined'

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.followups)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @ManyToOne(() => Lead, (lead) => lead.followups, { nullable: true })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @ManyToOne(() => TeamMember, (teamMember) => teamMember.followups, { nullable: true })
  @JoinColumn({ name: 'team_member_id' })
  teamMember: TeamMember;
}

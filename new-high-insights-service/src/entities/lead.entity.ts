import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Sale } from './sale.entity';
import { Followup } from './followup.entity';
import { Preorder } from './preorder.entity';

@Entity('leads')
export class Lead {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id', nullable: false })
  companyId: number;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({
    name: 'social_media',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  socialMedia: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  source: string;

  @Column({ type: 'varchar', length: 50, default: 'new' })
  status: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relacionamentos
  @ManyToOne(() => Company, (company) => company.leads)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @OneToMany(() => Sale, (sale) => sale.lead)
  sales: Sale[];

  @OneToMany(() => Followup, (followup) => followup.lead)
  followups: Followup[];

  @OneToMany(() => Preorder, (preorder) => preorder.lead)
  preorders: Preorder[];
}

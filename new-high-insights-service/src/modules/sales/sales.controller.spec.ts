import { Test, TestingModule } from '@nestjs/testing';
import { SalesController } from './sales.controller';
import { SalesService } from './sales.service';
import { SaleCreateDto } from './dto/sale-create.dto';
import { SaleUpdateDto } from './dto/sale-update.dto';
import { SaleResponseDto } from './dto/sale-response.dto';
import { Admin } from '../../entities/admin.entity';
import { NotFoundException } from '@nestjs/common';

describe('SalesController', () => {
  let controller: SalesController;
  let service: SalesService;

  const mockAdmin: Admin = {
    id: 1,
    companyId: 1,
    username: 'test',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    company: null,
  };

  const mockSaleResponse: SaleResponseDto = {
    id: 1,
    leadId: 1,
    productId: 1,
    amount: 100.50,
    saleDate: new Date('2023-01-01'),
    notes: 'Test sale',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockSalesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SalesController],
      providers: [
        {
          provide: SalesService,
          useValue: mockSalesService,
        },
      ],
    }).compile();

    controller = module.get<SalesController>(SalesController);
    service = module.get<SalesService>(SalesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of sales', async () => {
      const result = [mockSaleResponse];
      mockSalesService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(mockAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(mockAdmin.companyId);
    });
  });

  describe('create', () => {
    it('should create a new sale', async () => {
      const createDto: SaleCreateDto = {
        leadId: 1,
        productId: 1,
        amount: 100.50,
        saleDate: '2023-01-01',
        notes: 'Test sale',
      };

      mockSalesService.create.mockResolvedValue(mockSaleResponse);

      expect(await controller.create(createDto, mockAdmin)).toBe(mockSaleResponse);
      expect(service.create).toHaveBeenCalledWith(createDto, mockAdmin.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single sale', async () => {
      mockSalesService.findOne.mockResolvedValue(mockSaleResponse);

      expect(await controller.findOne(1, mockAdmin)).toBe(mockSaleResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });

    it('should throw NotFoundException when sale not found', async () => {
      mockSalesService.findOne.mockRejectedValue(new NotFoundException('Venda não encontrada'));

      await expect(controller.findOne(999, mockAdmin)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a sale', async () => {
      const updateDto: SaleUpdateDto = {
        amount: 150.75,
        notes: 'Updated sale',
      };

      const updatedSale = { ...mockSaleResponse, ...updateDto };
      mockSalesService.update.mockResolvedValue(updatedSale);

      expect(await controller.update(1, updateDto, mockAdmin)).toBe(updatedSale);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockAdmin.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a sale', async () => {
      mockSalesService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(1, mockAdmin);
      expect(result).toEqual({ message: 'Venda deletada com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });
  });
});

import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { SaleCreateDto } from './dto/sale-create.dto';
import { SaleResponseDto } from './dto/sale-response.dto';
import { SaleUpdateDto } from './dto/sale-update.dto';
import { SalesController } from './sales.controller';
import { SalesService } from './sales.service';

describe('SalesController', () => {
  let controller: SalesController;
  let service: SalesService;

  const mockSaleResponse: SaleResponseDto = {
    id: 1,
    lead_id: 1,
    product_id: 1,
    amount: 100.5,
    sale_date: new Date('2023-01-01'),
    notes: 'Test sale',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockSalesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SalesController],
      providers: [
        {
          provide: SalesService,
          useValue: mockSalesService,
        },
      ],
    }).compile();

    controller = module.get<SalesController>(SalesController);
    service = module.get<SalesService>(SalesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return sales for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const result = [mockSaleResponse];
      mockSalesService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all sales for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const result = [mockSaleResponse];
      mockSalesService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return sales for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const result = [mockSaleResponse];
      mockSalesService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new sale', async () => {
      const createDto: SaleCreateDto = {
        lead_id: 1,
        product_id: 1,
        amount: 100.5,
        sale_date: '2023-01-01',
        notes: 'Test sale',
      };

      mockSalesService.create.mockResolvedValue(mockSaleResponse);

      const companyId = 1;
      expect(await controller.create(createDto, companyId)).toBe(
        mockSaleResponse,
      );
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single sale', async () => {
      const companyId = 1;
      mockSalesService.findOne.mockResolvedValue(mockSaleResponse);

      expect(await controller.findOne(1, companyId)).toBe(mockSaleResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when sale not found', async () => {
      const companyId = 1;
      mockSalesService.findOne.mockRejectedValue(
        new NotFoundException('Venda não encontrada'),
      );

      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a sale', async () => {
      const updateDto: SaleUpdateDto = {
        amount: 150.75,
        notes: 'Updated sale',
      };

      const updatedSale = { ...mockSaleResponse, ...updateDto };
      mockSalesService.update.mockResolvedValue(updatedSale);

      const companyId = 1;
      expect(await controller.update(1, updateDto, companyId)).toBe(
        updatedSale,
      );
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a sale', async () => {
      const companyId = 1;
      mockSalesService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(1, companyId);
      expect(result).toEqual({ message: 'Venda deletada com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

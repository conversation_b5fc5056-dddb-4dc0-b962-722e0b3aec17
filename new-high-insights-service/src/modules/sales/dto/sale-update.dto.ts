import {
  IsOptional,
  IsN<PERSON>ber,
  IsDateString,
  IsString,
  IsPositive,
  Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class SaleUpdateDto {
  @IsOptional()
  @IsNumber({}, { message: 'Lead ID deve ser um número' })
  lead_id?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Product ID deve ser um número' })
  product_id?: number;

  @IsOptional()
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: 'Valor deve ter no máximo 2 casas decimais' },
  )
  @IsPositive({ message: 'Valor deve ser positivo' })
  @Max(99999999.99, { message: 'Valor muito alto' })
  @Transform(({ value }) => parseFloat(value))
  amount?: number;

  @IsOptional()
  @IsDateString({}, { message: 'Data da venda deve ser uma data válida' })
  sale_date?: string;

  @IsOptional()
  @IsString({ message: 'Notas devem ser uma string' })
  notes?: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  company_id?: number;
}

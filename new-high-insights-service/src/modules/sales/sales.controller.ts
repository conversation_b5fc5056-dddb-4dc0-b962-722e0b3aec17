import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { SalesService } from './sales.service';
import { SaleCreateDto } from './dto/sale-create.dto';
import { SaleUpdateDto } from './dto/sale-update.dto';
import { SaleResponseDto } from './dto/sale-response.dto';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('sales')
@UseGuards(JwtAuthGuard)
export class SalesController {
  constructor(private readonly salesService: SalesService) {}

  @Post()
  async create(
    @Body() saleCreateDto: SaleCreateDto,
    @CompanyId() companyId: number,
  ): Promise<SaleResponseDto> {
    return this.salesService.create(saleCreateDto, companyId);
  }

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<SaleResponseDto[]> {
    return this.salesService.findAll(companyId, isAdmin);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<SaleResponseDto> {
    return this.salesService.findOne(id, companyId);
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() saleUpdateDto: SaleUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<SaleResponseDto> {
    return this.salesService.update(id, saleUpdateDto, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    await this.salesService.remove(id, companyId);
    return { message: 'Venda deletada com sucesso' };
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { SalesService } from './sales.service';
import { SaleCreateDto } from './dto/sale-create.dto';
import { SaleUpdateDto } from './dto/sale-update.dto';
import { SaleResponseDto } from './dto/sale-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Admin } from '../../entities/admin.entity';

@Controller('sales')
@UseGuards(JwtAuthGuard)
export class SalesController {
  constructor(private readonly salesService: SalesService) {}

  @Post()
  async create(
    @Body() saleCreateDto: SaleCreateDto,
    @CurrentUser() user: Admin,
  ): Promise<SaleResponseDto> {
    return this.salesService.create(saleCreateDto, user.companyId);
  }

  @Get()
  async findAll(@CurrentUser() user: Admin): Promise<SaleResponseDto[]> {
    return this.salesService.findAll(user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<SaleResponseDto> {
    return this.salesService.findOne(id, user.companyId);
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() saleUpdateDto: SaleUpdateDto,
    @CurrentUser() user: Admin,
  ): Promise<SaleResponseDto> {
    return this.salesService.update(id, saleUpdateDto, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<{ message: string }> {
    await this.salesService.remove(id, user.companyId);
    return { message: 'Venda deletada com sucesso' };
  }
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Sale } from '../../entities/sale.entity';
import { SaleCreateDto } from './dto/sale-create.dto';
import { SaleUpdateDto } from './dto/sale-update.dto';
import { SaleResponseDto } from './dto/sale-response.dto';

@Injectable()
export class SalesService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
  ) {}

  async findAll(companyId: number): Promise<SaleResponseDto[]> {
    const sales = await this.saleRepository.find({
      where: { companyId },
      relations: ['lead', 'product'],
      order: { createdAt: 'DESC' },
    });

    return sales.map((sale) => this.mapToResponseDto(sale));
  }

  async findOne(id: number, companyId: number): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id, companyId },
      relations: ['lead', 'product'],
    });

    if (!sale) {
      throw new NotFoundException('Venda não encontrada');
    }

    return this.mapToResponseDto(sale);
  }

  async create(
    saleCreateDto: SaleCreateDto,
    companyId: number,
  ): Promise<SaleResponseDto> {
    const sale = this.saleRepository.create({
      ...saleCreateDto,
      saleDate: new Date(saleCreateDto.saleDate),
      companyId,
    });

    const savedSale = await this.saleRepository.save(sale);

    // Buscar a venda com relacionamentos
    const saleWithRelations = await this.saleRepository.findOne({
      where: { id: savedSale.id },
      relations: ['lead', 'product'],
    });

    return this.mapToResponseDto(saleWithRelations!);
  }

  async update(
    id: number,
    saleUpdateDto: SaleUpdateDto,
    companyId: number,
  ): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id, companyId },
    });

    if (!sale) {
      throw new NotFoundException('Venda não encontrada');
    }

    // Atualizar campos
    Object.assign(sale, saleUpdateDto);

    if (saleUpdateDto.saleDate) {
      sale.saleDate = new Date(saleUpdateDto.saleDate);
    }

    const updatedSale = await this.saleRepository.save(sale);

    // Buscar a venda com relacionamentos
    const saleWithRelations = await this.saleRepository.findOne({
      where: { id: updatedSale.id },
      relations: ['lead', 'product'],
    });

    return this.mapToResponseDto(saleWithRelations!);
  }

  async remove(id: number, companyId: number): Promise<void> {
    const sale = await this.saleRepository.findOne({
      where: { id, companyId },
    });

    if (!sale) {
      throw new NotFoundException('Venda não encontrada');
    }

    await this.saleRepository.remove(sale);
  }

  private mapToResponseDto(sale: Sale): SaleResponseDto {
    return {
      id: sale.id,
      leadId: sale.leadId,
      productId: sale.productId,
      amount: parseFloat(sale.amount.toString()),
      saleDate: sale.saleDate,
      notes: sale.notes,
      createdAt: sale.createdAt,
      updatedAt: sale.updatedAt,
      lead: sale.lead
        ? {
            id: sale.lead.id,
            name: sale.lead.name,
            email: sale.lead.email,
            phone: sale.lead.phone,
            socialMedia: sale.lead.socialMedia,
            source: sale.lead.source,
            status: sale.lead.status,
            createdAt: sale.lead.createdAt,
            updatedAt: sale.lead.updatedAt,
          }
        : undefined,
      product: sale.product
        ? {
            id: sale.product.id,
            name: sale.product.name,
            price: parseFloat(sale.product.price.toString()),
            createdAt: sale.product.createdAt,
            updatedAt: sale.product.updatedAt,
          }
        : undefined,
    };
  }
}

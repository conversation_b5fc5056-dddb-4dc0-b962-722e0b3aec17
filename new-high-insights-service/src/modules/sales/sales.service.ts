import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Sale } from '../../entities/sale.entity';
import { SaleCreateDto } from './dto/sale-create.dto';
import { SaleResponseDto } from './dto/sale-response.dto';
import { SaleUpdateDto } from './dto/sale-update.dto';
import { Lead } from '../../entities/lead.entity';
import { Goal } from '../../entities/goal.entity';

@Injectable()
export class SalesService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
    @InjectRepository(Goal)
    private goalsRepository: Repository<Goal>,
  ) { }

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<SaleResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const sales = await this.saleRepository.find({
      where: whereCondition,
      relations: [
        'lead',
        'lead.company',
        'product',
        'product.company',
        'company',
      ],
      order: { created_at: 'DESC' },
    });

    return sales.map((sale) => this.mapToResponseDto(sale));
  }

  async findOne(id: number, company_id: number): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id, company_id },
      relations: [
        'lead',
        'lead.company',
        'product',
        'product.company',
        'company',
      ],
    });

    if (!sale) {
      throw new NotFoundException('Venda não encontrada');
    }

    return this.mapToResponseDto(sale);
  }

  async create(
    saleCreateDto: SaleCreateDto,
    headerCompanyId: number,
  ): Promise<SaleResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!saleCreateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = saleCreateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const sale = this.saleRepository.create({
      ...saleCreateDto,
      sale_date: new Date(saleCreateDto.sale_date),
      company_id: finalCompanyId,
    });

    const goal = await this.goalsRepository.findOne({
      where: { company_id: finalCompanyId },
      order: { created_at: 'DESC' },
    });

    const lead = await this.leadRepository.findOne({
      where: { id: saleCreateDto.lead_id, company_id: finalCompanyId },
    });

    if (goal && lead) {
      lead.status = 'bought';
      await this.leadRepository.save(lead);

      const currentValueInCents = Math.round(goal.current_value * 100);
      const saleAmountInCents = Math.round(sale.amount * 100);
      const newValueInCents = currentValueInCents + saleAmountInCents;
      goal.current_value = newValueInCents / 100;

      await this.goalsRepository.save(goal);
    }

    const savedSale = await this.saleRepository.save(sale);

    // Buscar a venda com relacionamentos
    const saleWithRelations = await this.saleRepository.findOne({
      where: { id: savedSale.id },
      relations: ['lead', 'product', 'company'],
    });

    if (!saleWithRelations) {
      throw new Error('Erro ao buscar venda criada');
    }

    return this.mapToResponseDto(saleWithRelations);
  }

  async update(
    id: number,
    saleUpdateDto: SaleUpdateDto,
    headerCompanyId: number,
  ): Promise<SaleResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!saleUpdateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = saleUpdateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const sale = await this.saleRepository.findOne({
      where: { id, company_id: finalCompanyId },
    });

    if (!sale) {
      throw new NotFoundException('Venda não encontrada');
    }

    // Atualizar campos (exceto company_id que já foi tratado)
    Object.keys(saleUpdateDto).forEach((key) => {
      if (saleUpdateDto[key] !== undefined && key !== 'company_id') {
        sale[key] = saleUpdateDto[key];
      }
    });

    if (saleUpdateDto.sale_date) {
      sale.sale_date = new Date(saleUpdateDto.sale_date || '');
    }

    const updatedSale = await this.saleRepository.save(sale);

    // Buscar a venda com relacionamentos
    const saleWithRelations = await this.saleRepository.findOne({
      where: { id: updatedSale.id },
      relations: ['lead', 'product', 'company'],
    });

    if (!saleWithRelations) {
      throw new Error('Erro ao buscar venda atualizada');
    }

    return this.mapToResponseDto(saleWithRelations);
  }

  async remove(id: number, company_id: number): Promise<void> {
    const sale = await this.saleRepository.findOne({
      where: { id, company_id },
    });

    if (!sale) {
      throw new NotFoundException('Venda não encontrada');
    }

    await this.saleRepository.remove(sale);
  }

  private mapToResponseDto(sale: Sale): SaleResponseDto {
    return {
      id: sale.id,
      lead_id: sale.lead_id,
      product_id: sale.product_id,
      amount: parseFloat(sale.amount.toString()),
      sale_date: sale.sale_date,
      notes: sale.notes,
      company_id: sale.company_id,
      company_name: sale.company?.name,
      created_at: sale.created_at,
      updated_at: sale.updated_at,
      lead: sale.lead
        ? {
          id: sale.lead.id,
          name: sale.lead.name,
          email: sale.lead.email,
          phone: sale.lead.phone,
          social_media: sale.lead.social_media,
          source: sale.lead.source,
          status: sale.lead.status,
          company_name: sale.lead.company?.name,
          company_id: sale.lead.company_id,
          created_at: sale.lead.created_at,
          updated_at: sale.lead.updated_at,
        }
        : undefined,
      product: sale.product
        ? {
          id: sale.product.id,
          name: sale.product.name,
          price: parseFloat(sale.product.price.toString()),
          company_id: sale.product.company_id,
          company_name: sale.product.company?.name,
          created_at: sale.product.created_at,
          updated_at: sale.product.updated_at,
        }
        : undefined,
    };
  }
}

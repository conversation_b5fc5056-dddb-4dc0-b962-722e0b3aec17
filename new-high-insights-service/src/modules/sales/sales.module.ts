import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SalesService } from './sales.service';
import { SalesController } from './sales.controller';
import { Sale } from '../../entities/sale.entity';
import { Lead } from '../../entities/lead.entity';
import { Goal } from '../../entities/goal.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Sale, Lead, Goal])],
  controllers: [SalesController],
  providers: [SalesService],
  exports: [SalesService],
})
export class SalesModule {}

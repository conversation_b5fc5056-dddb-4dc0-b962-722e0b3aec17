import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';

@Controller('protected')
export class ExampleProtectedController {
  
  @Get('profile')
  @UseGuards(JwtAuthGuard)
  getProfile(@CurrentUser() user: any) {
    return {
      message: 'Esta é uma rota protegida!',
      user: {
        id: user.id,
        username: user.username,
        companyId: user.companyId,
        company: user.company,
      },
    };
  }
}

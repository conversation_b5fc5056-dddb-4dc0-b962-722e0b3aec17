import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { Admin } from '../../entities/admin.entity';
import { Company } from '../../entities/company.entity';
import { LoginRequestDto } from './dto/login-request.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async login(loginData: LoginRequestDto): Promise<LoginResponseDto> {
    // Debug logging similar to Python version
    const isDebug = this.configService.get<string>('DEBUG') === 'True';

    if (isDebug) {
      console.log('🐛 DEBUG: Iniciando processo de login');
      console.log(`🔍 Buscando admin por username: ${loginData.username}`);
    }

    // Buscar admin por username
    const admin = await this.adminRepository.findOne({
      where: { username: loginData.username },
    });

    if (isDebug) {
      console.log(`🐛 DEBUG: Admin encontrado:`, admin ? 'Sim' : 'Não');
    }

    if (!admin) {
      throw new UnauthorizedException('Credenciais inválidas');
    }

    // Verificar senha
    const isPasswordValid = await this.verifyPassword(
      loginData.password,
      admin.passwordHash,
    );

    if (isDebug) {
      console.log(`🔒 Senha válida:`, isPasswordValid ? 'Sim' : 'Não');
    }

    if (!isPasswordValid) {
      throw new UnauthorizedException('Credenciais inválidas');
    }

    // Buscar dados da empresa
    const company = await this.companyRepository.findOne({
      where: { id: admin.company_id },
    });

    if (isDebug) {
      console.log(
        `🏢 Empresa encontrada:`,
        company ? company.name : 'Não encontrada',
      );
    }

    if (!company) {
      throw new NotFoundException('Empresa não encontrada');
    }

    // Criar token JWT
    const payload = {
      sub: admin.id.toString(),
      company_id: admin.company_id,
      is_global_admin: admin.is_global_admin,
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '30m', // 30 minutos como no Python
    });

    return {
      access_token: accessToken,
      token_type: 'bearer',
      company: {
        id: company.id,
        name: company.name,
      },
      is_global_admin: admin.is_global_admin,
    };
  }

  async verifyPassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 10);
  }

  async validateAdmin(adminId: number): Promise<Admin> {
    const admin = await this.adminRepository.findOne({
      where: { id: adminId },
      relations: ['company'],
    });

    if (!admin) {
      throw new UnauthorizedException('Admin não encontrado');
    }

    return admin;
  }
}

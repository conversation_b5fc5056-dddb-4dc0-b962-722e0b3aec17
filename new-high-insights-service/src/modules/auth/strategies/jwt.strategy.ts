import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey:
        configService.get<string>('JWT_SECRET') ||
        'your-super-secret-jwt-key-change-in-production',
    });
  }

  async validate(payload: any) {
    const adminId = parseInt(payload.sub as string);

    if (!adminId) {
      throw new UnauthorizedException('Token inválido');
    }

    try {
      const admin = await this.authService.validateAdmin(adminId);
      return {
        id: admin.id,
        username: admin.username,
        company_id: admin.company_id,
        is_global_admin: admin.is_global_admin,
        company: admin.company,
      };
    } catch (error) {
      console.error('Erro ao validar admin:', error);
      throw new UnauthorizedException('Admin não encontrado');
    }
  }
}

import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const CurrentUser = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

export const CompanyId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const companyId = request.headers['company-id'];

    if (companyId === undefined || companyId === null) {
      throw new Error('Header company-id é obrigatório');
    }

    const parsedCompanyId = parseInt(companyId as string, 10);
    if (isNaN(parsedCompanyId) || parsedCompanyId < 0) {
      throw new Error('Header company-id deve ser um número válido >= 0');
    }

    return parsedCompanyId;
  },
);

export const IsAdmin = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const isAdmin = request.headers['is-admin'];

    if (isAdmin === undefined || isAdmin === null) {
      throw new Error('Header is-admin é obrigatório');
    }

    // Converte string para boolean
    return isAdmin === 'true' || isAdmin === true;
  },
);

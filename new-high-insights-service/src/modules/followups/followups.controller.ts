import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { FollowupsService } from './followups.service';
import { FollowupCreateDto } from './dto/followup-create.dto';
import { FollowupUpdateDto } from './dto/followup-update.dto';
import { FollowupResponseDto } from './dto/followup-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';

@Controller('followups')
@UseGuards(JwtAuthGuard)
export class FollowupsController {
  constructor(private readonly followupsService: FollowupsService) {}

  @Post()
  async create(
    @Body() followupCreateDto: FollowupCreateDto,
    @CompanyId() companyId: number,
  ): Promise<FollowupResponseDto> {
    return this.followupsService.create(followupCreateDto, companyId);
  }

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<FollowupResponseDto[]> {
    return this.followupsService.findAll(companyId, isAdmin);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<FollowupResponseDto> {
    return this.followupsService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() followupUpdateDto: FollowupUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<FollowupResponseDto> {
    return this.followupsService.update(id, followupUpdateDto, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    await this.followupsService.remove(id, companyId);
    return { message: 'Follow-up deletado com sucesso' };
  }
}

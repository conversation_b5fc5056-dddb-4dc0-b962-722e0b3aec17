import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { FollowupsService } from './followups.service';
import { FollowupCreateDto } from './dto/followup-create.dto';
import { FollowupUpdateDto } from './dto/followup-update.dto';
import { FollowupResponseDto } from './dto/followup-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Admin } from '../../entities/admin.entity';

@Controller('followups')
@UseGuards(JwtAuthGuard)
export class FollowupsController {
  constructor(private readonly followupsService: FollowupsService) {}

  @Post()
  async create(
    @Body() followupCreateDto: FollowupCreateDto,
    @CurrentUser() user: Admin,
  ): Promise<FollowupResponseDto> {
    return this.followupsService.create(followupCreateDto, user.companyId);
  }

  @Get()
  async findAll(@CurrentUser() user: Admin): Promise<FollowupResponseDto[]> {
    return this.followupsService.findAll(user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<FollowupResponseDto> {
    return this.followupsService.findOne(id, user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() followupUpdateDto: FollowupUpdateDto,
    @CurrentUser() user: Admin,
  ): Promise<FollowupResponseDto> {
    return this.followupsService.update(id, followupUpdateDto, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<{ message: string }> {
    await this.followupsService.remove(id, user.companyId);
    return { message: 'Follow-up deletado com sucesso' };
  }
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FollowupsController } from './followups.controller';
import { FollowupsService } from './followups.service';
import { Followup } from '../../entities/followup.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Followup])],
  controllers: [FollowupsController],
  providers: [FollowupsService],
  exports: [FollowupsService],
})
export class FollowupsModule {}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Followup } from '../../entities/followup.entity';
import { FollowupCreateDto } from './dto/followup-create.dto';
import { FollowupUpdateDto } from './dto/followup-update.dto';
import { FollowupResponseDto } from './dto/followup-response.dto';

@Injectable()
export class FollowupsService {
  constructor(
    @InjectRepository(Followup)
    private followupRepository: Repository<Followup>,
  ) {}

  async create(followupCreateDto: FollowupCreateDto, companyId: number): Promise<FollowupResponseDto> {
    const followup = this.followupRepository.create({
      ...followupCreateDto,
      scheduledDate: new Date(followupCreateDto.scheduledDate),
      status: followupCreateDto.status || 'pending',
      companyId,
    });

    const savedFollowup = await this.followupRepository.save(followup);
    return this.mapToResponseDto(savedFollowup);
  }

  async findAll(companyId: number): Promise<FollowupResponseDto[]> {
    const followups = await this.followupRepository.find({
      where: { companyId },
      order: { scheduledDate: 'ASC' },
    });

    return followups.map(followup => this.mapToResponseDto(followup));
  }

  async findOne(id: number, companyId: number): Promise<FollowupResponseDto> {
    const followup = await this.followupRepository.findOne({
      where: { id, companyId },
    });

    if (!followup) {
      throw new NotFoundException('Follow-up não encontrado');
    }

    return this.mapToResponseDto(followup);
  }

  async update(id: number, followupUpdateDto: FollowupUpdateDto, companyId: number): Promise<FollowupResponseDto> {
    const followup = await this.followupRepository.findOne({
      where: { id, companyId },
    });

    if (!followup) {
      throw new NotFoundException('Follow-up não encontrado');
    }

    // Atualizar campos
    Object.assign(followup, followupUpdateDto);
    
    // Converter data se fornecida
    if (followupUpdateDto.scheduledDate) {
      followup.scheduledDate = new Date(followupUpdateDto.scheduledDate);
    }

    await this.followupRepository.save(followup);
    
    // Buscar o followup atualizado completo
    const updatedFollowup = await this.followupRepository.findOne({
      where: { id, companyId },
    });
    
    return this.mapToResponseDto(updatedFollowup!);
  }

  async remove(id: number, companyId: number): Promise<void> {
    const followup = await this.followupRepository.findOne({
      where: { id, companyId },
    });

    if (!followup) {
      throw new NotFoundException('Follow-up não encontrado');
    }

    await this.followupRepository.remove(followup);
  }

  private mapToResponseDto(followup: Followup): FollowupResponseDto {
    return {
      id: followup.id,
      leadId: followup.leadId,
      teamMemberId: followup.teamMemberId,
      title: followup.title,
      description: followup.description,
      scheduledDate: followup.scheduledDate,
      status: followup.status,
      notes: followup.notes,
      createdAt: followup.createdAt,
      updatedAt: followup.updatedAt,
    };
  }
}

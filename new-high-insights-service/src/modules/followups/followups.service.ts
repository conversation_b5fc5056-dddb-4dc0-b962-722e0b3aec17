import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Followup } from '../../entities/followup.entity';
import { FollowupCreateDto } from './dto/followup-create.dto';
import { FollowupResponseDto } from './dto/followup-response.dto';
import { FollowupUpdateDto } from './dto/followup-update.dto';

@Injectable()
export class FollowupsService {
  constructor(
    @InjectRepository(Followup)
    private followupRepository: Repository<Followup>,
  ) {}

  async create(
    followupCreateDto: FollowupCreateDto,
    headerCompanyId: number,
  ): Promise<FollowupResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!followupCreateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = followupCreateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const followup = this.followupRepository.create({
      ...followupCreateDto,
      scheduled_date: new Date(followupCreateDto.scheduled_date),
      status: followupCreateDto.status || 'pending',
      company_id: finalCompanyId,
    });

    const savedFollowup = await this.followupRepository.save(followup);

    // Busca novamente com relations para ter company_name
    const followupWithCompany = await this.followupRepository.findOne({
      where: { id: savedFollowup.id },
      relations: ['company'],
    });

    if (!followupWithCompany) {
      throw new Error('Erro ao buscar followup criado');
    }

    return this.mapToResponseDto(followupWithCompany);
  }

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<FollowupResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const followups = await this.followupRepository.find({
      where: whereCondition,
      relations: ['company'],
      order: { scheduled_date: 'ASC' },
    });

    return followups.map((followup) => this.mapToResponseDto(followup));
  }

  async findOne(id: number, company_id: number): Promise<FollowupResponseDto> {
    const followup = await this.followupRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!followup) {
      throw new NotFoundException('Follow-up não encontrado');
    }

    return this.mapToResponseDto(followup);
  }

  async update(
    id: number,
    followupUpdateDto: FollowupUpdateDto,
    headerCompanyId: number,
  ): Promise<FollowupResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!followupUpdateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = followupUpdateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const followup = await this.followupRepository.findOne({
      where: { id, company_id: finalCompanyId },
    });

    if (!followup) {
      throw new NotFoundException('Follow-up não encontrado');
    }

    // Atualizar campos (exceto company_id que já foi tratado)
    Object.keys(followupUpdateDto).forEach((key) => {
      if (followupUpdateDto[key] !== undefined && key !== 'company_id') {
        followup[key] = followupUpdateDto[key];
      }
    });

    // Converter data se fornecida
    if (followupUpdateDto.scheduled_date) {
      followup.scheduled_date = new Date(followupUpdateDto.scheduled_date);
    }

    await this.followupRepository.save(followup);

    // Buscar o followup atualizado completo
    const updatedFollowup = await this.followupRepository.findOne({
      where: { id, company_id: finalCompanyId },
      relations: ['company'],
    });

    if (!updatedFollowup) {
      throw new Error('Erro ao buscar followup atualizado');
    }

    return this.mapToResponseDto(updatedFollowup);
  }

  async remove(id: number, company_id: number): Promise<void> {
    const followup = await this.followupRepository.findOne({
      where: { id, company_id },
    });

    if (!followup) {
      throw new NotFoundException('Follow-up não encontrado');
    }

    await this.followupRepository.remove(followup);
  }

  private mapToResponseDto(followup: Followup): FollowupResponseDto {
    return {
      id: followup.id,
      lead_id: followup.lead_id,
      team_member_id: followup.team_member_id,
      title: followup.title,
      description: followup.description,
      scheduled_date: followup.scheduled_date,
      status: followup.status,
      notes: followup.notes,
      company_id: followup.company_id,
      company_name: followup.company?.name,
      created_at: followup.created_at,
      updated_at: followup.updated_at,
    };
  }
}

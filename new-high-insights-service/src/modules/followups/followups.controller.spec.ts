import { Test, TestingModule } from '@nestjs/testing';
import { FollowupsController } from './followups.controller';
import { FollowupsService } from './followups.service';
import { FollowupCreateDto } from './dto/followup-create.dto';
import { FollowupUpdateDto } from './dto/followup-update.dto';
import { FollowupResponseDto } from './dto/followup-response.dto';
import { NotFoundException } from '@nestjs/common';

describe('FollowupsController', () => {
  let controller: FollowupsController;
  let service: FollowupsService;

  const mockFollowupResponse: FollowupResponseDto = {
    id: 1,
    lead_id: 1,
    teamMember_id: 1,
    title: 'Ligar para cliente',
    description: 'Fazer follow-up da proposta enviada',
    scheduledDate: new Date('2024-01-15T10:00:00Z'),
    status: 'pending',
    notes: 'Cliente interessado no produto premium',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockFollowupsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FollowupsController],
      providers: [
        {
          provide: FollowupsService,
          useValue: mockFollowupsService,
        },
      ],
    }).compile();

    controller = module.get<FollowupsController>(FollowupsController);
    service = module.get<FollowupsService>(FollowupsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return followups for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const result = [mockFollowupResponse];
      mockFollowupsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all followups for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const result = [mockFollowupResponse];
      mockFollowupsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return followups for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const result = [mockFollowupResponse];
      mockFollowupsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new followup', async () => {
      const createDto: FollowupCreateDto = {
        lead_id: 1,
        teamMember_id: 1,
        title: 'Ligar para cliente',
        description: 'Fazer follow-up da proposta enviada',
        scheduledDate: '2024-01-15T10:00:00Z',
        status: 'pending',
        notes: 'Cliente interessado no produto premium',
      };

      mockFollowupsService.create.mockResolvedValue(mockFollowupResponse);

      const companyId = 1;
      expect(await controller.create(createDto, companyId)).toBe(
        mockFollowupResponse,
      );
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single followup', async () => {
      mockFollowupsService.findOne.mockResolvedValue(mockFollowupResponse);

      const companyId = 1;
      expect(await controller.findOne(1, companyId)).toBe(mockFollowupResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when followup not found', async () => {
      mockFollowupsService.findOne.mockRejectedValue(
        new NotFoundException('Follow-up não encontrado'),
      );

      const companyId = 1;
      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a followup', async () => {
      const updateDto: FollowupUpdateDto = {
        status: 'complete',
        notes: 'Follow-up realizado com sucesso',
      };

      const updatedFollowup = { ...mockFollowupResponse, ...updateDto };
      mockFollowupsService.update.mockResolvedValue(updatedFollowup);

      const companyId = 1;
      expect(await controller.update(1, updateDto, companyId)).toBe(
        updatedFollowup,
      );
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a followup', async () => {
      mockFollowupsService.remove.mockResolvedValue(undefined);

      const companyId = 1;
      const result = await controller.remove(1, companyId);
      expect(result).toEqual({ message: 'Follow-up deletado com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

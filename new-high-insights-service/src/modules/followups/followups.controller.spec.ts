import { Test, TestingModule } from '@nestjs/testing';
import { FollowupsController } from './followups.controller';
import { FollowupsService } from './followups.service';
import { FollowupCreateDto } from './dto/followup-create.dto';
import { FollowupUpdateDto } from './dto/followup-update.dto';
import { FollowupResponseDto } from './dto/followup-response.dto';
import { Admin } from '../../entities/admin.entity';
import { NotFoundException } from '@nestjs/common';

describe('FollowupsController', () => {
  let controller: FollowupsController;
  let service: FollowupsService;

  const mockAdmin: Admin = {
    id: 1,
    companyId: 1,
    username: 'test',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    company: null,
  };

  const mockFollowupResponse: FollowupResponseDto = {
    id: 1,
    leadId: 1,
    teamMemberId: 1,
    title: 'Ligar para cliente',
    description: 'Fazer follow-up da proposta enviada',
    scheduledDate: new Date('2024-01-15T10:00:00Z'),
    status: 'pending',
    notes: 'Cliente interessado no produto premium',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockFollowupsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FollowupsController],
      providers: [
        {
          provide: FollowupsService,
          useValue: mockFollowupsService,
        },
      ],
    }).compile();

    controller = module.get<FollowupsController>(FollowupsController);
    service = module.get<FollowupsService>(FollowupsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of followups', async () => {
      const result = [mockFollowupResponse];
      mockFollowupsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(mockAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(mockAdmin.companyId);
    });
  });

  describe('create', () => {
    it('should create a new followup', async () => {
      const createDto: FollowupCreateDto = {
        leadId: 1,
        teamMemberId: 1,
        title: 'Ligar para cliente',
        description: 'Fazer follow-up da proposta enviada',
        scheduledDate: '2024-01-15T10:00:00Z',
        status: 'pending',
        notes: 'Cliente interessado no produto premium',
      };

      mockFollowupsService.create.mockResolvedValue(mockFollowupResponse);

      expect(await controller.create(createDto, mockAdmin)).toBe(mockFollowupResponse);
      expect(service.create).toHaveBeenCalledWith(createDto, mockAdmin.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single followup', async () => {
      mockFollowupsService.findOne.mockResolvedValue(mockFollowupResponse);

      expect(await controller.findOne(1, mockAdmin)).toBe(mockFollowupResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });

    it('should throw NotFoundException when followup not found', async () => {
      mockFollowupsService.findOne.mockRejectedValue(new NotFoundException('Follow-up não encontrado'));

      await expect(controller.findOne(999, mockAdmin)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a followup', async () => {
      const updateDto: FollowupUpdateDto = {
        status: 'complete',
        notes: 'Follow-up realizado com sucesso',
      };

      const updatedFollowup = { ...mockFollowupResponse, ...updateDto };
      mockFollowupsService.update.mockResolvedValue(updatedFollowup);

      expect(await controller.update(1, updateDto, mockAdmin)).toBe(updatedFollowup);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockAdmin.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a followup', async () => {
      mockFollowupsService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(1, mockAdmin);
      expect(result).toEqual({ message: 'Follow-up deletado com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });
  });
});

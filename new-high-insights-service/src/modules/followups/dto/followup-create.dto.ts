import {
  IsString,
  IsOptional,
  IsInt,
  IsDateString,
  IsIn,
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
} from 'class-validator';

export class FollowupCreateDto {
  @IsOptional()
  @IsInt({ message: 'ID do lead deve ser um número inteiro' })
  @Min(1, { message: 'ID do lead deve ser maior que 0' })
  leadId?: number;

  @IsOptional()
  @IsInt({ message: 'ID do membro da equipe deve ser um número inteiro' })
  @Min(1, { message: 'ID do membro da equipe deve ser maior que 0' })
  teamMemberId?: number;

  @IsString({ message: 'Título é obrigatório' })
  @MaxLength(200, { message: 'Título deve ter no máximo 200 caracteres' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Descrição deve ser uma string' })
  description?: string;

  @IsDateString({}, { message: 'Data agendada deve estar no formato ISO 8601' })
  scheduledDate: string;

  @IsOptional()
  @IsIn(['pending', 'complete', 'declined'], { 
    message: 'Status deve ser: pending, complete ou declined' 
  })
  status?: string;

  @IsOptional()
  @IsString({ message: 'Notas devem ser uma string' })
  notes?: string;
}

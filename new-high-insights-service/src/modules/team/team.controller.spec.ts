import { Test, TestingModule } from '@nestjs/testing';
import { Team<PERSON>ontroller } from './team.controller';
import { TeamService } from './team.service';
import { TeamMemberCreateDto } from './dto/team-member-create.dto';
import { TeamMemberUpdateDto } from './dto/team-member-update.dto';
import { TeamMemberResponseDto } from './dto/team-member-response.dto';
import { Admin } from '../../entities/admin.entity';
import { NotFoundException } from '@nestjs/common';

describe('TeamController', () => {
  let controller: TeamController;
  let service: TeamService;

  const mockAdmin: Admin = {
    id: 1,
    companyId: 1,
    username: 'test',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    company: null,
  };

  const mockTeamMemberResponse: TeamMemberResponseDto = {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '11999999999',
    position: 'Vendedor',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTeamService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TeamController],
      providers: [
        {
          provide: TeamService,
          useValue: mockTeamService,
        },
      ],
    }).compile();

    controller = module.get<TeamController>(TeamController);
    service = module.get<TeamService>(TeamService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of team members', async () => {
      const result = [mockTeamMemberResponse];
      mockTeamService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(mockAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(mockAdmin.companyId);
    });
  });

  describe('create', () => {
    it('should create a new team member', async () => {
      const createDto: TeamMemberCreateDto = {
        name: 'João Silva',
        email: '<EMAIL>',
        phone: '11999999999',
        position: 'Vendedor',
      };

      mockTeamService.create.mockResolvedValue(mockTeamMemberResponse);

      expect(await controller.create(createDto, mockAdmin)).toBe(mockTeamMemberResponse);
      expect(service.create).toHaveBeenCalledWith(createDto, mockAdmin.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single team member', async () => {
      mockTeamService.findOne.mockResolvedValue(mockTeamMemberResponse);

      expect(await controller.findOne(1, mockAdmin)).toBe(mockTeamMemberResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });

    it('should throw NotFoundException when team member not found', async () => {
      mockTeamService.findOne.mockRejectedValue(new NotFoundException('Membro da equipe não encontrado'));

      await expect(controller.findOne(999, mockAdmin)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a team member', async () => {
      const updateDto: TeamMemberUpdateDto = {
        position: 'Gerente de Vendas',
        phone: '11888888888',
      };

      const updatedTeamMember = { ...mockTeamMemberResponse, ...updateDto };
      mockTeamService.update.mockResolvedValue(updatedTeamMember);

      expect(await controller.update(1, updateDto, mockAdmin)).toBe(updatedTeamMember);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockAdmin.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a team member', async () => {
      mockTeamService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(1, mockAdmin);
      expect(result).toEqual({ message: 'Membro da equipe deletado com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });
  });
});

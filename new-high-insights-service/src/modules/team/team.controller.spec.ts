import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TeamMemberCreateDto } from './dto/team-member-create.dto';
import { TeamMemberResponseDto } from './dto/team-member-response.dto';
import { TeamMemberUpdateDto } from './dto/team-member-update.dto';
import { TeamController } from './team.controller';
import { TeamService } from './team.service';

describe('TeamController', () => {
  let controller: TeamController;
  let service: TeamService;

  const mockTeamMemberResponse: TeamMemberResponseDto = {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '11999999999',
    position: 'Vendedor',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockTeamService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TeamController],
      providers: [
        {
          provide: TeamService,
          useValue: mockTeamService,
        },
      ],
    }).compile();

    controller = module.get<TeamController>(TeamController);
    service = module.get<TeamService>(TeamService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return team members for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const result = [mockTeamMemberResponse];
      mockTeamService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all team members for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const result = [mockTeamMemberResponse];
      mockTeamService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return team members for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const result = [mockTeamMemberResponse];
      mockTeamService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new team member', async () => {
      const createDto: TeamMemberCreateDto = {
        name: 'João Silva',
        email: '<EMAIL>',
        phone: '11999999999',
        position: 'Vendedor',
      };

      mockTeamService.create.mockResolvedValue(mockTeamMemberResponse);

      const companyId = 1;
      expect(await controller.create(createDto, companyId)).toBe(
        mockTeamMemberResponse,
      );
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single team member', async () => {
      mockTeamService.findOne.mockResolvedValue(mockTeamMemberResponse);

      const companyId = 1;
      expect(await controller.findOne(1, companyId)).toBe(
        mockTeamMemberResponse,
      );
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when team member not found', async () => {
      mockTeamService.findOne.mockRejectedValue(
        new NotFoundException('Membro da equipe não encontrado'),
      );

      const companyId = 1;
      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a team member', async () => {
      const updateDto: TeamMemberUpdateDto = {
        position: 'Gerente de Vendas',
        phone: '11888888888',
      };

      const updatedTeamMember = { ...mockTeamMemberResponse, ...updateDto };
      mockTeamService.update.mockResolvedValue(updatedTeamMember);

      const companyId = 1;
      expect(await controller.update(1, updateDto, companyId)).toBe(
        updatedTeamMember,
      );
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a team member', async () => {
      mockTeamService.remove.mockResolvedValue(undefined);

      const companyId = 1;
      const result = await controller.remove(1, companyId);
      expect(result).toEqual({
        message: 'Membro da equipe deletado com sucesso',
      });
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

import { IsString, <PERSON>Optional, Is<PERSON>mail, IsN<PERSON>ber } from 'class-validator';
import { Type } from 'class-transformer';

export class TeamMemberUpdateDto {
  @IsOptional()
  @IsString({ message: 'Nome deve ser uma string' })
  name?: string;

  @IsOptional()
  @IsEmail({}, { message: 'Email deve ter um formato válido' })
  email?: string;

  @IsOptional()
  @IsString({ message: 'Telefone deve ser uma string' })
  phone?: string;

  @IsOptional()
  @IsString({ message: 'Cargo deve ser uma string' })
  position?: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  company_id?: number;
}

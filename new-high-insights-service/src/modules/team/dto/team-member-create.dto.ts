import {
  IsString,
  IsOptional,
  IsEmail,
} from 'class-validator';

export class TeamMemberCreateDto {
  @IsString({ message: 'Nome deve ser uma string' })
  name: string;

  @IsOptional()
  @IsEmail({}, { message: 'Email deve ter um formato válido' })
  email?: string;

  @IsOptional()
  @IsString({ message: 'Telefone deve ser uma string' })
  phone?: string;

  @IsOptional()
  @IsString({ message: 'Cargo deve ser uma string' })
  position?: string;
}

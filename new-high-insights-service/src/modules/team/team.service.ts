import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TeamMember } from '../../entities/team-member.entity';
import { TeamMemberCreateDto } from './dto/team-member-create.dto';
import { TeamMemberResponseDto } from './dto/team-member-response.dto';
import { TeamMemberUpdateDto } from './dto/team-member-update.dto';

@Injectable()
export class TeamService {
  constructor(
    @InjectRepository(TeamMember)
    private teamMemberRepository: Repository<TeamMember>,
  ) {}

  async create(
    teamMemberCreateDto: TeamMemberCreateDto,
    headerCompanyId: number,
  ): Promise<TeamMemberResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!teamMemberCreateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = teamMemberCreateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const teamMember = this.teamMemberRepository.create({
      ...teamMemberCreateDto,
      company_id: finalCompanyId,
    });

    const savedTeamMember = await this.teamMemberRepository.save(teamMember);
    return this.mapToResponseDto(savedTeamMember);
  }

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<TeamMemberResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const teamMembers = await this.teamMemberRepository.find({
      where: whereCondition,
      relations: ['company'],
      order: { created_at: 'DESC' },
    });

    return teamMembers.map((teamMember) => this.mapToResponseDto(teamMember));
  }

  async findOne(
    id: number,
    company_id: number,
  ): Promise<TeamMemberResponseDto> {
    const teamMember = await this.teamMemberRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!teamMember) {
      throw new NotFoundException('Membro da equipe não encontrado');
    }

    return this.mapToResponseDto(teamMember);
  }

  async update(
    id: number,
    teamMemberUpdateDto: TeamMemberUpdateDto,
    headerCompanyId: number,
  ): Promise<TeamMemberResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!teamMemberUpdateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = teamMemberUpdateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const teamMember = await this.teamMemberRepository.findOne({
      where: { id, company_id: finalCompanyId },
      relations: ['company'],
    });

    if (!teamMember) {
      throw new NotFoundException('Membro da equipe não encontrado');
    }

    // Atualizar campos
    Object.assign(teamMember, teamMemberUpdateDto);

    await this.teamMemberRepository.save(teamMember);

    // Buscar o teamMember atualizado completo
    const updatedTeamMember = await this.teamMemberRepository.findOne({
      where: { id, company_id: finalCompanyId },
      relations: ['company'],
    });

    return this.mapToResponseDto(updatedTeamMember!);
  }

  async remove(id: number, company_id: number): Promise<void> {
    const teamMember = await this.teamMemberRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!teamMember) {
      throw new NotFoundException('Membro da equipe não encontrado');
    }

    await this.teamMemberRepository.remove(teamMember);
  }

  private mapToResponseDto(teamMember: TeamMember): TeamMemberResponseDto {
    return {
      id: teamMember.id,
      name: teamMember.name,
      email: teamMember.email,
      phone: teamMember.phone,
      position: teamMember.position,
      company_id: teamMember.company_id,
      company_name: teamMember.company?.name,
      created_at: teamMember.created_at,
      updated_at: teamMember.updated_at,
    };
  }
}

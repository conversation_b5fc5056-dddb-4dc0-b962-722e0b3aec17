import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TeamMember } from '../../entities/team-member.entity';
import { TeamMemberCreateDto } from './dto/team-member-create.dto';
import { TeamMemberUpdateDto } from './dto/team-member-update.dto';
import { TeamMemberResponseDto } from './dto/team-member-response.dto';

@Injectable()
export class TeamService {
  constructor(
    @InjectRepository(TeamMember)
    private teamMemberRepository: Repository<TeamMember>,
  ) {}

  async create(teamMemberCreateDto: TeamMemberCreateDto, companyId: number): Promise<TeamMemberResponseDto> {
    const teamMember = this.teamMemberRepository.create({
      ...teamMemberCreateDto,
      companyId,
    });

    const savedTeamMember = await this.teamMemberRepository.save(teamMember);
    return this.mapToResponseDto(savedTeamMember);
  }

  async findAll(companyId: number): Promise<TeamMemberResponseDto[]> {
    const teamMembers = await this.teamMemberRepository.find({
      where: { companyId },
      order: { createdAt: 'DESC' },
    });

    return teamMembers.map(teamMember => this.mapToResponseDto(teamMember));
  }

  async findOne(id: number, companyId: number): Promise<TeamMemberResponseDto> {
    const teamMember = await this.teamMemberRepository.findOne({
      where: { id, companyId },
    });

    if (!teamMember) {
      throw new NotFoundException('Membro da equipe não encontrado');
    }

    return this.mapToResponseDto(teamMember);
  }

  async update(id: number, teamMemberUpdateDto: TeamMemberUpdateDto, companyId: number): Promise<TeamMemberResponseDto> {
    const teamMember = await this.teamMemberRepository.findOne({
      where: { id, companyId },
    });

    if (!teamMember) {
      throw new NotFoundException('Membro da equipe não encontrado');
    }

    // Atualizar campos
    Object.assign(teamMember, teamMemberUpdateDto);

    await this.teamMemberRepository.save(teamMember);
    
    // Buscar o teamMember atualizado completo
    const updatedTeamMember = await this.teamMemberRepository.findOne({
      where: { id, companyId },
    });
    
    return this.mapToResponseDto(updatedTeamMember!);
  }

  async remove(id: number, companyId: number): Promise<void> {
    const teamMember = await this.teamMemberRepository.findOne({
      where: { id, companyId },
    });

    if (!teamMember) {
      throw new NotFoundException('Membro da equipe não encontrado');
    }

    await this.teamMemberRepository.remove(teamMember);
  }

  private mapToResponseDto(teamMember: TeamMember): TeamMemberResponseDto {
    return {
      id: teamMember.id,
      name: teamMember.name,
      email: teamMember.email,
      phone: teamMember.phone,
      position: teamMember.position,
      createdAt: teamMember.createdAt,
      updatedAt: teamMember.updatedAt,
    };
  }
}

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { TeamService } from './team.service';
import { TeamMemberCreateDto } from './dto/team-member-create.dto';
import { TeamMemberUpdateDto } from './dto/team-member-update.dto';
import { TeamMemberResponseDto } from './dto/team-member-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Admin } from '../../entities/admin.entity';

@Controller('team')
@UseGuards(JwtAuthGuard)
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @Post()
  async create(
    @Body() teamMemberCreateDto: TeamMemberCreateDto,
    @CurrentUser() user: Admin,
  ): Promise<TeamMemberResponseDto> {
    return this.teamService.create(teamMemberCreateDto, user.companyId);
  }

  @Get()
  async findAll(@CurrentUser() user: Admin): Promise<TeamMemberResponseDto[]> {
    return this.teamService.findAll(user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<TeamMemberResponseDto> {
    return this.teamService.findOne(id, user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() teamMemberUpdateDto: TeamMemberUpdateDto,
    @CurrentUser() user: Admin,
  ): Promise<TeamMemberResponseDto> {
    return this.teamService.update(id, teamMemberUpdateDto, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<{ message: string }> {
    await this.teamService.remove(id, user.companyId);
    return { message: 'Membro da equipe deletado com sucesso' };
  }
}

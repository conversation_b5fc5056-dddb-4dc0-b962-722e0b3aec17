import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { TeamService } from './team.service';
import { TeamMemberCreateDto } from './dto/team-member-create.dto';
import { TeamMemberUpdateDto } from './dto/team-member-update.dto';
import { TeamMemberResponseDto } from './dto/team-member-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';

@Controller('team')
@UseGuards(JwtAuthGuard)
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @Post()
  async create(
    @Body() teamMemberCreateDto: TeamMemberCreateDto,
    @CompanyId() companyId: number,
  ): Promise<TeamMemberResponseDto> {
    return this.teamService.create(teamMemberCreateDto, companyId);
  }

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<TeamMemberResponseDto[]> {
    return this.teamService.findAll(companyId, isAdmin);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<TeamMemberResponseDto> {
    return this.teamService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() teamMemberUpdateDto: TeamMemberUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<TeamMemberResponseDto> {
    return this.teamService.update(id, teamMemberUpdateDto, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    await this.teamService.remove(id, companyId);
    return { message: 'Membro da equipe deletado com sucesso' };
  }
}

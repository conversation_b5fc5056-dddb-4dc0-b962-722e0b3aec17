import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { GoalsService } from './goals.service';
import { GoalCreateDto } from './dto/goal-create.dto';
import { GoalUpdateDto } from './dto/goal-update.dto';
import { GoalResponseDto } from './dto/goal-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Admin } from '../../entities/admin.entity';

@Controller('goals')
@UseGuards(JwtAuthGuard)
export class GoalsController {
  constructor(private readonly goalsService: GoalsService) {}

  @Post()
  async create(
    @Body() goalCreateDto: GoalCreateDto,
    @CurrentUser() user: Admin,
  ): Promise<GoalResponseDto> {
    return this.goalsService.create(goalCreateDto, user.companyId);
  }

  @Get()
  async findAll(@CurrentUser() user: Admin): Promise<GoalResponseDto[]> {
    return this.goalsService.findAll(user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<GoalResponseDto> {
    return this.goalsService.findOne(id, user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() goalUpdateDto: GoalUpdateDto,
    @CurrentUser() user: Admin,
  ): Promise<GoalResponseDto> {
    return this.goalsService.update(id, goalUpdateDto, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<{ message: string }> {
    await this.goalsService.remove(id, user.companyId);
    return { message: 'Meta deletada com sucesso' };
  }
}

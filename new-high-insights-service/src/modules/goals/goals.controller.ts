import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { GoalsService } from './goals.service';
import { GoalCreateDto } from './dto/goal-create.dto';
import { GoalUpdateDto } from './dto/goal-update.dto';
import { GoalResponseDto } from './dto/goal-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';

@Controller('goals')
@UseGuards(JwtAuthGuard)
export class GoalsController {
  constructor(private readonly goalsService: GoalsService) {}

  @Post()
  async create(
    @Body() goalCreateDto: GoalCreateDto,
    @CompanyId() companyId: number,
  ): Promise<GoalResponseDto> {
    return this.goalsService.create(goalCreateDto, companyId);
  }

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<GoalResponseDto[]> {
    return this.goalsService.findAll(companyId, isAdmin);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<GoalResponseDto> {
    return this.goalsService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() goalUpdateDto: GoalUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<GoalResponseDto> {
    return this.goalsService.update(id, goalUpdateDto, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    await this.goalsService.remove(id, companyId);
    return { message: 'Meta deletada com sucesso' };
  }
}

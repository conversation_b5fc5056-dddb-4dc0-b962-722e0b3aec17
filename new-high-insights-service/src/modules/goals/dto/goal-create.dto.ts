import { IsString, IsOptional, IsN<PERSON>ber, IsDateString, IsPositive, Max, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';

export class GoalCreateDto {
  @IsString({ message: 'Título deve ser uma string' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Descrição deve ser uma string' })
  description?: string;

  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Valor alvo deve ter no máximo 2 casas decimais' })
  @IsPositive({ message: 'Valor alvo deve ser positivo' })
  @Max(99999999.99, { message: 'Valor alvo muito alto' })
  @Transform(({ value }) => parseFloat(value))
  targetValue: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Valor atual deve ter no máximo 2 casas decimais' })
  @IsPositive({ message: 'Valor atual deve ser positivo ou zero' })
  @Max(99999999.99, { message: 'Valor atual muito alto' })
  @Transform(({ value }) => parseFloat(value))
  currentValue?: number;

  @IsString({ message: 'Tipo de meta deve ser uma string' })
  @IsIn(['sales', 'leads', 'revenue'], { message: 'Tipo de meta deve ser: sales, leads ou revenue' })
  goalType: string;

  @IsString({ message: 'Tipo de período deve ser uma string' })
  @IsIn(['monthly', 'quarterly', 'yearly'], { message: 'Tipo de período deve ser: monthly, quarterly ou yearly' })
  periodType: string;

  @IsDateString({}, { message: 'Data de início deve ser uma data válida' })
  startDate: string;

  @IsDateString({}, { message: 'Data de fim deve ser uma data válida' })
  endDate: string;
}

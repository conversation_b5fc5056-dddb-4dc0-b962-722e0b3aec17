import { Test, TestingModule } from '@nestjs/testing';
import { GoalsController } from './goals.controller';
import { GoalsService } from './goals.service';
import { GoalCreateDto } from './dto/goal-create.dto';
import { GoalUpdateDto } from './dto/goal-update.dto';
import { GoalResponseDto } from './dto/goal-response.dto';
import { Admin } from '../../entities/admin.entity';
import { NotFoundException } from '@nestjs/common';

describe('GoalsController', () => {
  let controller: GoalsController;
  let service: GoalsService;

  const mockAdmin: Admin = {
    id: 1,
    companyId: 1,
    username: 'test',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    company: null,
  };

  const mockGoalResponse: GoalResponseDto = {
    id: 1,
    title: 'Meta de Vendas Q1',
    description: 'Meta de vendas para o primeiro trimestre',
    targetValue: 50000.00,
    currentValue: 15000.00,
    goalType: 'sales',
    periodType: 'quarterly',
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-03-31'),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockGoalsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GoalsController],
      providers: [
        {
          provide: GoalsService,
          useValue: mockGoalsService,
        },
      ],
    }).compile();

    controller = module.get<GoalsController>(GoalsController);
    service = module.get<GoalsService>(GoalsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of goals', async () => {
      const result = [mockGoalResponse];
      mockGoalsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(mockAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(mockAdmin.companyId);
    });
  });

  describe('create', () => {
    it('should create a new goal', async () => {
      const createDto: GoalCreateDto = {
        title: 'Meta de Vendas Q1',
        description: 'Meta de vendas para o primeiro trimestre',
        targetValue: 50000.00,
        currentValue: 15000.00,
        goalType: 'sales',
        periodType: 'quarterly',
        startDate: '2023-01-01',
        endDate: '2023-03-31',
      };

      mockGoalsService.create.mockResolvedValue(mockGoalResponse);

      expect(await controller.create(createDto, mockAdmin)).toBe(mockGoalResponse);
      expect(service.create).toHaveBeenCalledWith(createDto, mockAdmin.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single goal', async () => {
      mockGoalsService.findOne.mockResolvedValue(mockGoalResponse);

      expect(await controller.findOne(1, mockAdmin)).toBe(mockGoalResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });

    it('should throw NotFoundException when goal not found', async () => {
      mockGoalsService.findOne.mockRejectedValue(new NotFoundException('Meta não encontrada'));

      await expect(controller.findOne(999, mockAdmin)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a goal', async () => {
      const updateDto: GoalUpdateDto = {
        currentValue: 25000.00,
        description: 'Meta atualizada',
      };

      const updatedGoal = { ...mockGoalResponse, ...updateDto };
      mockGoalsService.update.mockResolvedValue(updatedGoal);

      expect(await controller.update(1, updateDto, mockAdmin)).toBe(updatedGoal);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockAdmin.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a goal', async () => {
      mockGoalsService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(1, mockAdmin);
      expect(result).toEqual({ message: 'Meta deletada com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });
  });
});

import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { GoalCreateDto } from './dto/goal-create.dto';
import { GoalResponseDto } from './dto/goal-response.dto';
import { GoalUpdateDto } from './dto/goal-update.dto';
import { GoalsController } from './goals.controller';
import { GoalsService } from './goals.service';

describe('GoalsController', () => {
  let controller: GoalsController;
  let service: GoalsService;

  const mockGoalResponse: GoalResponseDto = {
    id: 1,
    title: 'Meta de Vendas Q1',
    description: 'Meta de vendas para o primeiro trimestre',
    target_value: 50000.0,
    current_value: 15000.0,
    goal_type: 'sales',
    period_type: 'quarterly',
    start_date: new Date('2023-01-01'),
    end_date: new Date('2023-03-31'),
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockGoalsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GoalsController],
      providers: [
        {
          provide: GoalsService,
          useValue: mockGoalsService,
        },
      ],
    }).compile();

    controller = module.get<GoalsController>(GoalsController);
    service = module.get<GoalsService>(GoalsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return goals for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const result = [mockGoalResponse];
      mockGoalsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all goals for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const result = [mockGoalResponse];
      mockGoalsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return goals for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const result = [mockGoalResponse];
      mockGoalsService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new goal', async () => {
      const createDto: GoalCreateDto = {
        title: 'Meta de Vendas Q1',
        description: 'Meta de vendas para o primeiro trimestre',
        target_value: 50000.0,
        current_value: 15000.0,
        goal_type: 'sales',
        period_type: 'quarterly',
        start_date: '2023-01-01',
        end_date: '2023-03-31',
      };

      mockGoalsService.create.mockResolvedValue(mockGoalResponse);

      const companyId = 1;
      expect(await controller.create(createDto, companyId)).toBe(
        mockGoalResponse,
      );
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single goal', async () => {
      mockGoalsService.findOne.mockResolvedValue(mockGoalResponse);

      const companyId = 1;
      expect(await controller.findOne(1, companyId)).toBe(mockGoalResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when goal not found', async () => {
      mockGoalsService.findOne.mockRejectedValue(
        new NotFoundException('Meta não encontrada'),
      );

      const companyId = 1;
      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a goal', async () => {
      const updateDto: GoalUpdateDto = {
        current_value: 25000.0,
        description: 'Meta atualizada',
      };

      const updatedGoal = { ...mockGoalResponse, ...updateDto };
      mockGoalsService.update.mockResolvedValue(updatedGoal);

      const companyId = 1;
      expect(await controller.update(1, updateDto, companyId)).toBe(
        updatedGoal,
      );
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a goal', async () => {
      mockGoalsService.remove.mockResolvedValue(undefined);

      const companyId = 1;
      const result = await controller.remove(1, companyId);
      expect(result).toEqual({ message: 'Meta deletada com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

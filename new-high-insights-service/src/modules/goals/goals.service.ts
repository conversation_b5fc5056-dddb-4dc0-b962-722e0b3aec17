import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Goal } from '../../entities/goal.entity';
import { GoalCreateDto } from './dto/goal-create.dto';
import { GoalResponseDto } from './dto/goal-response.dto';
import { GoalUpdateDto } from './dto/goal-update.dto';

@Injectable()
export class GoalsService {
  constructor(
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
  ) {}

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<GoalResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const goals = await this.goalRepository.find({
      where: whereCondition,
      relations: ['company'],
      order: { created_at: 'DESC' },
    });

    return goals.map((goal) => this.mapToResponseDto(goal));
  }

  async findOne(id: number, company_id: number): Promise<GoalResponseDto> {
    const goal = await this.goalRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!goal) {
      throw new NotFoundException('Meta não encontrada');
    }

    return this.mapToResponseDto(goal);
  }

  async create(
    goalCreateDto: GoalCreateDto,
    headerCompanyId: number,
  ): Promise<GoalResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!goalCreateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = goalCreateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const goal = this.goalRepository.create({
      ...goalCreateDto,
      start_date: new Date(goalCreateDto.start_date),
      end_date: new Date(goalCreateDto.end_date),
      current_value: goalCreateDto.current_value || 0,
      company_id: finalCompanyId,
    });

    const savedGoal = await this.goalRepository.save(goal);

    // Busca novamente com relations para ter company_name
    const goalWithCompany = await this.goalRepository.findOne({
      where: { id: savedGoal.id },
      relations: ['company'],
    });

    if (!goalWithCompany) {
      throw new Error('Erro ao buscar meta criada');
    }

    return this.mapToResponseDto(goalWithCompany);
  }

  async update(
    id: number,
    goalUpdateDto: GoalUpdateDto,
    headerCompanyId: number,
  ): Promise<GoalResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!goalUpdateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = goalUpdateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const goal = await this.goalRepository.findOne({
      where: { id, company_id: finalCompanyId },
    });

    if (!goal) {
      throw new NotFoundException('Meta não encontrada');
    }

    // Atualizar campos (exceto company_id que já foi tratado)
    Object.keys(goalUpdateDto).forEach((key) => {
      if (goalUpdateDto[key] !== undefined && key !== 'company_id') {
        goal[key] = goalUpdateDto[key];
      }
    });

    if (goalUpdateDto.start_date) {
      goal.start_date = new Date(goalUpdateDto.start_date);
    }

    if (goalUpdateDto.end_date) {
      goal.end_date = new Date(goalUpdateDto.end_date);
    }

    await this.goalRepository.save(goal);

    // Buscar a meta atualizada completa
    const updatedGoal = await this.goalRepository.findOne({
      where: { id, company_id: finalCompanyId },
      relations: ['company'],
    });

    if (!updatedGoal) {
      throw new Error('Erro ao buscar meta atualizada');
    }

    return this.mapToResponseDto(updatedGoal);
  }

  async remove(id: number, company_id: number): Promise<void> {
    const goal = await this.goalRepository.findOne({
      where: { id, company_id },
    });

    if (!goal) {
      throw new NotFoundException('Meta não encontrada');
    }

    await this.goalRepository.remove(goal);
  }

  private mapToResponseDto(goal: Goal): GoalResponseDto {
    return {
      id: goal.id,
      title: goal.title,
      description: goal.description,
      target_value: parseFloat((goal.target_value || 0).toString()),
      current_value: parseFloat((goal.current_value || 0).toString()),
      goal_type: goal.goal_type,
      period_type: goal.period_type,
      start_date: goal.start_date,
      end_date: goal.end_date,
      company_id: goal.company_id,
      company_name: goal.company?.name,
      created_at: goal.created_at,
      updated_at: goal.updated_at,
    };
  }
}

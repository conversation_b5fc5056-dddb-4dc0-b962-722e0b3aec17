import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Goal } from '../../entities/goal.entity';
import { GoalCreateDto } from './dto/goal-create.dto';
import { GoalUpdateDto } from './dto/goal-update.dto';
import { GoalResponseDto } from './dto/goal-response.dto';

@Injectable()
export class GoalsService {
  constructor(
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
  ) {}

  async findAll(companyId: number): Promise<GoalResponseDto[]> {
    const goals = await this.goalRepository.find({
      where: { companyId },
      order: { createdAt: 'DESC' },
    });

    return goals.map((goal) => this.mapToResponseDto(goal));
  }

  async findOne(id: number, companyId: number): Promise<GoalResponseDto> {
    const goal = await this.goalRepository.findOne({
      where: { id, companyId },
    });

    if (!goal) {
      throw new NotFoundException('Meta não encontrada');
    }

    return this.mapToResponseDto(goal);
  }

  async create(
    goalCreateDto: GoalCreateDto,
    companyId: number,
  ): Promise<GoalResponseDto> {
    const goal = this.goalRepository.create({
      ...goalCreateDto,
      startDate: new Date(goalCreateDto.startDate),
      endDate: new Date(goalCreateDto.endDate),
      currentValue: goalCreateDto.currentValue || 0,
      companyId,
    });

    const savedGoal = await this.goalRepository.save(goal);
    return this.mapToResponseDto(savedGoal);
  }

  async update(
    id: number,
    goalUpdateDto: GoalUpdateDto,
    companyId: number,
  ): Promise<GoalResponseDto> {
    const goal = await this.goalRepository.findOne({
      where: { id, companyId },
    });

    if (!goal) {
      throw new NotFoundException('Meta não encontrada');
    }

    // Atualizar campos
    Object.assign(goal, goalUpdateDto);

    if (goalUpdateDto.startDate) {
      goal.startDate = new Date(goalUpdateDto.startDate);
    }

    if (goalUpdateDto.endDate) {
      goal.endDate = new Date(goalUpdateDto.endDate);
    }

    await this.goalRepository.save(goal);

    // Buscar a meta atualizada completa
    const updatedGoal = await this.goalRepository.findOne({
      where: { id, companyId },
    });

    return this.mapToResponseDto(updatedGoal!);
  }

  async remove(id: number, companyId: number): Promise<void> {
    const goal = await this.goalRepository.findOne({
      where: { id, companyId },
    });

    if (!goal) {
      throw new NotFoundException('Meta não encontrada');
    }

    await this.goalRepository.remove(goal);
  }

  private mapToResponseDto(goal: Goal): GoalResponseDto {
    return {
      id: goal.id,
      title: goal.title,
      description: goal.description,
      targetValue: parseFloat((goal.targetValue || 0).toString()),
      currentValue: parseFloat((goal.currentValue || 0).toString()),
      goalType: goal.goalType,
      periodType: goal.periodType,
      startDate: goal.startDate,
      endDate: goal.endDate,
      createdAt: goal.createdAt,
      updatedAt: goal.updatedAt,
    };
  }
}

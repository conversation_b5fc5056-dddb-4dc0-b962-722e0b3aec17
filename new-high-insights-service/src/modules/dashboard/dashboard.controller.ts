import { Controller, Get, UseGuards } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardKPIsDto } from './dto/dashboard-kpis.dto';
import { DashboardGraphsDto } from './dto/dashboard-graphs.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';

@Controller('dashboard')
@UseGuards(JwtAuthGuard)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('kpis')
  async getKPIs(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<DashboardKPIsDto> {
    return this.dashboardService.getKPIs(companyId, isAdmin);
  }

  @Get('graphs')
  async getGraphs(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<DashboardGraphsDto> {
    return this.dashboardService.getGraphs(companyId, isAdmin);
  }
}

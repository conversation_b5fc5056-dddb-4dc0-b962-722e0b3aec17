import {
  Controller,
  Get,
  UseGuards,
} from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardKPIsDto } from './dto/dashboard-kpis.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Admin } from '../../entities/admin.entity';

@Controller('dashboard')
@UseGuards(JwtAuthGuard)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('kpis')
  async getKPIs(@CurrentUser() user: Admin): Promise<DashboardKPIsDto> {
    return this.dashboardService.getKPIs(user.companyId);
  }
}

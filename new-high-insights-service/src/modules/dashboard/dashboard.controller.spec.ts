import { Test, TestingModule } from '@nestjs/testing';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { DashboardKPIsDto } from './dto/dashboard-kpis.dto';

describe('DashboardController', () => {
  let controller: DashboardController;
  let service: DashboardService;

  const mockKPIs: DashboardKPIsDto = {
    total_revenue: 15000.5,
    total_leads: 120,
    conversion_rate: 25.5,
    monthly_sales: 5000.25,
    goals_achieved: 3,
    total_goals: 5,
    pending_followups: 8,
    total_team_members: 4,
  };

  const mockDashboardService = {
    getKPIs: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        {
          provide: DashboardService,
          useValue: mockDashboardService,
        },
      ],
    }).compile();

    controller = module.get<DashboardController>(DashboardController);
    service = module.get<DashboardService>(DashboardService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getKPIs', () => {
    it('should return dashboard KPIs for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      mockDashboardService.getKPIs.mockResolvedValue(mockKPIs);

      const result = await controller.getKPIs(companyId, isAdmin);

      expect(result).toBe(mockKPIs);
      expect(service.getKPIs).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return dashboard KPIs for global admin - all companies (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      mockDashboardService.getKPIs.mockResolvedValue(mockKPIs);

      const result = await controller.getKPIs(companyId, isAdmin);

      expect(result).toBe(mockKPIs);
      expect(service.getKPIs).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return dashboard KPIs for global admin - specific company (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      mockDashboardService.getKPIs.mockResolvedValue(mockKPIs);

      const result = await controller.getKPIs(companyId, isAdmin);

      expect(result).toBe(mockKPIs);
      expect(service.getKPIs).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should handle service errors', async () => {
      const companyId = 1;
      const isAdmin = false;
      const error = new Error('Database error');
      mockDashboardService.getKPIs.mockRejectedValue(error);

      await expect(controller.getKPIs(companyId, isAdmin)).rejects.toThrow(
        'Database error',
      );
    });
  });
});

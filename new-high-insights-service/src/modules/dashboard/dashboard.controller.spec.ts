import { Test, TestingModule } from '@nestjs/testing';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { DashboardKPIsDto } from './dto/dashboard-kpis.dto';
import { Admin } from '../../entities/admin.entity';

describe('DashboardController', () => {
  let controller: DashboardController;
  let service: DashboardService;

  const mockAdmin: Admin = {
    id: 1,
    companyId: 1,
    username: 'test',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    company: null,
  };

  const mockKPIs: DashboardKPIsDto = {
    totalRevenue: 15000.50,
    totalLeads: 120,
    conversionRate: 25.5,
    monthlySales: 5000.25,
    goalsAchieved: 3,
    totalGoals: 5,
    pendingFollowups: 8,
    totalTeamMembers: 4,
  };

  const mockDashboardService = {
    getKPIs: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        {
          provide: DashboardService,
          useValue: mockDashboardService,
        },
      ],
    }).compile();

    controller = module.get<DashboardController>(DashboardController);
    service = module.get<DashboardService>(DashboardService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getKPIs', () => {
    it('should return dashboard KPIs', async () => {
      mockDashboardService.getKPIs.mockResolvedValue(mockKPIs);

      expect(await controller.getKPIs(mockAdmin)).toBe(mockKPIs);
      expect(service.getKPIs).toHaveBeenCalledWith(mockAdmin.companyId);
    });

    it('should handle service errors', async () => {
      const error = new Error('Database error');
      mockDashboardService.getKPIs.mockRejectedValue(error);

      await expect(controller.getKPIs(mockAdmin)).rejects.toThrow('Database error');
    });
  });
});

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Followup } from '../../entities/followup.entity';
import { Goal } from '../../entities/goal.entity';
import { Lead } from '../../entities/lead.entity';
import { Sale } from '../../entities/sale.entity';
import { TeamMember } from '../../entities/team-member.entity';
import { DashboardKPIsDto } from './dto/dashboard-kpis.dto';
import {
  DashboardGraphsDto,
  LeadStatusGraphDto,
  SalesMonthGraphDto,
} from './dto/dashboard-graphs.dto';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
    @InjectRepository(Followup)
    private followupRepository: Repository<Followup>,
    @InjectRepository(TeamMember)
    private teamMemberRepository: Repository<TeamMember>,
  ) {}

  async getKPIs(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<DashboardKPIsDto> {
    // Validar parâmetros
    if (company_id === 0 && !is_admin) {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    // Receita total
    const totalRevenueQuery = this.saleRepository
      .createQueryBuilder('sale')
      .select('SUM(sale.amount)', 'total');

    if (company_id > 0) {
      totalRevenueQuery.where('sale.company_id = :company_id', { company_id });
    }
    // Se company_id === 0 && is_admin === true, não adiciona filtro (busca todas as empresas)

    const totalRevenueResult = await totalRevenueQuery.getRawOne();
    const total_revenue = parseFloat(totalRevenueResult?.total || '0');

    // Total de leads
    let leadCountCondition = {};
    if (company_id > 0) {
      leadCountCondition = { company_id: company_id };
    }
    // Se company_id === 0 && is_admin === true, condição fica vazia (busca todas as empresas)

    const total_leads = await this.leadRepository.count({
      where: leadCountCondition,
    });

    // Vendas do mês atual
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const monthlySalesQuery = this.saleRepository
      .createQueryBuilder('sale')
      .select('SUM(sale.amount)', 'total')
      .where('sale.sale_date >= :currentMonth', { currentMonth });

    if (company_id > 0) {
      monthlySalesQuery.andWhere('sale.company_id = :company_id', {
        company_id,
      });
    }

    const monthlySalesResult = await monthlySalesQuery.getRawOne();
    const monthly_sales = parseFloat(monthlySalesResult?.total || '0');

    // Taxa de conversão (leads que viraram vendas)
    const leadsWithSalesQuery = this.saleRepository
      .createQueryBuilder('sale')
      .select('COUNT(DISTINCT sale.lead_id)', 'count')
      .where('sale.lead_id IS NOT NULL');

    if (company_id > 0) {
      leadsWithSalesQuery.andWhere('sale.company_id = :company_id', {
        company_id,
      });
    }

    const leadsWithSalesResult = await leadsWithSalesQuery.getRawOne();
    const leads_with_sales = parseInt(leadsWithSalesResult?.count || '0');

    const conversion_rate =
      total_leads > 0 ? (leads_with_sales / total_leads) * 100 : 0;

    // Metas atingidas
    let goalCountCondition = {};
    if (company_id > 0) {
      goalCountCondition = { company_id: company_id };
    }
    const total_goals = await this.goalRepository.count({
      where: goalCountCondition,
    });

    const goalsAchievedQuery = this.goalRepository
      .createQueryBuilder('goal')
      .where('goal.current_value >= goal.target_value');

    if (company_id > 0) {
      goalsAchievedQuery.andWhere('goal.company_id = :company_id', {
        company_id,
      });
    }

    const goals_achieved = await goalsAchievedQuery.getCount();

    // Follow-ups pendentes
    let followupCountCondition: any = { status: 'pending' };
    if (company_id > 0) {
      followupCountCondition = {
        company_id,
        status: 'pending',
      };
    }
    const pending_followups = await this.followupRepository.count({
      where: followupCountCondition,
    });

    // Total de membros da equipe
    let teamCountCondition = {};
    if (company_id > 0) {
      teamCountCondition = { company_id: company_id };
    }
    const total_team_members = await this.teamMemberRepository.count({
      where: teamCountCondition,
    });

    return {
      total_revenue: Math.round(total_revenue * 100) / 100, // Arredondar para 2 casas decimais
      total_leads,
      conversion_rate: Math.round(conversion_rate * 100) / 100, // Arredondar para 2 casas decimais
      monthly_sales: Math.round(monthly_sales * 100) / 100, // Arredondar para 2 casas decimais
      goals_achieved,
      total_goals,
      pending_followups,
      total_team_members,
    };
  }

  async getGraphs(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<DashboardGraphsDto> {
    // Validar parâmetros
    if (company_id === 0 && !is_admin) {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    // 1. Gráfico de leads por status
    const leadsResumeQuery = this.leadRepository
      .createQueryBuilder('lead')
      .select('lead.status', 'status')
      .addSelect('COUNT(*)', 'count');

    if (company_id > 0) {
      leadsResumeQuery.where('lead.company_id = :company_id', { company_id });
    }
    // Se company_id === 0 && is_admin === true, não adiciona filtro (busca todas as empresas)

    const leadsResumeResult = await leadsResumeQuery
      .groupBy('lead.status')
      .getRawMany();

    // Calcular total para porcentagens
    const totalLeads = leadsResumeResult.reduce(
      (sum, item) => sum + parseInt(item.count),
      0,
    );

    // Cores predefinidas para os status
    const statusColors = {
      new: '#3B82F6',
      contacted: '#6B7280',
      qualified: '#F59E0B',
      bought: '#10B981',
      lost: '#EF4444',
      disqualified: '#e9cc7b',
      pending: '#8B5CF6',
    };

    const leads_resume: LeadStatusGraphDto[] = leadsResumeResult.map((item) => {
      const count = parseInt(item.count);
      const percentage =
        totalLeads > 0 ? Math.round((count / totalLeads) * 100) : 0;

      return {
        name: this.getStatusDisplayName(item.status),
        value: percentage,
        color: statusColors[item.status] || '#6B7280', // cor padrão se status não encontrado
      };
    });

    // 2. Gráfico de vendas dos últimos 6 meses
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const salesQuery = this.saleRepository
      .createQueryBuilder('sale')
      .select('EXTRACT(YEAR FROM sale.sale_date)', 'year')
      .addSelect('EXTRACT(MONTH FROM sale.sale_date)', 'month')
      .addSelect('SUM(sale.amount)', 'total')
      .where('sale.sale_date >= :sixMonthsAgo', { sixMonthsAgo });

    if (company_id > 0) {
      salesQuery.andWhere('sale.company_id = :company_id', { company_id });
    }

    const salesResult = await salesQuery
      .groupBy('EXTRACT(YEAR FROM sale.sale_date)')
      .addGroupBy('EXTRACT(MONTH FROM sale.sale_date)')
      .orderBy('year', 'ASC')
      .addOrderBy('month', 'ASC')
      .getRawMany();

    // Gerar array dos últimos 6 meses
    const lastSixMonthResume: SalesMonthGraphDto[] = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const date = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - i,
        1,
      );
      const year = date.getFullYear();
      const month = date.getMonth() + 1;

      // Buscar vendas para este mês
      const salesForMonth = salesResult.find(
        (sale) =>
          parseInt(sale.year) === year && parseInt(sale.month) === month,
      );

      lastSixMonthResume.push({
        name: this.getMonthName(month),
        vendas: salesForMonth ? parseFloat(salesForMonth.total) : 0,
      });
    }

    return {
      leads_resume,
      lastSixMonthResume,
    };
  }

  private getStatusDisplayName(status: string): string {
    const statusMap = {
      new: 'Novos',
      contacted: 'Contatados',
      qualified: 'Qualificados',
      disqualified: 'Desqualificados',
      bought: 'Comprou',
      lost: 'Perdidos',
      pending: 'Pendentes',
    };

    return statusMap[status] || status;
  }

  private getMonthName(month: number): string {
    const months = [
      'Jan',
      'Fev',
      'Mar',
      'Abr',
      'Mai',
      'Jun',
      'Jul',
      'Ago',
      'Set',
      'Out',
      'Nov',
      'Dez',
    ];

    return months[month - 1] || month.toString();
  }
}

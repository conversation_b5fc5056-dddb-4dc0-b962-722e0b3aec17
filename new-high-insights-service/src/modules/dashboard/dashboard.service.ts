import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Sale } from '../../entities/sale.entity';
import { Lead } from '../../entities/lead.entity';
import { Goal } from '../../entities/goal.entity';
import { Followup } from '../../entities/followup.entity';
import { TeamMember } from '../../entities/team-member.entity';
import { DashboardKPIsDto } from './dto/dashboard-kpis.dto';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
    @InjectRepository(Followup)
    private followupRepository: Repository<Followup>,
    @InjectRepository(TeamMember)
    private teamMemberRepository: Repository<TeamMember>,
  ) {}

  async getKPIs(companyId: number): Promise<DashboardKPIsDto> {
    // Receita total
    const totalRevenueResult = await this.saleRepository
      .createQueryBuilder('sale')
      .select('SUM(sale.amount)', 'total')
      .where('sale.companyId = :companyId', { companyId })
      .getRawOne();
    const totalRevenue = parseFloat(totalRevenueResult?.total || '0');

    // Total de leads
    const totalLeads = await this.leadRepository.count({
      where: { companyId },
    });

    // Vendas do mês atual
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const monthlySalesResult = await this.saleRepository
      .createQueryBuilder('sale')
      .select('SUM(sale.amount)', 'total')
      .where('sale.companyId = :companyId', { companyId })
      .andWhere('sale.saleDate >= :currentMonth', { currentMonth })
      .getRawOne();
    const monthlySales = parseFloat(monthlySalesResult?.total || '0');

    // Taxa de conversão (leads que viraram vendas)
    const leadsWithSalesResult = await this.saleRepository
      .createQueryBuilder('sale')
      .select('COUNT(DISTINCT sale.leadId)', 'count')
      .where('sale.companyId = :companyId', { companyId })
      .andWhere('sale.leadId IS NOT NULL')
      .getRawOne();
    const leadsWithSales = parseInt(leadsWithSalesResult?.count || '0');

    const conversionRate = totalLeads > 0 ? (leadsWithSales / totalLeads) * 100 : 0;

    // Metas atingidas
    const totalGoals = await this.goalRepository.count({
      where: { companyId },
    });

    const goalsAchieved = await this.goalRepository
      .createQueryBuilder('goal')
      .where('goal.companyId = :companyId', { companyId })
      .andWhere('goal.currentValue >= goal.targetValue')
      .getCount();

    // Follow-ups pendentes
    const pendingFollowups = await this.followupRepository.count({
      where: { 
        companyId,
        status: 'pending'
      },
    });

    // Total de membros da equipe
    const totalTeamMembers = await this.teamMemberRepository.count({
      where: { companyId },
    });

    return {
      totalRevenue: Math.round(totalRevenue * 100) / 100, // Arredondar para 2 casas decimais
      totalLeads,
      conversionRate: Math.round(conversionRate * 100) / 100, // Arredondar para 2 casas decimais
      monthlySales: Math.round(monthlySales * 100) / 100, // Arredondar para 2 casas decimais
      goalsAchieved,
      totalGoals,
      pendingFollowups,
      totalTeamMembers,
    };
  }
}

import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { Sale } from '../../entities/sale.entity';
import { Lead } from '../../entities/lead.entity';
import { Goal } from '../../entities/goal.entity';
import { Followup } from '../../entities/followup.entity';
import { TeamMember } from '../../entities/team-member.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Sale,
      Lead,
      Goal,
      Followup,
      TeamMember,
    ])
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
  exports: [DashboardService],
})
export class DashboardModule {}

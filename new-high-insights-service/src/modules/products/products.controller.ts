import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductResponseDto } from './dto/product-response.dto';
import { ProductUpdateDto } from './dto/product-update.dto';
import { ProductsService } from './products.service';

@Controller('products')
@UseGuards(JwtAuthGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<ProductResponseDto[]> {
    return this.productsService.findAll(companyId, isAdmin);
  }

  @Post()
  async create(
    @Body() productData: ProductCreateDto,
    @CompanyId() companyId: number,
  ): Promise<ProductResponseDto> {
    return this.productsService.create(productData, companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<ProductResponseDto> {
    return this.productsService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() productData: ProductUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<ProductResponseDto> {
    return this.productsService.update(id, productData, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    return this.productsService.remove(id, companyId);
  }
}

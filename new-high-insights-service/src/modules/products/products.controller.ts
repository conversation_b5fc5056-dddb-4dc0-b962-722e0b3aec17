import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductUpdateDto } from './dto/product-update.dto';
import { ProductResponseDto } from './dto/product-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';

@Controller('products')
@UseGuards(JwtAuthGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get()
  async findAll(@CurrentUser() user: any): Promise<ProductResponseDto[]> {
    return this.productsService.findAll(user.companyId);
  }

  @Post()
  async create(
    @Body() productData: ProductCreateDto,
    @CurrentUser() user: any,
  ): Promise<ProductResponseDto> {
    return this.productsService.create(productData, user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ): Promise<ProductResponseDto> {
    return this.productsService.findOne(id, user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() productData: ProductUpdateDto,
    @CurrentUser() user: any,
  ): Promise<ProductResponseDto> {
    return this.productsService.update(id, productData, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ): Promise<{ message: string }> {
    return this.productsService.remove(id, user.companyId);
  }
}

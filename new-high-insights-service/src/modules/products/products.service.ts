import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../../entities/product.entity';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductUpdateDto } from './dto/product-update.dto';
import { ProductResponseDto } from './dto/product-response.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  async findAll(companyId: number): Promise<ProductResponseDto[]> {
    const products = await this.productRepository.find({
      where: { companyId },
      order: { createdAt: 'DESC' },
    });

    return products.map(this.mapToResponseDto);
  }

  async findOne(id: number, companyId: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id, companyId },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    return this.mapToResponseDto(product);
  }

  async create(
    productData: ProductCreateDto,
    companyId: number,
  ): Promise<ProductResponseDto> {
    const product = this.productRepository.create({
      ...productData,
      companyId,
    });

    const savedProduct = await this.productRepository.save(product);
    return this.mapToResponseDto(savedProduct);
  }

  async update(
    id: number,
    productData: ProductUpdateDto,
    companyId: number,
  ): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id, companyId },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    // Atualizar apenas os campos fornecidos
    Object.keys(productData).forEach((key) => {
      if (productData[key] !== undefined) {
        product[key] = productData[key];
      }
    });

    const updatedProduct = await this.productRepository.save(product);
    return this.mapToResponseDto(updatedProduct);
  }

  async remove(id: number, companyId: number): Promise<{ message: string }> {
    const product = await this.productRepository.findOne({
      where: { id, companyId },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    await this.productRepository.remove(product);
    return { message: 'Produto deletado com sucesso' };
  }

  private mapToResponseDto(product: Product): ProductResponseDto {
    return {
      id: product.id,
      name: product.name,
      price: product.price,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    };
  }
}

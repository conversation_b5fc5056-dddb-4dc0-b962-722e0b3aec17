import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../../entities/product.entity';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductUpdateDto } from './dto/product-update.dto';
import { ProductResponseDto } from './dto/product-response.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<ProductResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const products = await this.productRepository.find({
      where: whereCondition,
      relations: ['company'],
      order: { created_at: 'DESC' },
    });

    return products.map(this.mapToResponseDto);
  }

  async findOne(id: number, company_id: number): Promise<ProductResponseDto> {
    const product = await this.productRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    return this.mapToResponseDto(product);
  }

  async create(
    productData: ProductCreateDto,
    headerCompanyId: number,
  ): Promise<ProductResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!productData.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = productData.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const product = this.productRepository.create({
      ...productData,
      company_id: finalCompanyId,
    });

    const savedProduct = await this.productRepository.save(product);

    // Busca novamente com relations para ter company_name
    const productWithCompany = await this.productRepository.findOne({
      where: { id: savedProduct.id },
      relations: ['company'],
    });

    if (!productWithCompany) {
      throw new Error('Erro ao buscar produto criado');
    }

    return this.mapToResponseDto(productWithCompany);
  }

  async update(
    id: number,
    productData: ProductUpdateDto,
    headerCompanyId: number,
  ): Promise<ProductResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!productData.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = productData.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const product = await this.productRepository.findOne({
      where: { id, company_id: finalCompanyId },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    // Atualizar apenas os campos fornecidos (exceto company_id que já foi tratado)
    Object.keys(productData).forEach((key) => {
      if (productData[key] !== undefined && key !== 'company_id') {
        product[key] = productData[key];
      }
    });

    const updatedProduct = await this.productRepository.save(product);

    // Busca novamente com relations para ter company_name
    const productWithCompany = await this.productRepository.findOne({
      where: { id: updatedProduct.id },
      relations: ['company'],
    });

    if (!productWithCompany) {
      throw new Error('Erro ao buscar produto atualizado');
    }

    return this.mapToResponseDto(productWithCompany);
  }

  async remove(id: number, company_id: number): Promise<{ message: string }> {
    const product = await this.productRepository.findOne({
      where: { id, company_id },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    await this.productRepository.remove(product);
    return { message: 'Produto deletado com sucesso' };
  }

  private mapToResponseDto(product: Product): ProductResponseDto {
    return {
      id: product.id,
      name: product.name,
      price: product.price,
      company_id: product.company_id,
      company_name: product.company?.name,
      created_at: product.created_at,
      updated_at: product.updated_at,
    };
  }
}

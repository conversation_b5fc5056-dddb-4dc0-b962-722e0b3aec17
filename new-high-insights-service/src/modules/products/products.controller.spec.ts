import { Test, TestingModule } from '@nestjs/testing';
import { ProductsController } from './products.controller';
import { ProductsService } from './products.service';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductUpdateDto } from './dto/product-update.dto';
import { NotFoundException } from '@nestjs/common';

describe('ProductsController', () => {
  let controller: ProductsController;
  let service: ProductsService;

  const mockProductsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockUser = {
    id: 1,
    username: 'testuser',
    companyId: 1,
  };

  const mockProduct = {
    id: 1,
    name: 'Test Product',
    price: 99.99,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductsController],
      providers: [
        {
          provide: ProductsService,
          useValue: mockProductsService,
        },
      ],
    }).compile();

    controller = module.get<ProductsController>(ProductsController);
    service = module.get<ProductsService>(ProductsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of products', async () => {
      const expectedProducts = [mockProduct];
      mockProductsService.findAll.mockResolvedValue(expectedProducts);

      const result = await controller.findAll(mockUser);

      expect(result).toEqual(expectedProducts);
      expect(service.findAll).toHaveBeenCalledWith(mockUser.companyId);
    });
  });

  describe('create', () => {
    it('should create a new product', async () => {
      const createDto: ProductCreateDto = {
        name: 'New Product',
        price: 149.99,
      };

      mockProductsService.create.mockResolvedValue({ ...mockProduct, ...createDto });

      const result = await controller.create(createDto, mockUser);

      expect(result).toEqual({ ...mockProduct, ...createDto });
      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single product', async () => {
      mockProductsService.findOne.mockResolvedValue(mockProduct);

      const result = await controller.findOne(1, mockUser);

      expect(result).toEqual(mockProduct);
      expect(service.findOne).toHaveBeenCalledWith(1, mockUser.companyId);
    });

    it('should throw NotFoundException when product not found', async () => {
      mockProductsService.findOne.mockRejectedValue(new NotFoundException('Produto não encontrado'));

      await expect(controller.findOne(999, mockUser)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a product', async () => {
      const updateDto: ProductUpdateDto = {
        name: 'Updated Product',
        price: 199.99,
      };

      const updatedProduct = { ...mockProduct, ...updateDto };
      mockProductsService.update.mockResolvedValue(updatedProduct);

      const result = await controller.update(1, updateDto, mockUser);

      expect(result).toEqual(updatedProduct);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockUser.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a product', async () => {
      const expectedResponse = { message: 'Produto deletado com sucesso' };
      mockProductsService.remove.mockResolvedValue(expectedResponse);

      const result = await controller.remove(1, mockUser);

      expect(result).toEqual(expectedResponse);
      expect(service.remove).toHaveBeenCalledWith(1, mockUser.companyId);
    });
  });
});

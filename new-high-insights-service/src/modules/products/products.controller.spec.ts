import { Test, TestingModule } from '@nestjs/testing';
import { ProductsController } from './products.controller';
import { ProductsService } from './products.service';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductUpdateDto } from './dto/product-update.dto';
import { NotFoundException } from '@nestjs/common';

describe('ProductsController', () => {
  let controller: ProductsController;
  let service: ProductsService;

  const mockProductsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockProduct = {
    id: 1,
    name: 'Test Product',
    price: 99.99,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductsController],
      providers: [
        {
          provide: ProductsService,
          useValue: mockProductsService,
        },
      ],
    }).compile();

    controller = module.get<ProductsController>(ProductsController);
    service = module.get<ProductsService>(ProductsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return products for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const expectedProducts = [mockProduct];
      mockProductsService.findAll.mockResolvedValue(expectedProducts);

      const result = await controller.findAll(companyId, isAdmin);

      expect(result).toEqual(expectedProducts);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all products for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const expectedProducts = [mockProduct];
      mockProductsService.findAll.mockResolvedValue(expectedProducts);

      const result = await controller.findAll(companyId, isAdmin);

      expect(result).toEqual(expectedProducts);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return products for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const expectedProducts = [mockProduct];
      mockProductsService.findAll.mockResolvedValue(expectedProducts);

      const result = await controller.findAll(companyId, isAdmin);

      expect(result).toEqual(expectedProducts);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new product', async () => {
      const companyId = 1;
      const createDto: ProductCreateDto = {
        name: 'New Product',
        price: 149.99,
      };

      mockProductsService.create.mockResolvedValue({
        ...mockProduct,
        ...createDto,
      });

      const result = await controller.create(createDto, companyId);

      expect(result).toEqual({ ...mockProduct, ...createDto });
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single product', async () => {
      const companyId = 1;
      mockProductsService.findOne.mockResolvedValue(mockProduct);

      const result = await controller.findOne(1, companyId);

      expect(result).toEqual(mockProduct);
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when product not found', async () => {
      const companyId = 1;
      mockProductsService.findOne.mockRejectedValue(
        new NotFoundException('Produto não encontrado'),
      );

      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a product', async () => {
      const companyId = 1;
      const updateDto: ProductUpdateDto = {
        name: 'Updated Product',
        price: 199.99,
      };

      const updatedProduct = { ...mockProduct, ...updateDto };
      mockProductsService.update.mockResolvedValue(updatedProduct);

      const result = await controller.update(1, updateDto, companyId);

      expect(result).toEqual(updatedProduct);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a product', async () => {
      const companyId = 1;
      const expectedResponse = { message: 'Produto deletado com sucesso' };
      mockProductsService.remove.mockResolvedValue(expectedResponse);

      const result = await controller.remove(1, companyId);

      expect(result).toEqual(expectedResponse);
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

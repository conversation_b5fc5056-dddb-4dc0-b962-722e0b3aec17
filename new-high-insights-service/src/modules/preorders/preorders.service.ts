import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Preorder } from '../../entities/preorder.entity';
import { PreorderCreateDto } from './dto/preorder-create.dto';
import { PreorderUpdateDto } from './dto/preorder-update.dto';
import { PreorderResponseDto } from './dto/preorder-response.dto';

@Injectable()
export class PreordersService {
  constructor(
    @InjectRepository(Preorder)
    private preorderRepository: Repository<Preorder>,
  ) {}

  async create(
    preorderCreateDto: PreorderCreateDto,
    headerCompanyId: number,
  ): Promise<PreorderResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!preorderCreateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = preorderCreateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const preorder = this.preorderRepository.create({
      ...preorderCreateDto,
      status: preorderCreateDto.status || 'pending',
      company_id: finalCompanyId,
    });

    const savedPreorder = await this.preorderRepository.save(preorder);
    return this.mapToResponseDto(savedPreorder);
  }

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<PreorderResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const preorders = await this.preorderRepository.find({
      where: whereCondition,
      relations: ['company'],
      order: { created_at: 'DESC' },
    });

    return preorders.map((preorder) => this.mapToResponseDto(preorder));
  }

  async findOne(id: number, company_id: number): Promise<PreorderResponseDto> {
    const preorder = await this.preorderRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!preorder) {
      throw new NotFoundException('Pré-venda não encontrada');
    }

    return this.mapToResponseDto(preorder);
  }

  async update(
    id: number,
    preorderUpdateDto: PreorderUpdateDto,
    headerCompanyId: number,
  ): Promise<PreorderResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!preorderUpdateDto.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = preorderUpdateDto.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const preorder = await this.preorderRepository.findOne({
      where: { id, company_id: finalCompanyId },
      relations: ['company'],
    });

    if (!preorder) {
      throw new NotFoundException('Pré-venda não encontrada');
    }

    // Atualizar campos
    Object.assign(preorder, preorderUpdateDto);

    await this.preorderRepository.save(preorder);

    // Buscar o preorder atualizado completo
    const updatedPreorder = await this.preorderRepository.findOne({
      where: { id, company_id: finalCompanyId },
      relations: ['company'],
    });

    return this.mapToResponseDto(updatedPreorder!);
  }

  async remove(id: number, company_id: number): Promise<void> {
    const preorder = await this.preorderRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!preorder) {
      throw new NotFoundException('Pré-venda não encontrada');
    }

    await this.preorderRepository.remove(preorder);
  }

  private mapToResponseDto(preorder: Preorder): PreorderResponseDto {
    return {
      id: preorder.id,
      lead_id: preorder.lead_id,
      product_id: preorder.product_id,
      amount: parseFloat((preorder.amount || 0).toString()),
      status: preorder.status,
      notes: preorder.notes,
      company_id: preorder.company_id,
      company_name: preorder.company?.name,
      created_at: preorder.created_at,
      updated_at: preorder.updated_at,
    };
  }
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Preorder } from '../../entities/preorder.entity';
import { PreorderCreateDto } from './dto/preorder-create.dto';
import { PreorderUpdateDto } from './dto/preorder-update.dto';
import { PreorderResponseDto } from './dto/preorder-response.dto';

@Injectable()
export class PreordersService {
  constructor(
    @InjectRepository(Preorder)
    private preorderRepository: Repository<Preorder>,
  ) {}

  async create(preorderCreateDto: PreorderCreateDto, companyId: number): Promise<PreorderResponseDto> {
    const preorder = this.preorderRepository.create({
      ...preorderCreateDto,
      status: preorderCreateDto.status || 'pending',
      companyId,
    });

    const savedPreorder = await this.preorderRepository.save(preorder);
    return this.mapToResponseDto(savedPreorder);
  }

  async findAll(companyId: number): Promise<PreorderResponseDto[]> {
    const preorders = await this.preorderRepository.find({
      where: { companyId },
      order: { createdAt: 'DESC' },
    });

    return preorders.map(preorder => this.mapToResponseDto(preorder));
  }

  async findOne(id: number, companyId: number): Promise<PreorderResponseDto> {
    const preorder = await this.preorderRepository.findOne({
      where: { id, companyId },
    });

    if (!preorder) {
      throw new NotFoundException('Pré-venda não encontrada');
    }

    return this.mapToResponseDto(preorder);
  }

  async update(id: number, preorderUpdateDto: PreorderUpdateDto, companyId: number): Promise<PreorderResponseDto> {
    const preorder = await this.preorderRepository.findOne({
      where: { id, companyId },
    });

    if (!preorder) {
      throw new NotFoundException('Pré-venda não encontrada');
    }

    // Atualizar campos
    Object.assign(preorder, preorderUpdateDto);

    await this.preorderRepository.save(preorder);
    
    // Buscar o preorder atualizado completo
    const updatedPreorder = await this.preorderRepository.findOne({
      where: { id, companyId },
    });
    
    return this.mapToResponseDto(updatedPreorder!);
  }

  async remove(id: number, companyId: number): Promise<void> {
    const preorder = await this.preorderRepository.findOne({
      where: { id, companyId },
    });

    if (!preorder) {
      throw new NotFoundException('Pré-venda não encontrada');
    }

    await this.preorderRepository.remove(preorder);
  }

  private mapToResponseDto(preorder: Preorder): PreorderResponseDto {
    return {
      id: preorder.id,
      leadId: preorder.leadId,
      productId: preorder.productId,
      amount: parseFloat((preorder.amount || 0).toString()),
      status: preorder.status,
      notes: preorder.notes,
      createdAt: preorder.createdAt,
      updatedAt: preorder.updatedAt,
    };
  }
}

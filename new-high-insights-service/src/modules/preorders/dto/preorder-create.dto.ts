import {
  IsString,
  IsOptional,
  IsInt,
  IsIn,
  Min,
  IsPositive,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';

export class PreorderCreateDto {
  @IsOptional()
  @IsInt({ message: 'ID do lead deve ser um número inteiro' })
  @Min(1, { message: 'ID do lead deve ser maior que 0' })
  lead_id?: number;

  @IsOptional()
  @IsInt({ message: 'ID do produto deve ser um número inteiro' })
  @Min(1, { message: 'ID do produto deve ser maior que 0' })
  product_id?: number;

  @IsPositive({ message: 'Valor deve ser positivo' })
  amount: number;

  @IsOptional()
  @IsIn(['pending', 'confirmed', 'cancelled'], {
    message: 'Status deve ser: pending, confirmed ou cancelled',
  })
  status?: string;

  @IsOptional()
  @IsString({ message: 'Notas devem ser uma string' })
  notes?: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  company_id?: number;
}

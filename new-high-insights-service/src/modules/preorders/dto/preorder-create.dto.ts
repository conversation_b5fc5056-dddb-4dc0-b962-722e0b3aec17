import {
  IsString,
  IsOptional,
  IsInt,
  IsIn,
  Min,
  IsPositive,
} from 'class-validator';

export class PreorderCreateDto {
  @IsOptional()
  @IsInt({ message: 'ID do lead deve ser um número inteiro' })
  @Min(1, { message: 'ID do lead deve ser maior que 0' })
  leadId?: number;

  @IsOptional()
  @IsInt({ message: 'ID do produto deve ser um número inteiro' })
  @Min(1, { message: 'ID do produto deve ser maior que 0' })
  productId?: number;

  @IsPositive({ message: 'Valor deve ser positivo' })
  amount: number;

  @IsOptional()
  @IsIn(['pending', 'confirmed', 'cancelled'], {
    message: 'Status deve ser: pending, confirmed ou cancelled',
  })
  status?: string;

  @IsOptional()
  @IsString({ message: 'Notas devem ser uma string' })
  notes?: string;
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PreordersController } from './preorders.controller';
import { PreordersService } from './preorders.service';
import { Preorder } from '../../entities/preorder.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Preorder])],
  controllers: [PreordersController],
  providers: [PreordersService],
  exports: [PreordersService],
})
export class PreordersModule {}

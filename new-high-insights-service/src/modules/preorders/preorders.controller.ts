import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { PreordersService } from './preorders.service';
import { PreorderCreateDto } from './dto/preorder-create.dto';
import { PreorderUpdateDto } from './dto/preorder-update.dto';
import { PreorderResponseDto } from './dto/preorder-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Admin } from '../../entities/admin.entity';

@Controller('preorders')
@UseGuards(JwtAuthGuard)
export class PreordersController {
  constructor(private readonly preordersService: PreordersService) {}

  @Post()
  async create(
    @Body() preorderCreateDto: PreorderCreateDto,
    @CurrentUser() user: Admin,
  ): Promise<PreorderResponseDto> {
    return this.preordersService.create(preorderCreateDto, user.companyId);
  }

  @Get()
  async findAll(@CurrentUser() user: Admin): Promise<PreorderResponseDto[]> {
    return this.preordersService.findAll(user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<PreorderResponseDto> {
    return this.preordersService.findOne(id, user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() preorderUpdateDto: PreorderUpdateDto,
    @CurrentUser() user: Admin,
  ): Promise<PreorderResponseDto> {
    return this.preordersService.update(id, preorderUpdateDto, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: Admin,
  ): Promise<{ message: string }> {
    await this.preordersService.remove(id, user.companyId);
    return { message: 'Pré-venda deletada com sucesso' };
  }
}

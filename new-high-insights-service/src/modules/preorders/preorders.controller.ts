import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { PreordersService } from './preorders.service';
import { PreorderCreateDto } from './dto/preorder-create.dto';
import { PreorderUpdateDto } from './dto/preorder-update.dto';
import { PreorderResponseDto } from './dto/preorder-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';

@Controller('preorders')
@UseGuards(JwtAuthGuard)
export class PreordersController {
  constructor(private readonly preordersService: PreordersService) {}

  @Post()
  async create(
    @Body() preorderCreateDto: PreorderCreateDto,
    @CompanyId() companyId: number,
  ): Promise<PreorderResponseDto> {
    return this.preordersService.create(preorderCreateDto, companyId);
  }

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<PreorderResponseDto[]> {
    return this.preordersService.findAll(companyId, isAdmin);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<PreorderResponseDto> {
    return this.preordersService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() preorderUpdateDto: PreorderUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<PreorderResponseDto> {
    return this.preordersService.update(id, preorderUpdateDto, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    await this.preordersService.remove(id, companyId);
    return { message: 'Pré-venda deletada com sucesso' };
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { PreordersController } from './preorders.controller';
import { PreordersService } from './preorders.service';
import { PreorderCreateDto } from './dto/preorder-create.dto';
import { PreorderUpdateDto } from './dto/preorder-update.dto';
import { PreorderResponseDto } from './dto/preorder-response.dto';
import { Admin } from '../../entities/admin.entity';
import { NotFoundException } from '@nestjs/common';

describe('PreordersController', () => {
  let controller: PreordersController;
  let service: PreordersService;

  const mockAdmin: Admin = {
    id: 1,
    companyId: 1,
    username: 'test',
    password: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
    company: null,
  };

  const mockPreorderResponse: PreorderResponseDto = {
    id: 1,
    leadId: 1,
    productId: 1,
    amount: 1500.00,
    status: 'pending',
    notes: 'Pré-venda do produto premium',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPreordersService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PreordersController],
      providers: [
        {
          provide: PreordersService,
          useValue: mockPreordersService,
        },
      ],
    }).compile();

    controller = module.get<PreordersController>(PreordersController);
    service = module.get<PreordersService>(PreordersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of preorders', async () => {
      const result = [mockPreorderResponse];
      mockPreordersService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(mockAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(mockAdmin.companyId);
    });
  });

  describe('create', () => {
    it('should create a new preorder', async () => {
      const createDto: PreorderCreateDto = {
        leadId: 1,
        productId: 1,
        amount: 1500.00,
        status: 'pending',
        notes: 'Pré-venda do produto premium',
      };

      mockPreordersService.create.mockResolvedValue(mockPreorderResponse);

      expect(await controller.create(createDto, mockAdmin)).toBe(mockPreorderResponse);
      expect(service.create).toHaveBeenCalledWith(createDto, mockAdmin.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single preorder', async () => {
      mockPreordersService.findOne.mockResolvedValue(mockPreorderResponse);

      expect(await controller.findOne(1, mockAdmin)).toBe(mockPreorderResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });

    it('should throw NotFoundException when preorder not found', async () => {
      mockPreordersService.findOne.mockRejectedValue(new NotFoundException('Pré-venda não encontrada'));

      await expect(controller.findOne(999, mockAdmin)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a preorder', async () => {
      const updateDto: PreorderUpdateDto = {
        status: 'confirmed',
        notes: 'Pré-venda confirmada pelo cliente',
      };

      const updatedPreorder = { ...mockPreorderResponse, ...updateDto };
      mockPreordersService.update.mockResolvedValue(updatedPreorder);

      expect(await controller.update(1, updateDto, mockAdmin)).toBe(updatedPreorder);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockAdmin.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a preorder', async () => {
      mockPreordersService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(1, mockAdmin);
      expect(result).toEqual({ message: 'Pré-venda deletada com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, mockAdmin.companyId);
    });
  });
});

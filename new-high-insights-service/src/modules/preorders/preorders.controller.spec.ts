import { Test, TestingModule } from '@nestjs/testing';
import { PreordersController } from './preorders.controller';
import { PreordersService } from './preorders.service';
import { PreorderCreateDto } from './dto/preorder-create.dto';
import { PreorderUpdateDto } from './dto/preorder-update.dto';
import { PreorderResponseDto } from './dto/preorder-response.dto';
import { NotFoundException } from '@nestjs/common';

describe('PreordersController', () => {
  let controller: PreordersController;
  let service: PreordersService;

  const mockPreorderResponse: PreorderResponseDto = {
    id: 1,
    lead_id: 1,
    product_id: 1,
    amount: 1500.0,
    status: 'pending',
    notes: 'Pré-venda do produto premium',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockPreordersService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PreordersController],
      providers: [
        {
          provide: PreordersService,
          useValue: mockPreordersService,
        },
      ],
    }).compile();

    controller = module.get<PreordersController>(PreordersController);
    service = module.get<PreordersService>(PreordersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return preorders for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const result = [mockPreorderResponse];
      mockPreordersService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all preorders for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const result = [mockPreorderResponse];
      mockPreordersService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return preorders for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const result = [mockPreorderResponse];
      mockPreordersService.findAll.mockResolvedValue(result);

      expect(await controller.findAll(companyId, isAdmin)).toBe(result);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new preorder', async () => {
      const createDto: PreorderCreateDto = {
        lead_id: 1,
        product_id: 1,
        amount: 1500.0,
        status: 'pending',
        notes: 'Pré-venda do produto premium',
      };

      mockPreordersService.create.mockResolvedValue(mockPreorderResponse);

      const companyId = 1;
      expect(await controller.create(createDto, companyId)).toBe(
        mockPreorderResponse,
      );
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single preorder', async () => {
      mockPreordersService.findOne.mockResolvedValue(mockPreorderResponse);

      const companyId = 1;
      expect(await controller.findOne(1, companyId)).toBe(mockPreorderResponse);
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when preorder not found', async () => {
      mockPreordersService.findOne.mockRejectedValue(
        new NotFoundException('Pré-venda não encontrada'),
      );

      const companyId = 1;
      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a preorder', async () => {
      const updateDto: PreorderUpdateDto = {
        status: 'confirmed',
        notes: 'Pré-venda confirmada pelo cliente',
      };

      const updatedPreorder = { ...mockPreorderResponse, ...updateDto };
      mockPreordersService.update.mockResolvedValue(updatedPreorder);

      const companyId = 1;
      expect(await controller.update(1, updateDto, companyId)).toBe(
        updatedPreorder,
      );
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a preorder', async () => {
      mockPreordersService.remove.mockResolvedValue(undefined);

      const companyId = 1;
      const result = await controller.remove(1, companyId);
      expect(result).toEqual({ message: 'Pré-venda deletada com sucesso' });
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

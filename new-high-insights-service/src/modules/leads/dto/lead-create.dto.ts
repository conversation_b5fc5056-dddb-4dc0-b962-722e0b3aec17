import { IsNotEmpty, <PERSON>Optional, IsString, <PERSON>N<PERSON>ber } from 'class-validator';

export class LeadCreateDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  social_media?: string;

  @IsString()
  @IsOptional()
  source?: string;

  @IsString()
  @IsOptional()
  status?: string = 'new';

  @IsNumber()
  @IsOptional()
  company_id?: number;
}

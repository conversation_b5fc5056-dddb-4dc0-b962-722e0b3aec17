import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Lead } from '../../entities/lead.entity';
import { LeadCreateDto } from './dto/lead-create.dto';
import { LeadUpdateDto } from './dto/lead-update.dto';
import { LeadResponseDto } from './dto/lead-response.dto';

@Injectable()
export class LeadsService {
  constructor(
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
  ) {}

  async findAll(companyId: number): Promise<LeadResponseDto[]> {
    const leads = await this.leadRepository.find({
      where: { companyId },
      order: { createdAt: 'DESC' },
    });

    return leads.map(this.mapToResponseDto);
  }

  async findOne(id: number, companyId: number): Promise<LeadResponseDto> {
    const lead = await this.leadRepository.findOne({
      where: { id, companyId },
    });

    if (!lead) {
      throw new NotFoundException('Lead não encontrado');
    }

    return this.mapToResponseDto(lead);
  }

  async create(
    leadData: LeadCreateDto,
    companyId: number,
  ): Promise<LeadResponseDto> {
    const lead = this.leadRepository.create({
      ...leadData,
      companyId,
    });

    const savedLead = await this.leadRepository.save(lead);
    return this.mapToResponseDto(savedLead);
  }

  async update(
    id: number,
    leadData: LeadUpdateDto,
    companyId: number,
  ): Promise<LeadResponseDto> {
    const lead = await this.leadRepository.findOne({
      where: { id, companyId },
    });

    if (!lead) {
      throw new NotFoundException('Lead não encontrado');
    }

    // Atualizar apenas os campos fornecidos
    Object.keys(leadData).forEach((key) => {
      if (leadData[key] !== undefined) {
        lead[key] = leadData[key];
      }
    });

    const updatedLead = await this.leadRepository.save(lead);
    return this.mapToResponseDto(updatedLead);
  }

  async remove(id: number, companyId: number): Promise<{ message: string }> {
    const lead = await this.leadRepository.findOne({
      where: { id, companyId },
    });

    if (!lead) {
      throw new NotFoundException('Lead não encontrado');
    }

    await this.leadRepository.remove(lead);
    return { message: 'Lead deletado com sucesso' };
  }

  private mapToResponseDto(lead: Lead): LeadResponseDto {
    return {
      id: lead.id,
      name: lead.name,
      email: lead.email,
      phone: lead.phone,
      socialMedia: lead.socialMedia,
      source: lead.source,
      status: lead.status,
      createdAt: lead.createdAt,
      updatedAt: lead.updatedAt,
    };
  }
}

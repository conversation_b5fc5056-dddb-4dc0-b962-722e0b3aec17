import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Lead } from '../../entities/lead.entity';
import { LeadCreateDto } from './dto/lead-create.dto';
import { LeadResponseDto } from './dto/lead-response.dto';
import { LeadUpdateDto } from './dto/lead-update.dto';

@Injectable()
export class LeadsService {
  constructor(
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
  ) {}

  async findAll(
    company_id: number,
    is_admin: boolean = false,
  ): Promise<LeadResponseDto[]> {
    let whereCondition = {};

    if (company_id === 0 && is_admin) {
      // Retorna todos os registros de todas as empresas
      whereCondition = {};
    } else if (company_id > 0) {
      // Retorna registros da empresa específica (tanto para admin quanto usuário normal)
      whereCondition = { company_id };
    } else {
      throw new Error(
        'company_id deve ser maior que 0 quando is_admin for false',
      );
    }

    const leads = await this.leadRepository.find({
      where: whereCondition,
      relations: ['company'],
      order: { created_at: 'DESC' },
    });

    return leads.map(this.mapToResponseDto);
  }

  async findOne(id: number, company_id: number): Promise<LeadResponseDto> {
    const lead = await this.leadRepository.findOne({
      where: { id, company_id },
      relations: ['company'],
    });

    if (!lead) {
      throw new NotFoundException('Lead não encontrado');
    }

    return this.mapToResponseDto(lead);
  }

  async create(
    leadData: LeadCreateDto,
    headerCompanyId: number,
  ): Promise<LeadResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!leadData.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = leadData.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const lead = this.leadRepository.create({
      ...leadData,
      company_id: finalCompanyId,
    });

    const savedLead = await this.leadRepository.save(lead);

    // Busca novamente com relations para ter company_name
    const leadWithCompany = await this.leadRepository.findOne({
      where: { id: savedLead.id },
      relations: ['company'],
    });

    if (!leadWithCompany) {
      throw new Error('Erro ao buscar lead criado');
    }

    return this.mapToResponseDto(leadWithCompany);
  }

  async update(
    id: number,
    leadData: LeadUpdateDto,
    headerCompanyId: number,
  ): Promise<LeadResponseDto> {
    // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
    let finalCompanyId: number;

    if (headerCompanyId > 0) {
      finalCompanyId = headerCompanyId;
    } else if (headerCompanyId === 0) {
      if (!leadData.company_id) {
        throw new Error(
          'company_id é obrigatório na requisição quando header company-id for 0',
        );
      }
      finalCompanyId = leadData.company_id;
    } else {
      throw new Error('company_id do header deve ser >= 0');
    }

    const lead = await this.leadRepository.findOne({
      where: { id, company_id: finalCompanyId },
    });

    if (!lead) {
      throw new NotFoundException('Lead não encontrado');
    }

    // Atualizar apenas os campos fornecidos (exceto company_id que já foi tratado)
    Object.keys(leadData).forEach((key) => {
      if (leadData[key] !== undefined && key !== 'company_id') {
        lead[key] = leadData[key];
      }
    });

    const updatedLead = await this.leadRepository.save(lead);

    // Busca novamente com relations para ter company_name
    const leadWithCompany = await this.leadRepository.findOne({
      where: { id: updatedLead.id },
      relations: ['company'],
    });

    if (!leadWithCompany) {
      throw new Error('Erro ao buscar lead atualizado');
    }

    return this.mapToResponseDto(leadWithCompany);
  }

  async remove(id: number, company_id: number): Promise<{ message: string }> {
    const lead = await this.leadRepository.findOne({
      where: { id, company_id },
    });

    if (!lead) {
      throw new NotFoundException('Lead não encontrado');
    }

    await this.leadRepository.remove(lead);
    return { message: 'Lead deletado com sucesso' };
  }

  private mapToResponseDto(lead: Lead): LeadResponseDto {
    return {
      id: lead.id,
      name: lead.name,
      email: lead.email,
      phone: lead.phone,
      social_media: lead.social_media,
      source: lead.source,
      status: lead.status,
      company_name: lead.company.name,
      company_id: lead.company_id,
      created_at: lead.created_at,
      updated_at: lead.updated_at,
    };
  }
}

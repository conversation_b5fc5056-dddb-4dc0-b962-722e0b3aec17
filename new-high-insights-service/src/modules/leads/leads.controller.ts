import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { CompanyId, IsAdmin } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { LeadCreateDto } from './dto/lead-create.dto';
import { LeadResponseDto } from './dto/lead-response.dto';
import { LeadUpdateDto } from './dto/lead-update.dto';
import { LeadsService } from './leads.service';

@Controller('leads')
@UseGuards(JwtAuthGuard)
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  @Get()
  async findAll(
    @CompanyId() companyId: number,
    @IsAdmin() isAdmin: boolean,
  ): Promise<LeadResponseDto[]> {
    return this.leadsService.findAll(companyId, isAdmin);
  }

  @Post()
  async create(
    @Body() leadData: LeadCreateDto,
    @CompanyId() companyId: number,
  ): Promise<LeadResponseDto> {
    return this.leadsService.create(leadData, companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<LeadResponseDto> {
    return this.leadsService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() leadData: LeadUpdateDto,
    @CompanyId() companyId: number,
  ): Promise<LeadResponseDto> {
    return this.leadsService.update(id, leadData, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CompanyId() companyId: number,
  ): Promise<{ message: string }> {
    return this.leadsService.remove(id, companyId);
  }
}

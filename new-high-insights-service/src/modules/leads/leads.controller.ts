import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { LeadsService } from './leads.service';
import { LeadCreateDto } from './dto/lead-create.dto';
import { LeadUpdateDto } from './dto/lead-update.dto';
import { LeadResponseDto } from './dto/lead-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';

@Controller('leads')
@UseGuards(JwtAuthGuard)
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  @Get()
  async findAll(@CurrentUser() user: any): Promise<LeadResponseDto[]> {
    return this.leadsService.findAll(user.companyId);
  }

  @Post()
  async create(
    @Body() leadData: LeadCreateDto,
    @CurrentUser() user: any,
  ): Promise<LeadResponseDto> {
    return this.leadsService.create(leadData, user.companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ): Promise<LeadResponseDto> {
    return this.leadsService.findOne(id, user.companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() leadData: LeadUpdateDto,
    @CurrentUser() user: any,
  ): Promise<LeadResponseDto> {
    return this.leadsService.update(id, leadData, user.companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ): Promise<{ message: string }> {
    return this.leadsService.remove(id, user.companyId);
  }
}

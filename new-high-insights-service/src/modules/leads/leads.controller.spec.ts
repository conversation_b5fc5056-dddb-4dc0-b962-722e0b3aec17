import { Test, TestingModule } from '@nestjs/testing';
import { LeadsController } from './leads.controller';
import { LeadsService } from './leads.service';
import { LeadCreateDto } from './dto/lead-create.dto';
import { LeadUpdateDto } from './dto/lead-update.dto';
import { NotFoundException } from '@nestjs/common';

describe('LeadsController', () => {
  let controller: LeadsController;
  let service: LeadsService;

  const mockLeadsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockUser = {
    id: 1,
    username: 'testuser',
    companyId: 1,
  };

  const mockLead = {
    id: 1,
    name: 'Test Lead',
    email: '<EMAIL>',
    phone: '*********',
    socialMedia: '@testlead',
    source: 'website',
    status: 'new',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LeadsController],
      providers: [
        {
          provide: LeadsService,
          useValue: mockLeadsService,
        },
      ],
    }).compile();

    controller = module.get<LeadsController>(LeadsController);
    service = module.get<LeadsService>(LeadsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of leads', async () => {
      const expectedLeads = [mockLead];
      mockLeadsService.findAll.mockResolvedValue(expectedLeads);

      const result = await controller.findAll(mockUser);

      expect(result).toEqual(expectedLeads);
      expect(service.findAll).toHaveBeenCalledWith(mockUser.companyId);
    });
  });

  describe('create', () => {
    it('should create a new lead', async () => {
      const createDto: LeadCreateDto = {
        name: 'New Lead',
        email: '<EMAIL>',
        phone: '*********',
        socialMedia: '@newlead',
        source: 'referral',
        status: 'new',
      };

      mockLeadsService.create.mockResolvedValue({ ...mockLead, ...createDto });

      const result = await controller.create(createDto, mockUser);

      expect(result).toEqual({ ...mockLead, ...createDto });
      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single lead', async () => {
      mockLeadsService.findOne.mockResolvedValue(mockLead);

      const result = await controller.findOne(1, mockUser);

      expect(result).toEqual(mockLead);
      expect(service.findOne).toHaveBeenCalledWith(1, mockUser.companyId);
    });

    it('should throw NotFoundException when lead not found', async () => {
      mockLeadsService.findOne.mockRejectedValue(new NotFoundException('Lead não encontrado'));

      await expect(controller.findOne(999, mockUser)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a lead', async () => {
      const updateDto: LeadUpdateDto = {
        name: 'Updated Lead',
        status: 'contacted',
      };

      const updatedLead = { ...mockLead, ...updateDto };
      mockLeadsService.update.mockResolvedValue(updatedLead);

      const result = await controller.update(1, updateDto, mockUser);

      expect(result).toEqual(updatedLead);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, mockUser.companyId);
    });
  });

  describe('remove', () => {
    it('should remove a lead', async () => {
      const expectedResponse = { message: 'Lead deletado com sucesso' };
      mockLeadsService.remove.mockResolvedValue(expectedResponse);

      const result = await controller.remove(1, mockUser);

      expect(result).toEqual(expectedResponse);
      expect(service.remove).toHaveBeenCalledWith(1, mockUser.companyId);
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { LeadsController } from './leads.controller';
import { LeadsService } from './leads.service';
import { LeadCreateDto } from './dto/lead-create.dto';
import { LeadUpdateDto } from './dto/lead-update.dto';
import { NotFoundException } from '@nestjs/common';

describe('LeadsController', () => {
  let controller: LeadsController;
  let service: LeadsService;

  const mockLeadsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockLead = {
    id: 1,
    name: 'Test Lead',
    email: '<EMAIL>',
    phone: '123456789',
    social_media: '@testlead',
    source: 'website',
    status: 'new',
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LeadsController],
      providers: [
        {
          provide: LeadsService,
          useValue: mockLeadsService,
        },
      ],
    }).compile();

    controller = module.get<LeadsController>(LeadsController);
    service = module.get<LeadsService>(LeadsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return leads for normal user (company_id > 0, is_admin = false)', async () => {
      const companyId = 1;
      const isAdmin = false;
      const expectedLeads = [mockLead];
      mockLeadsService.findAll.mockResolvedValue(expectedLeads);

      const result = await controller.findAll(companyId, isAdmin);

      expect(result).toEqual(expectedLeads);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return all leads for global admin (company_id = 0, is_admin = true)', async () => {
      const companyId = 0;
      const isAdmin = true;
      const expectedLeads = [mockLead];
      mockLeadsService.findAll.mockResolvedValue(expectedLeads);

      const result = await controller.findAll(companyId, isAdmin);

      expect(result).toEqual(expectedLeads);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });

    it('should return leads for specific company as admin (company_id > 0, is_admin = true)', async () => {
      const companyId = 1;
      const isAdmin = true;
      const expectedLeads = [mockLead];
      mockLeadsService.findAll.mockResolvedValue(expectedLeads);

      const result = await controller.findAll(companyId, isAdmin);

      expect(result).toEqual(expectedLeads);
      expect(service.findAll).toHaveBeenCalledWith(companyId, isAdmin);
    });
  });

  describe('create', () => {
    it('should create a new lead', async () => {
      const companyId = 1;
      const createDto: LeadCreateDto = {
        name: 'New Lead',
        email: '<EMAIL>',
        phone: '*********',
        social_media: '@newlead',
        source: 'referral',
        status: 'new',
      };

      mockLeadsService.create.mockResolvedValue({ ...mockLead, ...createDto });

      const result = await controller.create(createDto, companyId);

      expect(result).toEqual({ ...mockLead, ...createDto });
      expect(service.create).toHaveBeenCalledWith(createDto, companyId);
    });
  });

  describe('findOne', () => {
    it('should return a single lead', async () => {
      const companyId = 1;
      mockLeadsService.findOne.mockResolvedValue(mockLead);

      const result = await controller.findOne(1, companyId);

      expect(result).toEqual(mockLead);
      expect(service.findOne).toHaveBeenCalledWith(1, companyId);
    });

    it('should throw NotFoundException when lead not found', async () => {
      const companyId = 1;
      mockLeadsService.findOne.mockRejectedValue(
        new NotFoundException('Lead não encontrado'),
      );

      await expect(controller.findOne(999, companyId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a lead', async () => {
      const companyId = 1;
      const updateDto: LeadUpdateDto = {
        name: 'Updated Lead',
        status: 'contacted',
      };

      const updatedLead = { ...mockLead, ...updateDto };
      mockLeadsService.update.mockResolvedValue(updatedLead);

      const result = await controller.update(1, updateDto, companyId);

      expect(result).toEqual(updatedLead);
      expect(service.update).toHaveBeenCalledWith(1, updateDto, companyId);
    });
  });

  describe('remove', () => {
    it('should remove a lead', async () => {
      const companyId = 1;
      const expectedResponse = { message: 'Lead deletado com sucesso' };
      mockLeadsService.remove.mockResolvedValue(expectedResponse);

      const result = await controller.remove(1, companyId);

      expect(result).toEqual(expectedResponse);
      expect(service.remove).toHaveBeenCalledWith(1, companyId);
    });
  });
});

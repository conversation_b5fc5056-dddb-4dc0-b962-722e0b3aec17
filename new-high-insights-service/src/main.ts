import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configurar validação global
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Configurar CORS se necessário
  app.enableCors();

  await app.listen(process.env.PORT ?? 3000);
  console.log(`🚀 Aplicação rodando na porta ${process.env.PORT ?? 3000}`);
}
bootstrap();

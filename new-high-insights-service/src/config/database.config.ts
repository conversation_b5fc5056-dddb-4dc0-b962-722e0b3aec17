import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const getDatabaseConfig = (
  configService: ConfigService,
): TypeOrmModuleOptions => {
  // Usando as mesmas credenciais do projeto Python, mas com parâmetros separados
  // para evitar problemas com caracteres especiais na URL
  return {
    type: 'postgres',
    host: configService.get<string>('DB_HOST') || '***********',
    port: parseInt(configService.get<string>('DB_PORT') || '5432'),
    username: configService.get<string>('DB_USERNAME') || 'high-capital-dev',
    password: configService.get<string>('DB_PASSWORD') || 'YUli3z5([ZEJ%UOZ',
    database: configService.get<string>('DB_NAME') || 'high-insigts',
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
    synchronize: false, // Não sincronizar automaticamente em produção
    logging: configService.get<string>('DEBUG') === 'True',
    ssl: false, // Configurar conforme necessário para produção
  };
};

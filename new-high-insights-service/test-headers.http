### Arquivo de teste para os novos headers - 3 Cenários
### Use este arquivo com extensões como REST Client no VS Code

### 1. Login para obter token
POST http://localhost:3000/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}

### CENÁRIO 1: Usuário normal - empresa específica (company_id > 0, is-admin = false)
GET http://localhost:3000/leads
Authorization: Bearer {{token}}
company-id: 1
is-admin: false

### CENÁRIO 2: Admin global - todas as empresas (company_id = 0, is-admin = true)
GET http://localhost:3000/leads
Authorization: Bearer {{token}}
company-id: 0
is-admin: true

### CENÁRIO 3: Admin global - empresa específica (company_id > 0, is-admin = true)
GET http://localhost:3000/leads
Authorization: Bearer {{token}}
company-id: 1
is-admin: true

### CENÁRIO 1: Products - usuário normal
GET http://localhost:3000/products
Authorization: Bearer {{token}}
company-id: 1
is-admin: false

### CENÁRIO 2: Products - admin global todas as empresas
GET http://localhost:3000/products
Authorization: Bearer {{token}}
company-id: 0
is-admin: true

### CENÁRIO 3: Products - admin global empresa específica
GET http://localhost:3000/products
Authorization: Bearer {{token}}
company-id: 1
is-admin: true

### CENÁRIO 1: Sales - usuário normal
GET http://localhost:3000/sales
Authorization: Bearer {{token}}
company-id: 1
is-admin: false

### CENÁRIO 2: Sales - admin global todas as empresas
GET http://localhost:3000/sales
Authorization: Bearer {{token}}
company-id: 0
is-admin: true

### CENÁRIO 3: Sales - admin global empresa específica
GET http://localhost:3000/sales
Authorization: Bearer {{token}}
company-id: 1
is-admin: true

### CENÁRIO 1: Dashboard KPIs - usuário normal
GET http://localhost:3000/dashboard/kpis
Authorization: Bearer {{token}}
company-id: 1
is-admin: false

### CENÁRIO 2: Dashboard KPIs - admin global todas as empresas
GET http://localhost:3000/dashboard/kpis
Authorization: Bearer {{token}}
company-id: 0
is-admin: true

### CENÁRIO 3: Dashboard KPIs - admin global empresa específica
GET http://localhost:3000/dashboard/kpis
Authorization: Bearer {{token}}
company-id: 1
is-admin: true

### 10. Teste POST (deve funcionar normalmente com company-id)
POST http://localhost:3000/leads
Authorization: Bearer {{token}}
company-id: 1
is-admin: false
Content-Type: application/json

{
  "name": "Teste Lead",
  "email": "<EMAIL>",
  "phone": "11999999999",
  "source": "website",
  "status": "new"
}

### 11. Teste erro sem header company-id
GET http://localhost:3000/leads
Authorization: Bearer {{token}}
is-admin: false

### 12. Teste erro sem header is-admin
GET http://localhost:3000/leads
Authorization: Bearer {{token}}
company-id: 1

### 13. Teste erro com company-id inválido
GET http://localhost:3000/leads
Authorization: Bearer {{token}}
company-id: abc
is-admin: false

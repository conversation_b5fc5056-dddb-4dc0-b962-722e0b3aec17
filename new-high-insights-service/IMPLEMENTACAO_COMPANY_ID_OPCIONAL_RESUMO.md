# Implementação de company_id Opcional nos Endpoints de Criação e Update

## 📋 Resumo da Implementação

Implementei com sucesso a lógica de **company_id opcional** nos endpoints de criação e update de todos os módulos, seguindo exatamente as regras especificadas:

### 🎯 **Regras Implementadas:**

1. **`company_id > 0` no header**: Usa o company_id do header (ignora o da requisição)
2. **`company_id = 0` no header**: Usa o company_id da requisição (obrigatório)
3. **Retornos**: Todos incluem `company_id` e `company_name`

### ✅ **Módulos Atualizados:**

#### **1. DTOs de Criação e Update**
Todos os DTOs agora incluem o campo opcional `company_id`:

- ✅ **LeadsService** - `LeadCreateDto` e `LeadUpdateDto`
- ✅ **ProductsService** - `ProductCreateDto` e `ProductUpdateDto`
- ✅ **SalesService** - `SaleCreateDto` e `SaleUpdateDto`
- ✅ **GoalsService** - `GoalCreateDto` e `GoalUpdateDto`
- ✅ **FollowupsService** - `FollowupCreateDto` e `FollowupUpdateDto`
- ✅ **PreordersService** - `PreorderCreateDto` e `PreorderUpdateDto`
- ✅ **TeamService** - `TeamMemberCreateDto` e `TeamMemberUpdateDto`

#### **2. DTOs de Response**
Todos os DTOs de response agora incluem:
```typescript
company_id: number;
company_name?: string;
```

#### **3. Services Atualizados**
Todos os services implementam a lógica nos métodos `create` e `update`:

```typescript
async create(dto: CreateDto, headerCompanyId: number): Promise<ResponseDto> {
  // Lógica: se headerCompanyId > 0, usa o do header; se = 0, usa o da requisição
  let finalCompanyId: number;
  
  if (headerCompanyId > 0) {
    finalCompanyId = headerCompanyId;
  } else if (headerCompanyId === 0) {
    if (!dto.company_id) {
      throw new Error('company_id é obrigatório na requisição quando header company-id for 0');
    }
    finalCompanyId = dto.company_id;
  } else {
    throw new Error('company_id do header deve ser >= 0');
  }
  
  // Resto da lógica...
}
```

#### **4. Relations Adicionadas**
Todos os métodos `findAll`, `findOne`, `create` e `update` agora incluem:
```typescript
relations: ['company']
```

#### **5. MapToResponseDto Atualizados**
Todos os métodos de mapeamento agora incluem:
```typescript
company_id: entity.company_id,
company_name: entity.company?.name,
```

### 🔧 **Exemplos de Uso:**

#### **Cenário 1: Usuário Normal (company_id > 0 no header)**
```http
POST /api/leads
Headers:
  company-id: 1
  is-admin: false
Body:
{
  "name": "Lead Teste",
  "email": "<EMAIL>"
  // company_id no body é ignorado
}
```

#### **Cenário 2: Admin Global (company_id = 0 no header)**
```http
POST /api/leads
Headers:
  company-id: 0
  is-admin: true
Body:
{
  "name": "Lead Teste",
  "email": "<EMAIL>",
  "company_id": 5  // OBRIGATÓRIO quando header = 0
}
```

### 📊 **Status Final:**

- ✅ **7 Módulos** completamente atualizados
- ✅ **21 DTOs** modificados (7 create + 7 update + 7 response)
- ✅ **7 Services** com lógica implementada
- ✅ **Compilação** bem-sucedida
- ✅ **Relations** adicionadas para buscar company_name
- ✅ **Validação** de erros implementada

### 🎉 **Resultado:**

A implementação está **completa e funcional**! Todos os endpoints de criação e update agora:

1. **Respeitam a lógica de company_id** baseada nos headers
2. **Validam corretamente** quando company_id é obrigatório na requisição
3. **Retornam company_id e company_name** em todas as responses
4. **Mantêm compatibilidade** com o sistema de 3 cenários existente

A aplicação está pronta para uso em produção com a nova funcionalidade implementada! 🚀

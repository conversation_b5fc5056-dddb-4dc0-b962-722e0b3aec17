# Migração do Sistema de Autenticação - Python para NestJS

Este documento descreve a migração do router de autenticação do FastAPI (Python) para NestJS (TypeScript).

## 🚀 Funcionalidades Migradas

### Endpoint de Login

- **Rota**: `POST /auth/login`
- **Funcionalidade**: Autentica usuário e retorna JWT token
- **Compatibilidade**: Mantém a mesma estrutura de request/response do Python

### Estrutura de Dados

- **Request**: `{ username: string, password: string }`
- **Response**: `{ access_token: string, token_type: "bearer", company: { id: number, name: string } }`

## 📁 Estrutura do Projeto

```
src/
├── config/
│   └── database.config.ts          # Configuração do banco (mesmas credenciais do Python)
├── entities/
│   ├── admin.entity.ts             # Entidade Admin (migrada do SQLAlchemy)
│   └── company.entity.ts           # Entidade Company (migrada do SQLAlchemy)
└── modules/
    └── auth/
        ├── decorators/
        │   └── current-user.decorator.ts    # Decorator para obter usuário atual
        ├── dto/
        │   ├── login-request.dto.ts         # DTO para request de login
        │   └── login-response.dto.ts        # DTO para response de login
        ├── guards/
        │   └── jwt-auth.guard.ts            # Guard para proteger rotas
        ├── strategies/
        │   └── jwt.strategy.ts              # Estratégia JWT do Passport
        ├── auth.controller.ts               # Controller com endpoint /login
        ├── auth.service.ts                  # Serviço com lógica de autenticação
        └── auth.module.ts                   # Módulo de autenticação
```

## 🔧 Configuração

### Variáveis de Ambiente (.env)

```env
# Database Configuration (mesmas credenciais do projeto Python)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=high-capital-dev
DB_PASSWORD=YUli3z5([ZEJ%UOZ
DB_NAME=high-insigts

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Debug Configuration
DEBUG=True
```

### Versão Demo (Sem Banco de Dados)

Para testar a funcionalidade sem configurar o banco de dados, use os endpoints da versão demo que utiliza dados mockados.

### Dependências Instaladas

```bash
npm install @nestjs/typeorm typeorm pg @nestjs/jwt @nestjs/passport passport passport-jwt bcryptjs class-validator class-transformer @nestjs/config
npm install --save-dev @types/pg @types/bcryptjs @types/passport-jwt
```

## 🔐 Como Usar

### Versão Demo (Recomendada para Testes)

#### 1. Ver Informações da API

```bash
curl -X GET http://localhost:3000/auth-demo/info
```

#### 2. Fazer Login (Demo)

```bash
curl -X POST http://localhost:3000/auth-demo/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'
```

#### 3. Usar Token em Rotas Protegidas (Demo)

```bash
curl -X GET http://localhost:3000/auth-demo/profile \
  -H "Authorization: Bearer SEU_JWT_TOKEN"
```

### Versão com Banco de Dados

#### 1. Fazer Login

```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "seu_usuario", "password": "sua_senha"}'
```

#### 2. Usar Token em Rotas Protegidas

```bash
curl -X GET http://localhost:3000/protected/profile \
  -H "Authorization: Bearer SEU_JWT_TOKEN"
```

### 3. Exemplo de Rota Protegida

```typescript
@Controller('api')
export class ApiController {
  @Get('dashboard')
  @UseGuards(JwtAuthGuard)
  getDashboard(@CurrentUser() user: any) {
    return {
      message: 'Dashboard data',
      user: user,
    };
  }
}
```

## 🧪 Testes

Execute os testes:

```bash
npm test -- --testPathPatterns=auth.controller.spec.ts
```

### Resultados dos Testes

```
✓ AuthController should be defined
✓ should return login response on successful authentication
✓ should throw UnauthorizedException on invalid credentials

Test Suites: 1 passed, 1 total
Tests: 3 passed, 3 total
```

### Teste Manual da API Demo

#### 1. Informações da API:

```json
{
  "message": "Sistema de Autenticação NestJS - Migrado do Python",
  "version": "Demo 1.0",
  "endpoints": {
    "login": "POST /auth-demo/login",
    "profile": "GET /auth-demo/profile (protegida)"
  },
  "credentials": {
    "username": "admin",
    "password": "password"
  }
}
```

#### 2. Login bem-sucedido:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "company": {
    "id": 1,
    "name": "Empresa Demo"
  }
}
```

#### 3. Rota protegida com token válido:

```json
{
  "message": "Esta é uma rota protegida! (DEMO MODE)",
  "user": {
    "id": 1,
    "username": "admin",
    "company_id": 1,
    "company": {
      "id": 1,
      "name": "Empresa Demo"
    }
  }
}
```

## 🔄 Compatibilidade com Python

### Funcionalidades Mantidas:

- ✅ Mesma estrutura de banco de dados
- ✅ Mesma lógica de autenticação (bcrypt)
- ✅ Mesmo formato de JWT
- ✅ Mesmas credenciais de banco
- ✅ Debug logging similar
- ✅ Tratamento de erros equivalente

### Melhorias no NestJS:

- 🚀 TypeScript para type safety
- 🛡️ Decorators para validação automática
- 🔒 Guards para proteção de rotas
- 🧪 Testes unitários incluídos
- 📦 Estrutura modular

## 🚀 Executar a Aplicação

```bash
# Desenvolvimento
npm run start:dev

# Produção
npm run build
npm run start:prod
```

A aplicação estará disponível em `http://localhost:3000`

## 📝 Próximos Passos

1. Migrar outros endpoints do Python
2. Implementar middleware de auditoria
3. Adicionar mais testes de integração
4. Configurar Docker para deploy

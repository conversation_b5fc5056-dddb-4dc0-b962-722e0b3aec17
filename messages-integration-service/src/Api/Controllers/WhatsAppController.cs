
using Domain.WhatsApp.DTOs;
using Domain.WhatsApp.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WhatsAppController : ControllerBase
{
    private readonly ILogger<WhatsAppController> _logger;
    private readonly HttpClient _httpClient;

    private readonly IWhatsAppApplicationService _whatsAppApplicationService;
    
    public WhatsAppController(ILogger<WhatsAppController> logger, HttpClient httpClient, IWhatsAppApplicationService whatsAppApplicationService)
    {
        _logger = logger;
        _httpClient = httpClient;
        _whatsAppApplicationService = whatsAppApplicationService;
    }

    /// <summary>
    /// Envia uma mensagem de texto para um número do WhatsApp
    /// </summary>
    /// <param name="instanceName">Nome da instância do WhatsApp</param>
    /// <param name="request"><PERSON><PERSON> da mensagem a ser enviada</param>
    /// <returns>Resultado do envio da mensagem</returns>
    [HttpPost("{instanceName}/send-message")]
    public async Task<IActionResult> SendMessage(
        string instanceName,
        [FromBody] SendMessageRequest request
    )
    {


        _logger.LogInformation(
            "Received request to send message to {Number} via instance {InstanceName}",
            request.Number,
            instanceName
        );
        var res = await _whatsAppApplicationService.SendMessageAsync(
            instanceName,
            new Domain.WhatsApp.DTOs.SendMessageRequest
            {
                Number = request.Number,
                Text = request.Text,
                Delay = request.Delay
            }
        );

        return Ok(res);

    }

    /// <summary>
    /// Obtém o QR Code para conectar uma instância do WhatsApp
    /// </summary>
    /// <param name="instanceName">Nome da instância do WhatsApp</param>
    /// <returns>QR Code em base64</returns>
    [HttpGet("{instanceName}/qrcode")]
    public async Task<IActionResult> GetQrCode(string instanceName)
    {
        _logger.LogInformation(
            "Received request to get QR code for instance {InstanceName}",
            instanceName
        );

        
        var response = await _whatsAppApplicationService.GetQrCodeAsync(instanceName);

        return Ok(response);
    }

    /// <summary>
    /// Consulta o status da conexão de uma instância do WhatsApp
    /// </summary>
    /// <param name="instanceName">Nome da instância do WhatsApp</param>
    /// <returns>Status da conexão</returns>
    [HttpGet("{instanceName}/connection-status")]
    public async Task<IActionResult> GetConnectionStatus(string instanceName)
    {
        _logger.LogInformation(
            "Received request to get connection status for instance {InstanceName}",
            instanceName
        );

        var response = await _whatsAppApplicationService.GetConnectionStatusAsync(instanceName);

        return Ok(response);
    }

}



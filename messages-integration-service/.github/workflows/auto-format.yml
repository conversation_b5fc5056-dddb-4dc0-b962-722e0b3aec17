name: Auto Format Code

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to format (default: current branch)'
        required: false
        default: ''
  issue_comment:
    types: [created]

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  auto-format:
    name: Auto Format Code
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'workflow_dispatch' || 
      (github.event_name == 'issue_comment' && 
       github.event.issue.pull_request && 
       contains(github.event.comment.body, '/format'))
    
    permissions:
      contents: write
      pull-requests: write

    steps:
    - name: Get branch name
      id: branch
      run: |
        if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
          BRANCH="${{ github.event.inputs.branch }}"
          if [ -z "$BRANCH" ]; then
            BRANCH="${{ github.ref_name }}"
          fi
        else
          # Get PR branch from comment
          PR_NUMBER="${{ github.event.issue.number }}"
          BRANCH=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER" \
            | jq -r '.head.ref')
        fi
        echo "branch=$BRANCH" >> $GITHUB_OUTPUT
        echo "Formatting branch: $BRANCH"

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ steps.branch.outputs.branch }}
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore
      env:
        NUGET_USERNAME: ${{ secrets.NUGET_USERNAME }}
        NUGET_TOKEN: ${{ secrets.NUGET_TOKEN }}

    - name: Check current formatting
      id: check-format
      run: |
        if dotnet format --verify-no-changes --verbosity diagnostic; then
          echo "needs-formatting=false" >> $GITHUB_OUTPUT
          echo "✅ Code is already properly formatted"
        else
          echo "needs-formatting=true" >> $GITHUB_OUTPUT
          echo "❌ Code needs formatting"
        fi

    - name: Apply formatting
      if: steps.check-format.outputs.needs-formatting == 'true'
      run: |
        echo "🎨 Applying code formatting..."
        dotnet format --verbosity diagnostic
        
        # Check what changed
        if git diff --quiet; then
          echo "No changes after formatting"
          echo "changes-made=false" >> $GITHUB_OUTPUT
        else
          echo "Formatting changes applied"
          echo "changes-made=true" >> $GITHUB_OUTPUT
          
          # Show what changed
          echo "Files modified:"
          git diff --name-only
        fi

    - name: Commit changes
      if: steps.check-format.outputs.needs-formatting == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        if ! git diff --quiet; then
          git add .
          git commit -m "🎨 Auto-format code with dotnet format

          Applied automatic code formatting to ensure consistency.
          
          - Fixed indentation and spacing
          - Applied .editorconfig rules
          - Standardized code style
          
          This commit was created automatically by GitHub Actions."
          
          git push
          echo "✅ Changes committed and pushed"
        else
          echo "ℹ️ No changes to commit"
        fi

    - name: Comment on PR
      if: github.event_name == 'issue_comment' && steps.check-format.outputs.needs-formatting == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          const needsFormatting = '${{ steps.check-format.outputs.needs-formatting }}' === 'true';
          const changesMade = '${{ steps.apply-format.outputs.changes-made }}' === 'true';
          
          let message = '## 🎨 Auto-Format Results\n\n';
          
          if (needsFormatting) {
            if (changesMade) {
              message += '✅ **Code formatting applied successfully!**\n\n';
              message += 'The code has been automatically formatted according to the project standards.\n';
              message += 'Please review the changes and ensure they look correct.\n\n';
              message += '**What was fixed:**\n';
              message += '- Indentation and spacing\n';
              message += '- Code style consistency\n';
              message += '- .editorconfig rules applied\n';
            } else {
              message += '⚠️ **Formatting was needed but no changes were made.**\n\n';
              message += 'This might indicate an issue with the formatting process.\n';
            }
          } else {
            message += '✅ **Code is already properly formatted!**\n\n';
            message += 'No formatting changes were needed.\n';
          }
          
          message += '\n---\n*Triggered by comment: `/format`*';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: message
          });

    - name: Summary
      if: always()
      run: |
        echo "## 🎨 Auto-Format Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        NEEDS_FORMATTING="${{ steps.check-format.outputs.needs-formatting }}"
        CHANGES_MADE="${{ steps.apply-format.outputs.changes-made }}"
        
        if [ "$NEEDS_FORMATTING" == "true" ]; then
          if [ "$CHANGES_MADE" == "true" ]; then
            echo "✅ **Status**: Formatting applied successfully" >> $GITHUB_STEP_SUMMARY
            echo "📝 **Action**: Code was formatted and committed" >> $GITHUB_STEP_SUMMARY
          else
            echo "⚠️ **Status**: Formatting needed but no changes made" >> $GITHUB_STEP_SUMMARY
            echo "🔍 **Action**: Please check formatting manually" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "✅ **Status**: Code already properly formatted" >> $GITHUB_STEP_SUMMARY
          echo "🎯 **Action**: No changes needed" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Branch**: \`${{ steps.branch.outputs.branch }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Trigger**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY

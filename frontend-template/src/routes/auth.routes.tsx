import TwoFactorAuthPage from '@/pages/2fa-page';
import ForgoutPasswordPage from '@/pages/forgot-password';
import LoginPage from '@/pages/login-page';
import RegisterPage from '@/pages/register-page';
import { Navigate, type RouteObject } from 'react-router-dom';

export const authRoutes: RouteObject[] = [
  {
    path: '',
    element: <Navigate to="login" replace />,
  },
  {
    path: 'login',
    element: <LoginPage />,
  },
  {
    path: 'register',
    element: <RegisterPage />,
  },
  {
    path: 'forgot-password',
    element: <ForgoutPasswordPage />,
  },
  {
    path: '2fa',
    element: <TwoFactorAuthPage />,
  },
  {
    path: '*',
    element: <Navigate to="/404" replace />,
  },
];

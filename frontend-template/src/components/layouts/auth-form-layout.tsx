import type { ReactNode } from 'react';
import { Card } from '../atoms';

interface AuthFormLayoutProps {
  imgUrl?: string;
  children: ReactNode;
  imagePosition?: 'left' | 'right';
}

export function AuthFormLayout({
  imgUrl,
  children,
  imagePosition = 'right',
}: AuthFormLayoutProps) {
  return (
    <Card className="w-auto h-auto bg-white p-5">
      <div
        className={`w-full h-full flex items-stretch flex-row${imagePosition == 'right' ? '' : '-reverse'}`}
      >
        <div className="w-full h-full flex flex-col text-zinc-950 p-5">
          {children}
        </div>
        <div className="w-full">
          <img
            className=" h-full object-none rounded-xl"
            src={
              imgUrl ||
              'https://img.freepik.com/fotos-gratis/equipa-a-trabalhar-em-conjunto-num-projecto_23-2149273739.jpg?semt=ais_hybrid&w=740&q=80'
            }
          />
        </div>
      </div>
    </Card>
  );
}

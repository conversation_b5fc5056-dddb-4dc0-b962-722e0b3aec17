import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, MoreVert<PERSON>, Sun } from "lucide-react";
import logo from "../../assests/logo.png";
import icon from "../../assests/icon.png";
import { createContext, useContext, useState } from "react";
import { Button, DropdownMenu, DropdownMenuContent, DropdownMenuLabel, DropdownMenuTrigger } from "../atoms";
import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuSeparator } from "../atoms/dropdown-menu";
import { useTheme } from "@/hooks/use-theme";

type SidebarProps = {
  children: React.ReactNode;
};
export const SidebarContext = createContext({ expanded: true });

export const Sidebar = ({ children }: SidebarProps) => {
  const [expanded, setExpanded] = useState(true);

  const handleChangeExpanded = () => {
    setExpanded(prev => !prev);
  };

  return (
    <aside className={`h-screen ${expanded ? 'w-70' : 'w-18'}`}>
      <nav className="h-full flex flex-col shadow-sm ">
        <div className="p-4 pb-2 flex justify-between items-center">
          {expanded ? <img src={logo} className={`overflow-hidden  transition-all ${expanded ? 'w-32' : 'w-0'} h-auto`} alt="" /> : <img src={icon} alt="" />}
          <Button variant="outline" className={`${expanded ? '' : 'transition-all w-2 h-2'}`} onClick={handleChangeExpanded}>
            {expanded ? <ChevronFirst /> : <ChevronLast />}
          </Button>
        </div>
        <SidebarContext.Provider value={{ expanded }}>
          <ul className="flex-1 px-3 mt-6">{children}</ul>
        </SidebarContext.Provider>
        <div className="border-t flex p-3">
          <img src="https://plus.unsplash.com/premium_photo-1689708721750-8a0e6dc14cee?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8bWFuJTIwYXZhdGFyfGVufDB8fDB8fHww" className="w-10 h-10 rounded-md object-cover" alt="" />
          <div
            className={`flex justify-between items-center overflow-hidden  transition-all ${expanded ? 'w-52 ml-3' : 'w-0'}`}>
            <div className="leading-4">
              <h4 className="font-semibold">John Doe</h4>
              <p className="text-xs text-gray-600"><EMAIL></p>
            </div>
            <SidebarUserMenu />
          </div>
        </div>
      </nav>
    </aside>
  );
};

type SidebarItemProps = {
  icon: React.ReactNode;
  text: string;
  active?: boolean;
  alert?: boolean;
};

export const SidebarItem = ({ icon, text, active, alert }: SidebarItemProps) => {
  const { expanded } = useContext(SidebarContext);
  return (
    <li className={`relative flex py-3 px-3 my-1
                    font-medium rounded-md cursor-pointer
                    transition-colors group hover:bg-gray-100
                    ${active ? 'bg-gray-100' : ''}`}
    >
      {icon}
      {expanded && <span className={`overflow-hidden transition-all ${expanded ? 'w-52 ml-3' : 'w-0'}`}>{text}</span>}
      {alert && (
        <div className={`absolute right-2 w-2 h-2 rounded bg-red-400 ${expanded ? '' : 'top-2'}`} />
      )}
      {!expanded && (
        <div
          className={`
          absolute left-full rounded-md px-2 py-1 ml-6 text-sm
          invisible opacity-20 -translate-x-3 transition-all
          group-hover:visible group-hover:opacity-100 group-hover:translate-x-0
      `}
        >
          {text}
        </div>
      )}
    </li>
  );
}

export const SidebarUserMenu = () => {
  const { setTheme, theme } = useTheme();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost"><MoreVertical size={20} /></Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 ml-10 mb-[-40px]" align="start">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuGroup>
          <DropdownMenuItem>
            Profile
          </DropdownMenuItem>
          <DropdownMenuItem>
            Billing
          </DropdownMenuItem>
          <DropdownMenuItem>
            Settings
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => setTheme(theme == 'light' ? 'dark' : 'light')}>Theme: {theme == 'light' ? <><Sun />Light</> : <><Moon />Dark</>}</DropdownMenuItem>
        <DropdownMenuItem>
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
import { AuthFormLayout } from '../layouts';
import {
  BodyLoginForm,
  type loginSchemaProps,
} from '../molecules/body-login-form';
import { FooterLoginAuth } from '../molecules/footer-login-form';
import { HeaderFormAuth } from '../molecules/header-login-form';
import { useAuth } from '@/contexts/auth-context';
import { useNavigate } from 'react-router-dom';

export const LoginForm = () => {
  const { login } = useAuth();
  const navigate = useNavigate();

  const onSubmit = async (data: loginSchemaProps) => {
    try {
      await login(data.email, data.password);
      navigate('/app/dashboard');
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <AuthFormLayout
      imgUrl="https://img.freepik.com/fotos-gratis/equipa-a-trabalhar-em-conjunto-num-projecto_23-2149273739.jpg?semt=ais_hybrid&w=740&q=80"
      imagePosition="right"
    >
      <HeaderFormAuth />
      <BodyLoginForm onSubmit={onSubmit} />
      <FooterLoginAuth />
    </AuthFormLayout>
  );
};
